package com.dyd.dbc.domain.wxmini.homeIcon;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("wxmini_home_icon")
public class HomeIcon {

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 标题
     */
    @TableField("title")
    private String title;

    /**
     * icon图片地址
     */
    @TableField("url")
    private String url;

    /**
     * 位置:1-顶部, 2-中部, 3-底部
     */
    @TableField("location")
    private Integer location;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 状态:1-显示, 2-隐藏
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;
}

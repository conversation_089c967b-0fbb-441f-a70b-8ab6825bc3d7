package com.dyd.dbc.aop;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dyd.dbc.domain.JobFailureLog;
import com.dyd.dbc.domain.SysJobDto;
import com.dyd.dbc.mapper.JobFailureLogMapper;
import com.dyd.dbc.mapper.SysJobMapper;
import com.dyd.dingtalk.DingtalkClient;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 定时任务切面
 * <AUTHOR>
 */
@Aspect
@Component
public class JobAop {

    @Autowired
    private JobFailureLogMapper jobFailureLogMapper;

    @Autowired
    private DingtalkClient dingtalkClient;

    @Autowired
    private SysJobMapper sysJobMapper;

    @Pointcut(value = "@annotation(com.dyd.dbc.aop.JobAnnotation)")
    private void jobAnnation(){

    }

    @AfterReturning("jobAnnation()")
    public void jobAopAfter(JoinPoint point){
        Signature signature = point.getSignature();
        JobFailureLog jobFailureLog = new JobFailureLog();
        jobFailureLog.setJobName(signature.getName());
        jobFailureLog.setStatus(1);
        jobFailureLogMapper.insert(jobFailureLog);

    }

    @AfterThrowing(pointcut = "jobAnnation()",throwing = "e")
    public void jobAopThrow(JoinPoint joinPoint,Throwable e){
        Signature signature = joinPoint.getSignature();
        JobFailureLog jobFailureLog = new JobFailureLog();
        jobFailureLog.setJobName(signature.getName());
        jobFailureLog.setStatus(0);
        jobFailureLog.setFailureMsg(e.getMessage());
        jobFailureLogMapper.insert(jobFailureLog);

        List<SysJobDto> sysJobDtos = sysJobMapper.selectList(Wrappers.<SysJobDto>lambdaQuery().like(SysJobDto::getInvokeTarget, signature.getName()));
        String name = "";
        if(CollectionUtil.isNotEmpty(sysJobDtos)){
            name = sysJobDtos.get(0).getJobName();
        }

        dingtalkClient.sendMarkDomn(Arrays.asList("16853222162771202"),"任务名:"+name+"接口名:"+signature.getName()+"报错","任务名:"+name+"接口名:"+signature.getName()+"定时报错:"+e.getMessage());

    }
}

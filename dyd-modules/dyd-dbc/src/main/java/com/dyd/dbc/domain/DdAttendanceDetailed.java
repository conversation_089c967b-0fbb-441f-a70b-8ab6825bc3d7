package com.dyd.dbc.domain;

import com.dyd.common.core.annotation.Excel;
import com.dyd.common.core.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import java.util.Date;

/**
 * 钉钉打卡详细对象 dd_attendance_detailed
 *
 * <AUTHOR>
 * @date 2023-04-13
 */
public class DdAttendanceDetailed extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 打卡ID
     */
    private Long attendanceid;

    /**
     * 姓名
     */
    private String name;

    /**
     * 打卡人的钉钉userId
     */
    @Excel(name = "打卡人的钉钉userId")
    private String userId;

    /**
     * 用户打卡纬度
     */
    @Excel(name = "用户打卡纬度")
    private String userLatitude;

    /**
     * 用户打卡经度
     */
    @Excel(name = "用户打卡经度")
    private String userLongitude;

    /**
     * 用户打卡地址
     */
    @Excel(name = "用户打卡地址")
    private String userAddress;

    /**
     * 实际打卡时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "实际打卡时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date userCheckTime;

    /**
     * 实际打卡时间
     */
    private String selectTime;

    /**
     * 打卡结果
     */
    @Excel(name = "打卡结果")
    private String timeResult;

    /**
     * 位置结果
     */
    @Excel(name = "位置结果")
    private String locationResult;

    /**
     * 打卡类型
     */
    @Excel(name = "打卡类型")
    private String checkType;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    private String longitude;

    private String latitude;

    public String getName() {
        return name;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setAttendanceid(Long attendanceid) {
        this.attendanceid = attendanceid;
    }

    public Long getAttendanceid() {
        return attendanceid;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserLatitude(String userLatitude) {
        this.userLatitude = userLatitude;
    }

    public String getUserLatitude() {
        return userLatitude;
    }

    public void setUserLongitude(String userLongitude) {
        this.userLongitude = userLongitude;
    }

    public String getUserLongitude() {
        return userLongitude;
    }

    public void setUserAddress(String userAddress) {
        this.userAddress = userAddress;
    }

    public String getSelectTime() {
        return selectTime;
    }

    public void setSelectTime(String selectTime) {
        this.selectTime = selectTime;
    }

    public String getUserAddress() {
        return userAddress;
    }

    public void setUserCheckTime(Date userCheckTime) {
        this.userCheckTime = userCheckTime;
    }

    public Date getUserCheckTime() {
        return userCheckTime;
    }

    public void setTimeResult(String timeResult) {
        this.timeResult = timeResult;
    }

    public String getTimeResult() {
        return timeResult;
    }

    public void setLocationResult(String locationResult) {
        this.locationResult = locationResult;
    }


    public String getLocationResult() {
        return locationResult;
    }

    public void setCheckType(String checkType) {
        this.checkType = checkType;
    }

    public String getCheckType() {
        return checkType;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getDelFlag() {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("attendanceid", getAttendanceid())
                .append("userId", getUserId())
                .append("userLatitude", getUserLatitude())
                .append("userLongitude", getUserLongitude())
                .append("userAddress", getUserAddress())
                .append("userCheckTime", getUserCheckTime())
                .append("timeResult", getTimeResult())
                .append("locationResult", getLocationResult())
                .append("checkType", getCheckType())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}

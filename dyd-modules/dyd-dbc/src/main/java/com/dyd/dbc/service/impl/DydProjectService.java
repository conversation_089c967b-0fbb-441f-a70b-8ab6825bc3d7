package com.dyd.dbc.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dyd.common.core.constant.WxConstants;
import com.dyd.common.core.utils.FileUtil;
import com.dyd.common.core.utils.PageHelp;
import com.dyd.common.core.utils.PageWrapper;
import com.dyd.dbc.conver.DbcBeanConver;
import com.dyd.dbc.domain.*;
import com.dyd.dbc.dto.request.*;
import com.dyd.dbc.dto.response.DydProjectResponse;
import com.dyd.dbc.dto.response.DydWxTechnicalSupportListResponse;
import com.dyd.dbc.dto.response.WxSparePartResponse;
import com.dyd.dbc.mapper.*;
import com.dyd.jdy.bean.jdy.JdyCommonDto;
import com.dyd.jdy.bean.jdy.request.JdyContractManagerRequest;
import com.dyd.jdy.bean.jdy.request.JdyElectricalDesignRequest;
import com.dyd.jdy.bean.jdy.request.JdyMechanicalDesignWorkRequest;
import com.dyd.jdy.bean.jdy.request.JdyProductReportRequest;
import com.dyd.jdy.bean.jdy.response.*;
import com.dyd.jdy.constant.JdyConstants;
import com.dyd.jdy.constant.JdyProjectStage;
import com.dyd.jdy.service.JdyCommonService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 后台项目
 */
@Slf4j
@Service
public class DydProjectService {

    @Autowired
    private DydProjectMapper dydProjectMapper;

    @Autowired
    private DbcBeanConver dbcBeanConver;

    @Autowired
    private JdyCommonService jdyCommonService;

    @Autowired
    private DydWxTechnicalSupportMapper dydWxTechnicalSupportMapper;

    @Autowired
    private DydProjectInformationMapper dydProjectInformationMapper;

    @Autowired
    private WxDydSparePartMapper wxDydSparePartMapper;

    @Autowired
    private ProjectShipInfoMapper projectShipInfoMapper;

    /**
     * 项目列表
     * @param request
     * @return
     */
     public PageWrapper<List<DydProjectResponse>> getDydProjectList(DydProjectRequest request){

         List<DydProjectResponse> dydProjectResponseList = new ArrayList<>();
         Page<Object> page = PageHelper.startPage(request.getPageNum(), request.getPageSize());
         List<DydProject> dydProjects = dydProjectMapper.selectList(Wrappers.<DydProject>lambdaQuery().eq(StringUtils.isNotEmpty(request.getProjectCode()), DydProject::getProjectCode, request.getProjectCode())
                                               .eq(StringUtils.isNotEmpty(request.getCustomerName()),DydProject::getCustomerName,request.getCustomerName())
                                               .eq(StringUtils.isNotEmpty(request.getOwner()),DydProject::getOwner,request.getOwner())
                                               .eq(Objects.nonNull(request.getProjectStatus()),DydProject::getProjectStatus,request.getProjectStatus()));
         if(CollectionUtil.isNotEmpty(dydProjects)){

             dydProjectResponseList = dydProjects.stream().map(dydProject -> {
                 DydProjectResponse dydProjectResponse = dbcBeanConver.conerToResponse(dydProject);
                 int projectStatus = dydProjectResponse.getProjectStatus();
                 if (projectStatus == 1) {
                     dydProjectResponse.setProjectStatusDesc("进行中");
                 } else if (projectStatus == 2) {
                     dydProjectResponse.setProjectStatusDesc("已完成");
                 }
                 int projectStage = dydProjectResponse.getProjectStage();
                 dydProjectResponse.setProjectStageDesc(JdyProjectStage.ProjectStageEnum.getValueByKey(String.valueOf(projectStage)));
                 return dydProjectResponse;
             }).collect(Collectors.toList());
         }

         return PageHelp.render(page,dydProjectResponseList);
     }

    /**
     * 查看项目详情
     * @param id
     * @return
     */
     public DydProjectResponse getDydProjectInfo(Integer id){

         DydProject dydProject = dydProjectMapper.selectById(id);
         DydProjectResponse dydProjectResponse = dbcBeanConver.conerToResponse(dydProject);
         int projectStatus = dydProjectResponse.getProjectStatus();
         if (projectStatus == 1) {
             dydProjectResponse.setProjectStatusDesc("进行中");
         } else if (projectStatus == 2) {
             dydProjectResponse.setProjectStatusDesc("已完成");
         }
         int projectStage = dydProjectResponse.getProjectStage();
         dydProjectResponse.setProjectStageDesc(JdyProjectStage.ProjectStageEnum.getValueByKey(String.valueOf(projectStage)));

         //项目资料
         List<DydProjectInformation> dydProjectInformations = dydProjectInformationMapper.selectList(Wrappers.<DydProjectInformation>lambdaQuery().eq(DydProjectInformation::getProjectCode, dydProjectResponse.getProjectCode()).eq(DydProjectInformation::getType, 7));
         if(CollectionUtil.isNotEmpty(dydProjectInformations)) {
             dydProjectResponse.setProjectUrl(dydProjectInformations.get(0).getUrl());
         }

         //发货信息
         List<ProjectShipInfo> projectShipInfos = projectShipInfoMapper.selectList(Wrappers.<ProjectShipInfo>lambdaQuery().eq(ProjectShipInfo::getProjectCode, dydProjectResponse.getProjectCode()));
         if(CollectionUtil.isNotEmpty(projectShipInfos)){
             ProjectShipInfo projectShipInfo = projectShipInfos.get(0);
             DydProjectResponse.ShipInfo shipInfo = new DydProjectResponse.ShipInfo();
             shipInfo.setId(projectShipInfo.getId());
             shipInfo.setDriverName(projectShipInfo.getDriverName());
             shipInfo.setLicensePlate(projectShipInfo.getLicensePlate());
             shipInfo.setPhone(projectShipInfo.getPhone());
             shipInfo.setProjectCode(projectShipInfo.getProjectCode());
             //现场图片
             String pictureUrl = projectShipInfo.getPictureUrl();
             if(StringUtils.isNotEmpty(pictureUrl)){
                 shipInfo.setPictureUrl(Arrays.asList(pictureUrl.split(";")));
             }
             dydProjectResponse.setShipInfo(shipInfo);
         }
         return dydProjectResponse;
     }

    /**
     * 编辑项目
     * @return
     */
     public void updateDydProjectInfo(DydProjectUpdateRequest request){
         Long count = dydProjectInformationMapper.selectCount(Wrappers.<DydProjectInformation>lambdaQuery().eq(DydProjectInformation::getProjectCode, request.getProjectCode()).eq(DydProjectInformation::getType, 7));
         DydProjectInformation dydProjectInformation = new DydProjectInformation();
         dydProjectInformation.setUrl(request.getProjectUrl());
         if(count != 0) {
             dydProjectInformationMapper.update(dydProjectInformation, Wrappers.<DydProjectInformation>lambdaUpdate().eq(DydProjectInformation::getProjectCode, request.getProjectCode()).eq(DydProjectInformation::getType, 7));
         }else{
             dydProjectInformation.setProjectCode(request.getProjectCode());
             dydProjectInformation.setType(7);
             dydProjectInformationMapper.insert(dydProjectInformation);
         }
         DydProjectResponse.ShipInfo shipInfo = request.getShipInfo();
         ProjectShipInfo projectShipInfo = new ProjectShipInfo();
         projectShipInfo.setId(shipInfo.getId());
         projectShipInfo.setDriverName(shipInfo.getDriverName());
         projectShipInfo.setLicensePlate(shipInfo.getLicensePlate());
         projectShipInfo.setPhone(shipInfo.getPhone());
         projectShipInfo.setProjectCode(shipInfo.getProjectCode());

         StringBuilder stringBuilder = new StringBuilder();
         List<String> pictureUrls = shipInfo.getPictureUrl();
         if(CollectionUtil.isNotEmpty(pictureUrls)){
             for(String pictureUrl:pictureUrls){
                 stringBuilder.append(pictureUrl).append(";");
             }
         }
         projectShipInfo.setPictureUrl(stringBuilder.toString());
         if(Objects.nonNull(shipInfo) && Objects.nonNull(shipInfo.getId())) {
             projectShipInfoMapper.updateById(projectShipInfo);
         }else{
             projectShipInfoMapper.insert(projectShipInfo);
         }
     }

    /**
     * 台系统获取服务支持列表
     * @param request
     * @return
     */
     public PageWrapper<List<DydWxTechnicalSupportListResponse>> getWxTechnicalSupportList(DydWxTechnicalSupportListRequest request){

         //返回结果
         List<DydWxTechnicalSupportListResponse> responseList = new ArrayList<>();

         Page<Object> page = PageHelper.startPage(request.getPageNum(), request.getPageSize());
         List<DydWxTechnicalSupport> dydWxTechnicalSupports = dydWxTechnicalSupportMapper.selectList(Wrappers.<DydWxTechnicalSupport>lambdaQuery().eq(StringUtils.isNotEmpty(request.getPhone()), DydWxTechnicalSupport::getPhone, request.getPhone())
                 .eq(StringUtils.isNotEmpty(request.getName()), DydWxTechnicalSupport::getName, request.getName()).eq(StringUtils.isNotEmpty(request.getTitle()), DydWxTechnicalSupport::getTitle, request.getTitle()).eq(DydWxTechnicalSupport::getDeleted,0));
         if(CollectionUtil.isNotEmpty(dydWxTechnicalSupports)){
             for(DydWxTechnicalSupport dydWxTechnicalSupport : dydWxTechnicalSupports){
                 DydWxTechnicalSupportListResponse dydWxTechnicalSupportListResponse = dbcBeanConver.coverToDydWxTechnicalSupportListResponse(dydWxTechnicalSupport);
                 responseList.add(dydWxTechnicalSupportListResponse);
             }
         }
         return PageHelp.render(page,responseList);
     }

    /**
     * 同步04-4项目
     */
    @Transactional
    public void sysnProject(){

        List<JdyProjectManegerResponse.JdyProjectManegerData> dataList = new ArrayList<>();
        jdyCommonService.queryJdyProjectManegerIter(dataList,null,null,null,null,100);
        if(CollectionUtil.isNotEmpty(dataList)){

            List<String> projectCodes = dataList.stream().map(JdyProjectManegerResponse.JdyProjectManegerData::get_widget_1612315117670).collect(Collectors.toList());

            //查询04-3合同管理
            List<JdyContractManagerResponse.JdyContractManagerData> jdyContractManagerDataList = new ArrayList<>();
            JdyContractManagerRequest.Field contractManagerField = new JdyContractManagerRequest.Field();
            contractManagerField.setField("_widget_1612346722533");
            contractManagerField.setValue(projectCodes);
            contractManagerField.setMethod(JdyConstants.JDY_EQ);
            jdyCommonService.queryJdyContractManager(jdyContractManagerDataList,null,null,JdyConstants.JDY_AND,Arrays.asList(contractManagerField),100);

            Map<String, List<JdyContractManagerResponse.JdyContractManagerData>> contractManagerMap = new HashMap<>();
            if(CollectionUtil.isNotEmpty(jdyContractManagerDataList)){
                contractManagerMap = jdyContractManagerDataList.stream().collect(Collectors.groupingBy(JdyContractManagerResponse.JdyContractManagerData::get_widget_1612346722533));
                //商务合同号
               // myProjectInfoResponse.setContractCode(jdyContractManagerDataList.get(0).get_widget_1675729553648());
            }

            List<DydProject> dydProjects = new ArrayList<>();
            for(JdyProjectManegerResponse.JdyProjectManegerData jdyProjectManegerData:dataList){
                DydProject dydProject = new DydProject();
                dydProject.setProjectCode(jdyProjectManegerData.get_widget_1612315117670());
                dydProject.setDataId(jdyProjectManegerData.get_id());
                dydProject.setCustomerName(jdyProjectManegerData.get_widget_1612856198080());
                dydProject.setCustomerCode(jdyProjectManegerData.get_widget_1612849867582());
                dydProject.setCreateTime(LocalDateTime.now());
                dydProject.setCusProCode(jdyProjectManegerData.get_widget_1626595902758());
                if(CollectionUtil.isNotEmpty(contractManagerMap) && StringUtils.isNotEmpty(jdyProjectManegerData.get_widget_1612315117670())){
                    List<JdyContractManagerResponse.JdyContractManagerData> jdyContractManagerDataListT = contractManagerMap.get(jdyProjectManegerData.get_widget_1612315117670());
                    if(CollectionUtil.isNotEmpty(jdyContractManagerDataListT)){
                        dydProject.setContractCode(jdyContractManagerDataListT.get(0).get_widget_1675729553648());
                    }
                }

                //所属销售员
                JdyCommonDto.User widget_1612857518891 = jdyProjectManegerData.get_widget_1612857518891();
                if(Objects.nonNull(widget_1612857518891)){
                    dydProject.setSaleHead(widget_1612857518891.getName());
                }
                //项目管理
                JdyCommonDto.User widget_1612857518940 = jdyProjectManegerData.get_widget_1612857518940();
                if(Objects.nonNull(widget_1612857518940)){
                    dydProject.setProjectHead(widget_1612857518940.getName());
                }
                //项目小组-技术（机械）
                List<JdyCommonDto.User> widget_1680508510469 = jdyProjectManegerData.get_widget_1680508510469();
                if(CollectionUtil.isNotEmpty(widget_1680508510469)){
                    dydProject.setMechineDesigner(widget_1680508510469.get(0).getName());
                }
                //项目小组-技术（电气）
                List<JdyCommonDto.User> widget_1682229316709 = jdyProjectManegerData.get_widget_1682229316709();
                if(CollectionUtil.isNotEmpty(widget_1682229316709)){
                    dydProject.setApplicationEngineer(widget_1682229316709.get(0).getName());
                }

                //安装调试负责人
                List<JdyCommonDto.User> widget_1680835199903 = jdyProjectManegerData.get_widget_1680835199903();
                if(CollectionUtil.isNotEmpty(widget_1680835199903)){
                    dydProject.setAfterSaleHead(widget_1680835199903.get(0).getName());
                }


                JdyProjectStage.ProjectStageEnum projectStageEnum = jdyCommonService.getProjectStage(jdyProjectManegerData);
                if(Objects.nonNull(projectStageEnum)){
                    dydProject.setProjectStage(Integer.valueOf(projectStageEnum.getKey()));
                    if(JdyProjectStage.ProjectStageEnum.XIANGMU_YIWANCHENG.getKey().equals(projectStageEnum.getKey())){
                        dydProject.setProjectStatus(2);
                    }else{
                        dydProject.setProjectStatus(1);
                    }
                }
                dydProjects.add(dydProject);
            }

            //通过项目号去重
            dydProjects = dydProjects.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(DydProject::getProjectCode))), ArrayList::new));

            //已存在项目
            List<DydProject> dydExistProjects = dydProjectMapper.selectList(null);
            //如果不存在则直接保存
            if(CollectionUtil.isEmpty(dydExistProjects)){
                dydProjectMapper.insertBatchSomeColumn(dydProjects);
            }else{
                //已存在项目号
                List<String> existprojectCodes = dydExistProjects.stream().map(DydProject::getProjectCode).collect(Collectors.toList());

                //已存在数据用于更新
                List<DydProject> existProjectDatas = dydProjects.stream().filter(dydProject -> existprojectCodes.contains(dydProject.getProjectCode())).collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(existProjectDatas)){
                    for(DydProject dydProject:existProjectDatas) {
                        dydProjectMapper.update(dydProject,Wrappers.<DydProject>lambdaUpdate().eq(DydProject::getProjectCode,dydProject.getProjectCode()));
                    }
                }

                //不存在的数据，用于新增
                List<DydProject> addProjectDatas = dydProjects.stream().filter(dydProject -> !existprojectCodes.contains(dydProject.getProjectCode())).collect(Collectors.toList());
                if(CollectionUtil.isNotEmpty(addProjectDatas)){
                    dydProjectMapper.insertBatchSomeColumn(addProjectDatas);
                }

            }
            //查询技术资料
            try{
                if(CollectionUtil.isNotEmpty(projectCodes)){
                    //MD1.1机械设计工作返回参数
                    List<JdyMechanicalDesignWorkResponse.MechanicalDesignWorkData> mechanicalDesignWorkDataList = new ArrayList<>();
                    JdyMechanicalDesignWorkRequest.Field field = new JdyMechanicalDesignWorkRequest.Field();
                    field.setField("_widget_1607412530740");
                    field.setValue(projectCodes);
                    field.setMethod("in");
                    jdyCommonService.queryJdyMechanicalDesignWork(mechanicalDesignWorkDataList,null,null,JdyConstants.AND,Arrays.asList(field),500);
                    if(CollectionUtil.isNotEmpty(mechanicalDesignWorkDataList)){

                        for(JdyMechanicalDesignWorkResponse.MechanicalDesignWorkData mechanicalDesignWorkData:mechanicalDesignWorkDataList){

                            //项目手册
                            List<JdyMechanicalDesignWorkResponse.ProjectDesc> projectDescList = mechanicalDesignWorkData.get_widget_1612161738247();
                            if(CollectionUtil.isNotEmpty(projectDescList)){
                                for(JdyMechanicalDesignWorkResponse.ProjectDesc projectDesc : projectDescList){
                                    DydProjectInformation dydProjectInformation = new DydProjectInformation();
                                    dydProjectInformation.setProjectCode(mechanicalDesignWorkData.get_widget_1607412530740());
                                    //拉取文件
                                    String fileUrl = FileUtil.downLoadFromUrl(projectDesc.getUrl(), projectDesc.getName(), WxConstants.path);
                                    dydProjectInformation.setUrl(WxConstants.pathOut+fileUrl);
                                    dydProjectInformation.setType(1);
                                    dydProjectInformationMapper.insert(dydProjectInformation);
                                }
                            }
                        }

                    }

                    //DQ01-电气设计工作
                    List<JdyElectricalDesignResponse.JdyElectricalDesignData> jdyElectricalDesignDataList = new ArrayList<>();
                    JdyElectricalDesignRequest.Field electricalDesignField = new JdyElectricalDesignRequest.Field();
                    electricalDesignField.setField("_widget_1607412530740");
                    electricalDesignField.setValue(projectCodes);
                    electricalDesignField.setMethod("in");
                    jdyCommonService.queryJdyElectricalDesign(jdyElectricalDesignDataList,null,null,JdyConstants.AND,Arrays.asList(electricalDesignField),500);
                    if(CollectionUtil.isNotEmpty(jdyElectricalDesignDataList)){
                        for(JdyElectricalDesignResponse.JdyElectricalDesignData jdyElectricalDesignData:jdyElectricalDesignDataList){
                            //生产图文件
                            List<JdyElectricalDesignResponse.ProductionDiagramDesc> productionDiagramDescList = jdyElectricalDesignData.get_widget_1610953283478();
                            if(CollectionUtil.isNotEmpty(productionDiagramDescList)){
                                for(JdyElectricalDesignResponse.ProductionDiagramDesc productionDiagramDesc:productionDiagramDescList){
                                    DydProjectInformation dydProjectInformation = new DydProjectInformation();
                                    dydProjectInformation.setProjectCode(jdyElectricalDesignData.get_widget_1607412530740());
                                    dydProjectInformation.setType(2);
                                    //拉取文件
                                    String fileUrl = FileUtil.downLoadFromUrl(productionDiagramDesc.getUrl(), productionDiagramDesc.getName(), WxConstants.path);
                                    dydProjectInformation.setUrl(WxConstants.pathOut+fileUrl);
                                    dydProjectInformationMapper.insert(dydProjectInformation);
                                }
                            }
                        }
                    }

                    //3.0成品检验报告审批
                    List<JdyProductReportResponse.JdyProductReportData> jdyProductReportDataList = new ArrayList<>();
                    JdyProductReportRequest.Field reportField = new JdyProductReportRequest.Field();
                    reportField.setField("_widget_1626411047070");
                    reportField.setValue(projectCodes);
                    reportField.setMethod("in");
                    jdyCommonService.queryJdyProductReport(jdyProductReportDataList,null,null,JdyConstants.AND,Arrays.asList(reportField),500);
                    if(CollectionUtil.isNotEmpty(jdyProductReportDataList)){
                        for(JdyProductReportResponse.JdyProductReportData jdyProductReportData:jdyProductReportDataList){
                            List<JdyProductReportResponse.ProductReportDesc> productReportDescList = jdyProductReportData.get_widget_1639377261861();
                            if(CollectionUtil.isNotEmpty(productReportDescList)){
                                for(JdyProductReportResponse.ProductReportDesc productReportDesc:productReportDescList){
                                    DydProjectInformation dydProjectInformation = new DydProjectInformation();
                                    dydProjectInformation.setProjectCode(jdyProductReportData.get_widget_1626411047070());
                                    dydProjectInformation.setType(3);
                                    String fileUrl = FileUtil.downLoadFromUrl(productReportDesc.getUrl(), productReportDesc.getName(), WxConstants.path);
                                    dydProjectInformation.setUrl(WxConstants.pathOut+fileUrl);
                                    dydProjectInformationMapper.insert(dydProjectInformation);
                                }
                            }
                        }
                    }
                }

            }catch (Exception e){
                log.error("拉取项目资料报错{}",e.getMessage());
            }
        }
    }


    /**
     * 新增服务支持
     * @param request
     */
    public void editWxTechnicalSupport(DydWxTechnicalSupportAddRequest request){

        DydWxTechnicalSupport dydWxTechnicalSupport = new DydWxTechnicalSupport();
        dydWxTechnicalSupport.setName(request.getName());
        dydWxTechnicalSupport.setPhone(request.getPhone());
        dydWxTechnicalSupport.setTitle(request.getTitle());
        dydWxTechnicalSupport.setCompanyName(request.getCompanyName());
        dydWxTechnicalSupport.setPrCode(request.getPrCodeUrl());
        dydWxTechnicalSupport.setIsDisplay(request.getIsDisplay());
        dydWxTechnicalSupport.setIndustry(request.getIndustry());
        dydWxTechnicalSupport.setHeadPic(request.getHeadPicUrl());
        dydWxTechnicalSupport.setId(request.getId());
        dydWxTechnicalSupportMapper.updateById(dydWxTechnicalSupport);
    }

    public void addWxTechnicalSupport(DydWxTechnicalSupportAddRequest request){

        DydWxTechnicalSupport dydWxTechnicalSupport = new DydWxTechnicalSupport();
        dydWxTechnicalSupport.setName(request.getName());
        dydWxTechnicalSupport.setPhone(request.getPhone());
        dydWxTechnicalSupport.setTitle(request.getTitle());
        dydWxTechnicalSupport.setCompanyName(request.getCompanyName());
        dydWxTechnicalSupport.setPrCode(request.getPrCodeUrl());
        dydWxTechnicalSupport.setIsDisplay(request.getIsDisplay());
        dydWxTechnicalSupport.setIndustry(request.getIndustry());
        dydWxTechnicalSupport.setHeadPic(request.getHeadPicUrl());
        dydWxTechnicalSupportMapper.insert(dydWxTechnicalSupport);
    }

    /**
     * 服务支持详情
     * @param id
     * @return
     */
    public DydWxTechnicalSupportListResponse getWxTechnicalSupportInfo(Integer id){
        DydWxTechnicalSupport dydWxTechnicalSupport = dydWxTechnicalSupportMapper.selectById(id);
        if(Objects.nonNull(dydWxTechnicalSupport)){
            return dbcBeanConver.coverToDydWxTechnicalSupportListResponse(dydWxTechnicalSupport);
        }
        return new DydWxTechnicalSupportListResponse();
    }

    /**
     * 服务支持删除
     * @param id
     */
    public void wxTechnicalSupportDelete(Integer id){
        DydWxTechnicalSupport dydWxTechnicalSupport = new DydWxTechnicalSupport();
        dydWxTechnicalSupport.setDeleted(1);
        dydWxTechnicalSupport.setId(id);
        dydWxTechnicalSupportMapper.updateById(dydWxTechnicalSupport);
    }

    /**
     * 查询备件列表
     * @param request
     * @return
     */
    public PageWrapper<List<WxSparePartResponse>> getWxSparePartList(WxSparePartRequest request){

        Page<Object> page = PageHelper.startPage(request.getPageNum(), request.getPageSize());
        List<WxDydSparePart> wxDydSpareParts = wxDydSparePartMapper.selectList(Wrappers.<WxDydSparePart>lambdaQuery().eq(StringUtils.isNotEmpty(request.getProCode()), WxDydSparePart::getProCode, request.getProCode()).eq(WxDydSparePart::getDeleted, 0));
        List<WxSparePartResponse> collect = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(wxDydSpareParts)){
            collect = wxDydSpareParts.stream().map(wxDydSparePart -> {
                WxSparePartResponse wxSparePartResponse = dbcBeanConver.coverToWxSparePartResponse(wxDydSparePart);
                return wxSparePartResponse;
            }).collect(Collectors.toList());

        }
        return PageHelp.render(page,collect);
    }

    /**
     * 查询备件详情
     * @param id
     * @return
     */
    public WxSparePartResponse getWxSparePartDetail(Integer id){
        WxDydSparePart wxDydSparePart = wxDydSparePartMapper.selectById(id);
        return dbcBeanConver.coverToWxSparePartResponse(wxDydSparePart);
    }

    /**
     * 新增备件信息
     * @param request
     */
    public void addWxSparePart(WxSparePartAddRequest request){
        WxDydSparePart wxDydSparePart = new WxDydSparePart();
        wxDydSparePart.setProCode(request.getProCode());
        wxDydSparePart.setUrl(request.getUrl());
        wxDydSparePart.setRemark(request.getRemark());
        wxDydSparePart.setName(request.getName());
        wxDydSparePart.setContactName(request.getContactName());
        wxDydSparePart.setPhone(request.getPhone());
        wxDydSparePartMapper.insert(wxDydSparePart);
    }

    /**
     * 编辑备注信息
     * @param request
     */
    public void editWxSparePart(WxSparePartEditRequest request){
        WxDydSparePart wxDydSparePart = new WxDydSparePart();
        wxDydSparePart.setProCode(request.getProCode());
        wxDydSparePart.setRemark(request.getRemark());
        wxDydSparePart.setUrl(request.getUrl());
        wxDydSparePart.setUpdateTime(LocalDateTime.now());
        wxDydSparePart.setId(request.getId());
        wxDydSparePart.setName(request.getName());
        wxDydSparePart.setContactName(request.getContactName());
        wxDydSparePart.setPhone(request.getPhone());
        wxDydSparePartMapper.updateById(wxDydSparePart);
    }

    /**
     *删除备件信息
     * @param id
     */
    public void deleteWxSparePart(Integer id){
        WxDydSparePart wxDydSparePart = new WxDydSparePart();
        wxDydSparePart.setDeleted(1);
        wxDydSparePart.setId(id);
        wxDydSparePartMapper.updateById(wxDydSparePart);
    }
}

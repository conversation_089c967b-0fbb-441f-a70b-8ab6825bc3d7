package com.dyd.dbc.service;

import com.dyd.dbc.domain.DbcJdyAfterSalesMember;

import java.util.List;

/**
 * 简道云售后人员档案Service接口
 *
 * <AUTHOR>
 * @date 2023-04-20
 */
public interface IDbcJdyAfterSalesMemberService {
    /**
     * 查询简道云售后人员档案
     *
     * @param id 简道云售后人员档案主键
     * @return 简道云售后人员档案
     */
    public DbcJdyAfterSalesMember selectDbcJdyAfterSalesMemberById(String id,String destination);

    /**
     * 查询简道云售后人员档案列表
     *
     * @param dbcJdyAfterSalesMember 简道云售后人员档案
     * @return 简道云售后人员档案集合
     */
    public List<DbcJdyAfterSalesMember> selectDbcJdyAfterSalesMemberList(DbcJdyAfterSalesMember dbcJdyAfterSalesMember);

    /**
     * 新增简道云售后人员档案
     *
     * @param dbcJdyAfterSalesMember 简道云售后人员档案
     * @return 结果
     */
    public int insertDbcJdyAfterSalesMember(DbcJdyAfterSalesMember dbcJdyAfterSalesMember);

    /**
     * 修改简道云售后人员档案
     *
     * @param dbcJdyAfterSalesMember 简道云售后人员档案
     * @return 结果
     */
    public int updateDbcJdyAfterSalesMember(DbcJdyAfterSalesMember dbcJdyAfterSalesMember);

    /**
     * 批量删除简道云售后人员档案
     *
     * @param ids 需要删除的简道云售后人员档案主键集合
     * @return 结果
     */
    public int deleteDbcJdyAfterSalesMemberByIds(String[] ids);

    /**
     * 删除简道云售后人员档案信息
     *
     * @param id 简道云售后人员档案主键
     * @return 结果
     */
    public int deleteDbcJdyAfterSalesMemberById(String id);

    /**
     * 同步售后人员档案
     */
    public void synchAfterSalesMember();
}

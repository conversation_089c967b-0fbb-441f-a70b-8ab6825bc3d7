package com.dyd.dbc.service;

import com.dyd.dbc.domain.DdAddUserDto;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 钉钉数据同步
 * <AUTHOR>
 */
@Service
public interface DingtalkService {
    /**
     * 同步钉钉用户
     */
    void synchDingtalkUser();

    /**
     * 同步预计算时长
     */
    void synchDingDurationCalculate();

    /**
     * 同步用户打卡详情
     */
    void synchAttendanceDetailed();

    /**
     * 发送迟到未打卡人员到群
     */
    void sendingAbnormalPersonnel();

    /**
     * 同步钉钉部门和用户
     * type 0:默认 1：新增  2：离职
     */
    void synchDeptAndUser(List<String> userIds,Integer type);

    /**
     * 新增钉钉用户
     * @param ddAddUserDto
     */
    void addDdUser(DdAddUserDto ddAddUserDto);

    /**
     * 离职用户
     * @param userIds
     */
    void leaveUser(List<String> userIds);

    /**
     * 获取请假状态
     */
    void getLeaveStatus(String dateStr);

    /**
     * 同步售后人员档案
     */
    void synchAfterSalesMember();

    void syncUserInfoJdy();

    /**
     * 同步系统用户
     */
    void synSysUser(String userId);
}

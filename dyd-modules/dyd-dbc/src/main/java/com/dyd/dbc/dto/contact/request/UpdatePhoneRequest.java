package com.dyd.dbc.dto.contact.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class UpdatePhoneRequest implements Serializable {
    /**
     * id
     */
    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * 类型:1-产品, 2-配件
     */
    @NotNull(message = "类型不能为空")
    private Integer type;

    /**
     * 电话
     */
    @NotNull(message = "电话不能为空")
    @NotBlank(message = "电话不能为空")
    private String phone;
}

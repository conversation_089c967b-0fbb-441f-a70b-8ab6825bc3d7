package com.dyd.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dyd.system.api.domain.SysDept;
import com.dyd.system.domain.SysCountry;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 国家管理 数据层
 *
 * <AUTHOR>
 */
public interface SysCountryMapper extends BaseMapper<SysCountry> {

    /**
     * 搜索国家
     *
     * @param countryName
     * @return
     */
    List<SysCountry> searchList(@Param("keyword") String countryName);
}

package com.dyd.system.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_user")
public class SysUserDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @TableId(value = "user_id", type = IdType.AUTO)
    private Long userId;

    /**
     * 钉钉ID
     */
    private String ddUserId;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 用户账号
     */
    private String userName;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户类型（00系统用户）
     */
    private String userType;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 手机号码
     */
    private String phonenumber;

    /**
     * 用户性别（0男 1女 2未知）
     */
    private String sex;

    /**
     * 头像地址
     */
    private String avatar;

    /**
     * 密码
     */
    private String password;

    /**
     * 帐号状态（0正常 1停用）
     */
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 最后登录IP
     */
    private String loginIp;

    /**
     * 最后登录时间
     */
    private LocalDateTime loginDate;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 民族
     */
    private String nation;

    /**
     * 证件类型
     */
    private String idType;

    /**
     * 出生日期
     */
    private String birthday;

    /**
     * 所属公司
     */
    private String company;

    /**
     * 证件号码
     */
    private String idNo;

    /**
     * 入职日期
     */
    private String joinedDate;

    /**
     * 员工类别
     */
    private String category;

    /**
     * 工作城市
     */
    private String workCity;

    /**
     * 转正日期
     */
    private String probationDate;

    /**
     * 累计工龄
     */
    private String seniority;

    /**
     * 参加工作日期
     */
    private String participatework;

    /**
     * 学校
     */
    private String school;

    /**
     * 专业
     */
    private String speciality;

    /**
     * 学习方式
     */
    private String learningStyle;

    /**
     * 学历
     */
    private String education;

    /**
     * 教育证书
     */
    private String certificate;

    /**
     * 公司名称
     */
    private String previousCompany;

    /**
     * 职位名称
     */
    private String title;

    /**
     * 入职日期
     */
    private String previousJoinedDate;

    /**
     * 离职日期
     */
    private String terminationDate;

    /**
     * 证明人
     */
    private String certifier;

    /**
     * 联系方式
     */
    private String contactInformation;

    /**
     * 公司手机号
     */
    private String companyPhone;

    /**
     * 公司邮箱
     */
    private String companyEmail;

    /**
     * 籍贯
     */
    private String nativePlace;

    /**
     * 政治面貌
     */
    private String politicalOutlook;

    /**
     * 婚姻状况
     */
    private String maritalStatus;

    /**
     * 家人姓名
     */
    private String familyName;

    /**
     * 家人性别
     */
    private String familySex;

    /**
     * 关系
     */
    private String relationship;

    /**
     * 联系方式
     */
    private String familyPhone;

    /**
     * 微信号
     */
    private String wxNo;

    /**
     * 工作内容
     */
    private String work;

    /**
     * 兴趣爱好
     */
    private String interest;

    /**
     * 工作内容附件
     */
    private String workFile;

    /**
     * 兴趣爱好附件
     */
    private String interestFile;

    /**
     * 主管（0不是 1是）
     */
    private String manager;

    /**
     * 微信小程序openid
     */
    private String openid;

    /**
     * 微信小程序seesionkey
     */
    private String wxSessionKey;

    /**
     * 微信生态用户唯一标识
     */
    private String wxUnionId;

}

CREATE TABLE `dd_statistical_attendance` (
     `id` bigint unsigned NOT NULL AUTO_INCREMENT,
     `user_id` varchar(64)  DEFAULT NULL COMMENT '用户ID',
     `user_no` varchar(64)  DEFAULT NULL COMMENT '用户工号',
     `user_name` varchar(64)  DEFAULT NULL COMMENT '用户姓名',
     `statistical_date` varchar(64)  DEFAULT NULL COMMENT '统计日期',
     `clock_out_result` varchar(255)  DEFAULT NULL COMMENT '下班打卡结果',
     `clock_out_time` varchar(64)  DEFAULT NULL COMMENT '下班打卡时间',
     `clock_in_result` varchar(255)  DEFAULT NULL COMMENT '上班打卡结果',
     `clock_in_time` varchar(64)  DEFAULT NULL COMMENT '上班打卡时间',
     `neglect_work` varchar(64)  DEFAULT NULL COMMENT '旷工天数',
     `be_late_neglect_work` varchar(64)  DEFAULT NULL COMMENT '旷工迟到天数',
     `attendance_days` varchar(64)  DEFAULT NULL COMMENT '出勤天数',
     `attendance_results` varchar(500)  DEFAULT NULL COMMENT '考勤结果',
     `travel_duration` varchar(64)  DEFAULT NULL COMMENT '出差时长',
     `delay_duration` varchar(64)  DEFAULT NULL COMMENT '迟到时长',
     `number_of_latencies` varchar(64)  DEFAULT NULL COMMENT '迟到次数',
     `early_departure_duration` varchar(64)  DEFAULT NULL COMMENT '早退时长',
     `number_of_early_departures` varchar(64)  DEFAULT NULL COMMENT '早退次数',
     `number_of_card_replacements` varchar(64)  DEFAULT NULL COMMENT '补卡次数',
     `number_of_missed_cards_after_work` varchar(64)  DEFAULT NULL COMMENT '下班缺卡次数',
     `number_of_work_card_shortages` varchar(64)  DEFAULT NULL COMMENT '上班缺卡次数',
     `duration_of_outing` varchar(64)  DEFAULT NULL COMMENT '外出时长',
     `severe_lateness_duration` varchar(64)  DEFAULT NULL COMMENT '严重迟到时长',
     `number_of_severe_latencies` varchar(64)  DEFAULT NULL COMMENT '严重迟到次数',
     `attendance_days_required` varchar(64)  DEFAULT NULL COMMENT '应出勤天数',
     `annual_leave` varchar(64)  DEFAULT NULL COMMENT '年假',
     `sick_leave` varchar(64)  DEFAULT NULL COMMENT '病假',
     `leave_of_absence` varchar(64)  DEFAULT NULL COMMENT '事假',
     `compensatory_leave` varchar(64)  DEFAULT NULL COMMENT '调休',
     `marriage_leave` varchar(64)  DEFAULT NULL COMMENT '婚假',
     `maternity_leave` varchar(64)  DEFAULT NULL COMMENT '产假',
     `paternity_leave` varchar(64)  DEFAULT NULL COMMENT '陪产假',
     `accompany_sick_leave` varchar(64)  DEFAULT NULL COMMENT '陪病假',
     `home_leave` varchar(64)  DEFAULT NULL COMMENT '探亲假',
     `others_leave` varchar(64)  DEFAULT NULL COMMENT '其他假',
     `sync_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '同步时间',
     PRIMARY KEY (`id`) USING BTREE
) COMMENT='钉钉统计出勤表';

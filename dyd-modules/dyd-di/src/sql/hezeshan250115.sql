-- 新增涂装二级
INSERT INTO sys_category (id, pid, name, code, has_child)
VALUES ('A0104', 'A01', '白色家电', 'A0104', '1'),
       ('A0105', 'A01', '两轮车', 'A0105', '1'),
       ('A0106', 'A01', '彩涂板', 'A0106', '1'),
       ('A0107', 'A01', '印铁制罐', 'A0107', '1'),
       ('A0108', 'A01', '汽车金属零部件', 'A0108', '1'),
       ('A0109', 'A01', '厂房空调', 'A0109', '1'),
       ('A0110', 'A01', '辐射釆暖', 'A0120', '1');

-- 新增涂装三级
INSERT INTO sys_category (id, pid, name, code, has_child)
VALUES ('A010400', 'A0104', '白色家电', 'A010400', '0'),
       ('A010401', 'A0104', '其他', 'A010401', '0'),
       ('A010500', 'A0105', '两轮车', 'A010500', '0'),
       ('A010501', 'A0105', '其他', 'A010501', '0'),
       ('A010600', 'A0106', '彩涂板', 'A010600', '0'),
       ('A010601', 'A0106', '其他', 'A010601', '0'),
       ('A010700', 'A0107', '印铁制罐', 'A010700', '0'),
       ('A010701', 'A0107', '其他', 'A010701', '0'),
       ('A010800', 'A0108', '汽车金属零部件', 'A010800', '0'),
       ('A010801', 'A0108', '其他', 'A010801', '0'),
       ('A010900', 'A0109', '厂房空调', 'A010900', '0'),
       ('A010901', 'A0109', '其他', 'A010901', '0'),
       ('A011000', 'A0110', '辐射釆暖', 'A011000', '0'),
       ('A011001', 'A0110', '其他', 'A011001', '0');

-- 新增涂装三级负责人
INSERT INTO sys_dict_data (dict_label, dict_value, dict_type)
VALUES ('dyd471', 'A010400', 'industry_owner'),
       ('dyd471', 'A010500', 'industry_owner'),
       ('dyd471', 'A010600', 'industry_owner'),
       ('dyd471', 'A010700', 'industry_owner'),
       ('dyd471', 'A010800', 'industry_owner'),
       ('dyd471', 'A010900', 'industry_owner'),
       ('dyd471', 'A011000', 'industry_owner'),
       ('dyd026', 'A010401', 'industry_owner'),
       ('dyd026', 'A010501', 'industry_owner'),
       ('dyd026', 'A010601', 'industry_owner'),
       ('dyd026', 'A010701', 'industry_owner'),
       ('dyd026', 'A010801', 'industry_owner'),
       ('dyd026', 'A010901', 'industry_owner'),
       ('dyd026', 'A011001', 'industry_owner');



-- ====================================================================================



-- 新增干燥二级
INSERT INTO sys_category (id, pid, name,code,has_child)
VALUES ('A0211', 'A02', '焙烧窑', 'A0211', '1'),
       ('A0212', 'A02', '间接热风炉', 'A0212', '1'),
       ('A0213', 'A02', '肥料', 'A0213', '1'),
       ('A0214', 'A02', '白炭黑', 'A0214', '1'),
       ('A0215', 'A02', '氧化铝', 'A0215', '1'),
       ('A0216', 'A02', '陶瓷粉末', 'A0216', '1'),
       ('A0217', 'A02', '蛋白粉，奶精，咖啡，淀粉', 'A0217', '1'),
       ('A0218', 'A02', '珍珠岩膨胀炉', 'A0218', '1'),
       ('A0219', 'A02', '珠化工艺材料', 'A0219', '1'),
       ('A0220', 'A02', '玄武岩膨胀炉', 'A0220', '1');

-- 新增干燥三级
INSERT INTO sys_category (id, pid, name,code,has_child)
VALUES ('A021100', 'A0211', '焙烧窑', 'A021100', '0'),
       ('A021200', 'A0212', '间接热风炉', 'A021200', '0'),
       ('A021300', 'A0213', '肥料', 'A021300', '0'),
       ('A021400', 'A0214', '白炭黑', 'A021400', '0'),
       ('A021500', 'A0215', '氧化铝', 'A021500', '0'),
       ('A021600', 'A0216', '陶瓷粉末', 'A021600', '0'),
       ('A021700', 'A0217', '蛋白粉，奶精，咖啡，淀粉', 'A021700', '0'),
       ('A021800', 'A0218', '珍珠岩膨胀炉', 'A021800', '0'),
       ('A021900', 'A0219', '珠化工艺材料', 'A021900', '0'),
       ('A022000', 'A0220', '玄武岩膨胀炉', 'A022000', '0'),
       ('A021101', 'A0211', '其他', 'A021101', '0'),
       ('A021201', 'A0212', '其他', 'A021201', '0'),
       ('A021301', 'A0213', '其他', 'A021301', '0'),
       ('A021401', 'A0214', '其他', 'A021401', '0'),
       ('A021501', 'A0215', '其他', 'A021501', '0'),
       ('A021601', 'A0216', '其他', 'A021601', '0'),
       ('A021701', 'A0217', '其他', 'A021701', '0'),
       ('A021801', 'A0218', '其他', 'A021801', '0'),
       ('A021901', 'A0219', '其他', 'A021901', '0'),
       ('A022001', 'A0220', '其他', 'A022001', '0');

-- 新增干燥三级负责人
INSERT INTO sys_dict_data (dict_label, dict_value, dict_type)
VALUES ('dyd313', 'A021100', 'industry_owner'),
       ('dyd313', 'A021200', 'industry_owner'),
       ('dyd313', 'A021300', 'industry_owner'),
       ('dyd313', 'A021400', 'industry_owner'),
       ('dyd313', 'A021500', 'industry_owner'),
       ('dyd313', 'A021600', 'industry_owner'),
       ('dyd313', 'A021700', 'industry_owner'),
       ('dyd313', 'A021800', 'industry_owner'),
       ('dyd036', 'A021900', 'industry_owner'),
       ('dyd313', 'A022000', 'industry_owner'),
       ('dyd026', 'A021101', 'industry_owner'),
       ('dyd026', 'A021201', 'industry_owner'),
       ('dyd026', 'A021301', 'industry_owner'),
       ('dyd026', 'A021401', 'industry_owner'),
       ('dyd026', 'A021501', 'industry_owner'),
       ('dyd026', 'A021601', 'industry_owner'),
       ('dyd026', 'A021701', 'industry_owner'),
       ('dyd026', 'A021801', 'industry_owner'),
       ('dyd026', 'A021901', 'industry_owner'),
       ('dyd026', 'A022001', 'industry_owner');


-- ====================================================================================

-- 新增火焰二级
INSERT INTO sys_category (id, pid, name,code,has_child)
VALUES ('A0407', 'A04', '薄膜印刷前处理', 'A0407', '1'),
       ('A0408', 'A04', '消费品印刷前处理', 'A0408', '1'),
       ('A0409', 'A04', '金属表面清洁', 'A0409', '1');

-- 新增火焰三级
INSERT INTO sys_category (id, pid, name,code,has_child)
VALUES ('A040700', 'A0400', '薄膜印刷前处理', 'A040700', '0'),
       ('A040800', 'A0401', '消费品印刷前处理', 'A040800', '0'),
       ('A040900', 'A0402', '金属表面清洁', 'A040900', '0'),
       ('A040701', 'A0400', '其他', 'A040701', '0'),
       ('A040801', 'A0401', '其他', 'A040801', '0'),
       ('A040901', 'A0402', '其他', 'A040901', '0');

-- 新增火焰三级负责人
INSERT INTO sys_dict_data (dict_label, dict_value, dict_type)
VALUES ('dyd008', 'A040700', 'industry_owner'),
       ('dyd378', 'A040800', 'industry_owner'),
       ('dyd378', 'A040900', 'industry_owner'),
       ('dyd026', 'A040701', 'industry_owner'),
       ('dyd026', 'A040801', 'industry_owner'),
       ('dyd026', 'A040901', 'industry_owner');

-- ====================================================================================

-- 新增轻工二级
INSERT INTO sys_category (id, pid, name,code,has_child)
VALUES ('A0502', 'A05', '纺织', 'A0502', '1'),
       ('A0503', 'A05', '皮革', 'A0503', '1'),
       ('A0504', 'A05', '木材烘干', 'A0504', '1'),
       ('A0505', 'A05', '手套', 'A0505', '1'),
       ('A0506', 'A05', '塑料发泡', 'A0506', '1'),
       ('A0507', 'A05', '砂纸', 'A0507', '1'),
       ('A0508', 'A05', '浸胶/帘子布', 'A0508', '1'),
       ('A0509', 'A05', '印刷', 'A0509', '1'),
       ('A0510', 'A05', '洗涤', 'A0510', '1'),
       ('A0511', 'A05', '蓄电池', 'A0511', '1'),
       ('A0512', 'A05', '带式皮带', 'A0512', '1'),
       ('A0513', 'A05', '钢丝膨胀', 'A0513', '1'),
       ('A0514', 'A05', '炒货', 'A0514', '1');

-- 新增轻工三级
INSERT INTO sys_category (id, pid, name,code,has_child)
VALUES  ('A050200', 'A0502', '纺织', 'A050200', '0'),
        ('A050300', 'A0503', '皮革', 'A050300', '0'),
        ('A050400', 'A0504', '木材烘干', 'A050400', '0'),
        ('A050500', 'A0505', '手套', 'A050500', '0'),
        ('A050600', 'A0506', '塑料发泡', 'A050600', '0'),
        ('A050700', 'A0507', '砂纸', 'A050700', '0'),
        ('A050800', 'A0508', '浸胶/帘子布', 'A050800', '0'),
        ('A050900', 'A0509', '印刷', 'A050900', '0'),
        ('A051000', 'A0510', '洗涤', 'A051000', '0'),
        ('A051100', 'A0511', '蓄电池', 'A051100', '0'),
        ('A051200', 'A0512', '带式皮带', 'A051200', '0'),
        ('A051300', 'A0513', '钢丝膨胀', 'A051300', '0'),
        ('A051400', 'A0514', '炒货', 'A051400', '0'),
        ('A050201', 'A0502', '其他', 'A050201', '0'),
        ('A050301', 'A0503', '其他', 'A050301', '0'),
        ('A050401', 'A0504', '其他', 'A050401', '0'),
        ('A050501', 'A0505', '其他', 'A050501', '0'),
        ('A050601', 'A0506', '其他', 'A050601', '0'),
        ('A050701', 'A0507', '其他', 'A050701', '0'),
        ('A050801', 'A0508', '其他', 'A050801', '0'),
        ('A050901', 'A0509', '其他', 'A050901', '0'),
        ('A051001', 'A0510', '其他', 'A051001', '0'),
        ('A051101', 'A0511', '其他', 'A051101', '0'),
        ('A051201', 'A0512', '其他', 'A051201', '0'),
        ('A051301', 'A0513', '其他', 'A051301', '0'),
        ('A051401', 'A0514', '其他', 'A051401', '0');

-- 新增轻工三级负责人
INSERT INTO sys_dict_data (dict_label, dict_value, dict_type)
VALUES  ('dyd014', 'A050200', 'industry_owner'),
        ('dyd014', 'A050300', 'industry_owner'),
        ('dyd471', 'A050400', 'industry_owner'),
        ('dyd014', 'A050500', 'industry_owner'),
        ('dyd014', 'A050600', 'industry_owner'),
        ('dyd471', 'A050700', 'industry_owner'),
        ('dyd014', 'A050800', 'industry_owner'),
        ('dyd471', 'A050900', 'industry_owner'),
        ('dyd471', 'A051000', 'industry_owner'),
        ('dyd014', 'A051100', 'industry_owner'),
        ('dyd471', 'A051200', 'industry_owner'),
        ('dyd471', 'A051300', 'industry_owner'),
        ('dyd471', 'A051400', 'industry_owner'),
        ('dyd026', 'A050201', 'industry_owner'),
        ('dyd026', 'A050301', 'industry_owner'),
        ('dyd026', 'A050401', 'industry_owner'),
        ('dyd026', 'A050501', 'industry_owner'),
        ('dyd026', 'A050601', 'industry_owner'),
        ('dyd026', 'A050701', 'industry_owner'),
        ('dyd026', 'A050801', 'industry_owner'),
        ('dyd026', 'A050901', 'industry_owner'),
        ('dyd026', 'A051001', 'industry_owner'),
        ('dyd026', 'A051101', 'industry_owner'),
        ('dyd026', 'A051201', 'industry_owner'),
        ('dyd026', 'A051301', 'industry_owner'),
        ('dyd026', 'A051401', 'industry_owner');

-- 逻辑删除旧的
update sys_category set del_flag = '1'  WHERE id = 'A0501';
-- ====================================================================================

-- 新增非标二级
INSERT INTO sys_category (id, pid, name,code,has_child)
VALUES  ('A0607', 'A06', '辐射管燃烧器', 'A0607', '1'),
        ('A0608', 'A06', '加热炉', 'A0608', '1'),
        ('A0609', 'A06', '球团加热炉', 'A0609', '1'),
        ('A0610', 'A06', '热镀锌', 'A0610', '1'),
        ('A0611', 'A06', '烘炉燃烧器', 'A0611', '1'),
        ('A0612', 'A06', '钢包烘烤', 'A0612', '1'),
        ('A0613', 'A06', '钢包烘烤', 'A0613', '1'),
        ('A0614', 'A06', '溜槽加热', 'A0614', '1'),
        ('A0615', 'A06', '铜', 'A0615', '1'),
        ('A0616', 'A06', '铜', 'A0616', '1'),
        ('A0617', 'A06', '铝', 'A0617', '1'),
        ('A0618', 'A06', '锌', 'A0618', '1'),
        ('A0619', 'A06', '浮法玻璃窑', 'A0619', '1'),
        ('A0620', 'A06', '玻纤窑', 'A0620', '1'),
        ('A0621', 'A06', '玻纤烘干', 'A0621', '1'),
        ('A0622', 'A06', '马蹄窑', 'A0622', '1'),
        ('A0623', 'A06', '料道加热', 'A0623', '1'),
        ('A0624', 'A06', '料道加热', 'A0624', '1'),
        ('A0625', 'A06', '退火炉', 'A0625', '1'),
        ('A0626', 'A06', '生活垃圾焚烧', 'A0626', '1'),
        ('A0627', 'A06', '酸性气焚烧', 'A0627', '1'),
        ('A0628', 'A06', '废酸裂解', 'A0628', '1'),
        ('A0629', 'A06', '烘炉系统', 'A0629', '1'),
        ('A0630', 'A06', '酸再生', 'A0630', '1'),
        ('A0631', 'A06', '非标管路定制', 'A0631', '1'),
        ('A0632', 'A06', '甲醇燃料', 'A0632', '1'),
        ('A0633', 'A06', '氢能源', 'A0633', '1'),
        ('A0634', 'A06', '氮气', 'A0634', '1'),
        ('A0635', 'A06', 'EPC总包', 'A0635', '1'),
        ('A0636', 'A06', '节能改造', 'A0636', '1'),
        ('A0637', 'A06', '非标热风炉', 'A0637', '1'),
        ('A0638', 'A06', '热水锅炉', 'A0638', '1'),
        ('A0639', 'A06', '蒸汽锅炉', 'A0639', '1'),
        ('A0640', 'A06', '动力锅炉', 'A0640', '1'),
        ('A0641', 'A06', '硫磺回收', 'A0641', '1'),
        ('A0642', 'A06', '废液焚烧炉', 'A0642', '1'),
        ('A0643', 'A06', '水泥窑燃烧器', 'A0643', '1'),
        ('A0644', 'A06', '冷凝锅炉', 'A0644', '1'),
        ('A0645', 'A06', '小型热水锅炉', 'A0645', '1'),
        ('A0646', 'A06', '果蔬烘干', 'A0646', '1'),
        ('A0647', 'A06', '大棚加热', 'A0647', '1');

-- 新增非标三级
INSERT INTO sys_category (id, pid, name,code,has_child)
VALUES  ('A060700', 'A0607', '辐射管燃烧器', 'A060700', '0'),
        ('A060800', 'A0608', '加热炉', 'A060800', '0'),
        ('A060900', 'A0609', '球团加热炉', 'A060900', '0'),
        ('A061000', 'A0610', '热镀锌', 'A061000', '0'),
        ('A061100', 'A0611', '烘炉燃烧器', 'A061100', '0'),
        ('A061200', 'A0612', '钢包烘烤', 'A061200', '0'),
        ('A061300', 'A0613', '钢包烘烤', 'A061300', '0'),
        ('A061400', 'A0614', '溜槽加热', 'A061400', '0'),
        ('A061500', 'A0615', '铜', 'A061500', '0'),
        ('A061600', 'A0616', '铜', 'A061600', '0'),
        ('A061700', 'A0617', '铝', 'A061700', '0'),
        ('A061800', 'A0618', '锌', 'A061800', '0'),
        ('A061900', 'A0619', '浮法玻璃窑', 'A061900', '0'),
        ('A062000', 'A0620', '玻纤窑', 'A062000', '0'),
        ('A062100', 'A0621', '玻纤烘干', 'A062100', '0'),
        ('A062200', 'A0622', '马蹄窑', 'A062200', '0'),
        ('A062300', 'A0623', '料道加热', 'A062300', '0'),
        ('A062400', 'A0624', '料道加热', 'A062400', '0'),
        ('A062500', 'A0625', '退火炉', 'A062500', '0'),
        ('A062600', 'A0626', '生活垃圾焚烧', 'A062600', '0'),
        ('A062700', 'A0627', '酸性气焚烧', 'A062700', '0'),
        ('A062800', 'A0628', '废酸裂解', 'A062800', '0'),
        ('A062900', 'A0629', '烘炉系统', 'A062900', '0'),
        ('A063000', 'A0630', '酸再生', 'A063000', '0'),
        ('A063100', 'A0631', '非标管路定制', 'A063100', '0'),
        ('A063200', 'A0632', '甲醇燃料', 'A063200', '0'),
        ('A063300', 'A0633', '氢能源', 'A063300', '0'),
        ('A063400', 'A0634', '氮气', 'A063400', '0'),
        ('A063500', 'A0635', 'EPC总包', 'A063500', '0'),
        ('A063600', 'A0636', '节能改造', 'A063600', '0'),
        ('A063700', 'A0637', '非标热风炉', 'A063700', '0'),
        ('A063800', 'A0638', '热水锅炉', 'A063800', '0'),
        ('A063900', 'A0639', '蒸汽锅炉', 'A063900', '0'),
        ('A064000', 'A0640', '动力锅炉', 'A064000', '0'),
        ('A064100', 'A0641', '硫磺回收', 'A064100', '0'),
        ('A064200', 'A0642', '废液焚烧炉', 'A064200', '0'),
        ('A064300', 'A0643', '水泥窑燃烧器', 'A064300', '0'),
        ('A064400', 'A0644', '冷凝锅炉', 'A064400', '0'),
        ('A064500', 'A0645', '小型热水锅炉', 'A064500', '0'),
        ('A064600', 'A0646', '果蔬烘干', 'A064600', '0'),
        ('A064700', 'A0647', '大棚加热', 'A064700', '0'),
        ('A060701', 'A0607', '其他', 'A060701', '0'),
        ('A060801', 'A0608', '其他', 'A060801', '0'),
        ('A060901', 'A0609', '其他', 'A060901', '0'),
        ('A061001', 'A0610', '其他', 'A061001', '0'),
        ('A061101', 'A0611', '其他', 'A061101', '0'),
        ('A061201', 'A0612', '其他', 'A061201', '0'),
        ('A061301', 'A0613', '其他', 'A061301', '0'),
        ('A061401', 'A0614', '其他', 'A061401', '0'),
        ('A061501', 'A0615', '其他', 'A061501', '0'),
        ('A061601', 'A0616', '其他', 'A061601', '0'),
        ('A061701', 'A0617', '其他', 'A061701', '0'),
        ('A061801', 'A0618', '其他', 'A061801', '0'),
        ('A061901', 'A0619', '其他', 'A061901', '0'),
        ('A062001', 'A0620', '其他', 'A062001', '0'),
        ('A062101', 'A0621', '其他', 'A062101', '0'),
        ('A062201', 'A0622', '其他', 'A062201', '0'),
        ('A062301', 'A0623', '其他', 'A062301', '0'),
        ('A062401', 'A0624', '其他', 'A062401', '0'),
        ('A062501', 'A0625', '其他', 'A062501', '0'),
        ('A062601', 'A0626', '其他', 'A062601', '0'),
        ('A062701', 'A0627', '其他', 'A062701', '0'),
        ('A062801', 'A0628', '其他', 'A062801', '0'),
        ('A062901', 'A0629', '其他', 'A062901', '0'),
        ('A063001', 'A0630', '其他', 'A063001', '0'),
        ('A063101', 'A0631', '其他', 'A063101', '0'),
        ('A063201', 'A0632', '其他', 'A063201', '0'),
        ('A063301', 'A0633', '其他', 'A063301', '0'),
        ('A063401', 'A0634', '其他', 'A063401', '0'),
        ('A063501', 'A0635', '其他', 'A063501', '0'),
        ('A063601', 'A0636', '其他', 'A063601', '0'),
        ('A063701', 'A0637', '其他', 'A063701', '0'),
        ('A063801', 'A0638', '其他', 'A063801', '0'),
        ('A063901', 'A0639', '其他', 'A063901', '0'),
        ('A064001', 'A0640', '其他', 'A064001', '0'),
        ('A064101', 'A0641', '其他', 'A064101', '0'),
        ('A064201', 'A0642', '其他', 'A064201', '0'),
        ('A064301', 'A0643', '其他', 'A064301', '0'),
        ('A064401', 'A0644', '其他', 'A064401', '0'),
        ('A064501', 'A0645', '其他', 'A064501', '0'),
        ('A064601', 'A0646', '其他', 'A064601', '0'),
        ('A064701', 'A0647', '其他', 'A064701', '0');

-- 新增非标三级负责人
INSERT INTO sys_dict_data (dict_label, dict_value, dict_type)
VALUES  ('dyd008', 'A060700', 'industry_owner'),
        ('dyd008', 'A060800', 'industry_owner'),
        ('dyd008', 'A060900', 'industry_owner'),
        ('dyd008', 'A061000', 'industry_owner'),
        ('dyd008', 'A061100', 'industry_owner'),
        ('dyd008', 'A061200', 'industry_owner'),
        ('dyd008', 'A061300', 'industry_owner'),
        ('dyd008', 'A061400', 'industry_owner'),
        ('dyd008', 'A061500', 'industry_owner'),
        ('dyd008', 'A061600', 'industry_owner'),
        ('dyd008', 'A061700', 'industry_owner'),
        ('dyd008', 'A061800', 'industry_owner'),
        ('dyd008', 'A061900', 'industry_owner'),
        ('dyd008', 'A062000', 'industry_owner'),
        ('dyd008', 'A062100', 'industry_owner'),
        ('dyd008', 'A062200', 'industry_owner'),
        ('dyd008', 'A062300', 'industry_owner'),
        ('dyd008', 'A062400', 'industry_owner'),
        ('dyd008', 'A062500', 'industry_owner'),
        ('dyd008', 'A062600', 'industry_owner'),
        ('dyd008', 'A062700', 'industry_owner'),
        ('dyd008', 'A062800', 'industry_owner'),
        ('dyd008', 'A062900', 'industry_owner'),
        ('dyd008', 'A063000', 'industry_owner'),
        ('dyd008', 'A063100', 'industry_owner'),
        ('dyd008', 'A063200', 'industry_owner'),
        ('dyd008', 'A063300', 'industry_owner'),
        ('dyd008', 'A063400', 'industry_owner'),
        ('dyd008', 'A063500', 'industry_owner'),
        ('dyd008', 'A063600', 'industry_owner'),
        ('dyd008', 'A063700', 'industry_owner'),
        ('dyd069', 'A063800', 'industry_owner'),
        ('dyd069', 'A063900', 'industry_owner'),
        ('dyd069', 'A064000', 'industry_owner'),
        ('dyd069', 'A064100', 'industry_owner'),
        ('dyd069', 'A064200', 'industry_owner'),
        ('dyd008', 'A064300', 'industry_owner'),
        ('dyd069', 'A064400', 'industry_owner'),
        ('dyd069', 'A064500', 'industry_owner'),
        ('dyd313', 'A064600', 'industry_owner'),
        ('dyd313', 'A064700', 'industry_owner'),
        ('dyd026', 'A060701', 'industry_owner'),
        ('dyd026', 'A060801', 'industry_owner'),
        ('dyd026', 'A060901', 'industry_owner'),
        ('dyd026', 'A061001', 'industry_owner'),
        ('dyd026', 'A061101', 'industry_owner'),
        ('dyd026', 'A061201', 'industry_owner'),
        ('dyd026', 'A061301', 'industry_owner'),
        ('dyd026', 'A061401', 'industry_owner'),
        ('dyd026', 'A061501', 'industry_owner'),
        ('dyd026', 'A061601', 'industry_owner'),
        ('dyd026', 'A061701', 'industry_owner'),
        ('dyd026', 'A061801', 'industry_owner'),
        ('dyd026', 'A061901', 'industry_owner'),
        ('dyd026', 'A062001', 'industry_owner'),
        ('dyd026', 'A062101', 'industry_owner'),
        ('dyd026', 'A062201', 'industry_owner'),
        ('dyd026', 'A062301', 'industry_owner'),
        ('dyd026', 'A062401', 'industry_owner'),
        ('dyd026', 'A062501', 'industry_owner'),
        ('dyd026', 'A062601', 'industry_owner'),
        ('dyd026', 'A062701', 'industry_owner'),
        ('dyd026', 'A062801', 'industry_owner'),
        ('dyd026', 'A062901', 'industry_owner'),
        ('dyd026', 'A063001', 'industry_owner'),
        ('dyd026', 'A063101', 'industry_owner'),
        ('dyd026', 'A063201', 'industry_owner'),
        ('dyd026', 'A063301', 'industry_owner'),
        ('dyd026', 'A063401', 'industry_owner'),
        ('dyd026', 'A063501', 'industry_owner'),
        ('dyd026', 'A063601', 'industry_owner'),
        ('dyd026', 'A063701', 'industry_owner'),
        ('dyd026', 'A063801', 'industry_owner'),
        ('dyd026', 'A063901', 'industry_owner'),
        ('dyd026', 'A064001', 'industry_owner'),
        ('dyd026', 'A064101', 'industry_owner'),
        ('dyd026', 'A064201', 'industry_owner'),
        ('dyd026', 'A064301', 'industry_owner'),
        ('dyd026', 'A064401', 'industry_owner'),
        ('dyd026', 'A064501', 'industry_owner'),
        ('dyd026', 'A064601', 'industry_owner'),
        ('dyd026', 'A064701', 'industry_owner');


-- 逻辑删除旧的
update sys_category set del_flag = '1'  WHERE id = 'A0601';
update sys_category set del_flag = '1'  WHERE id = 'A0702';
-- ====================================================================================

-- 新增出口二级
INSERT INTO sys_category (id, pid, name,code,has_child)
VALUES  ('A0702', 'A06', '烧嘴', 'A0702', '1');

-- 新增出口三级
INSERT INTO sys_category (id, pid, name,code,has_child)
VALUES  ('A070200', 'A0702', '烧嘴-常规', 'A070200', '0'),
        ('A070201', 'A0702', '烧嘴-非常规', 'A070201', '0'),
        ('A070202', 'A0702', '其他', 'A070202', '0');

-- 新增出口三级负责人
INSERT INTO sys_dict_data (dict_label, dict_value, dict_type)
VALUES  ('dyd036', 'A070200', 'industry_owner'),
        ('dyd008', 'A070201', 'industry_owner'),
        ('dyd026', 'A070202', 'industry_owner');


-- ====================================================================================

-- 新增船舶二级
INSERT INTO sys_category (id, pid, name,code,has_child)
VALUES  ('A0804', 'A08', '燃料供给系统', 'A0804', '1');

-- 新增船舶三级
INSERT INTO sys_category (id, pid, name,code,has_child)
VALUES  ('A080400', 'A0804', 'GVU', 'A080400', '0'),
        ('A080401', 'A0804', '甲醇供给系统', 'A080401', '0'),
        ('A080402', 'A0804', '其他', 'A080402', '0');

-- 新增船舶三级负责人
INSERT INTO sys_dict_data (dict_label, dict_value, dict_type)
VALUES  ('dyd008', 'A080400', 'industry_owner'),
        ('dyd008', 'A080401', 'industry_owner'),
        ('dyd026', 'A080402', 'industry_owner');


-- 逻辑删除旧的
update sys_category set del_flag = '1'  WHERE id = 'A0801';
update sys_category set del_flag = '1'  WHERE id = 'A0802';
-- ====================================================================================


-- 新增动力二级
INSERT INTO sys_category (id, pid, name,code,has_child)
VALUES  ('A0903', 'A09', '电厂锅炉', 'A0903', '1');

-- 新增动力三级
INSERT INTO sys_category (id, pid, name,code,has_child)
VALUES  ('A090300', 'A0903', '等离子发生器(备件)', 'A090300', '0'),
        ('A090301', 'A0903', '风道燃烧器', 'A090301', '0'),
        ('A090302', 'A0903', '其他', 'A090302', '0');

-- 新增动力三级负责人
INSERT INTO sys_dict_data (dict_label, dict_value, dict_type)
VALUES  ('dyd008', 'A090300', 'industry_owner'),
        ('dyd367', 'A090301', 'industry_owner'),
        ('dyd026', 'A090302', 'industry_owner');


-- ====================================================================================

-- 更改废弃名称
update sys_category set name = 'RTO-非标燃料（废气废液/低热值气体等）'  WHERE id = 'A030101';

-- 废弃逻辑删除旧的
update sys_category set del_flag = '1'  WHERE id = 'A030104';

-- 新增废弃三级
INSERT INTO sys_category (id, pid, name,code,has_child)
VALUES  ('A030105', 'A0301', 'TO炉-常规燃料（天然气/液化气/轻柴油/沼气）', 'A030105', '0'),
        ('A030106', 'A0301', 'TO炉-非标燃料（废气废液/低热值气体等）', 'A030106', '0');


-- 新增废弃三级负责人
INSERT INTO sys_dict_data (dict_label, dict_value, dict_type)
VALUES  ('dyd367', 'A030105', 'industry_owner'),
        ('dyd008', 'A030106', 'industry_owner');

-- ====================================================================================

-- 固废逻辑删除旧的
update sys_category set del_flag = '1'  WHERE id = 'A030201';
update sys_category set del_flag = '1'  WHERE id = 'A030206';

-- 新增固废三级
INSERT INTO sys_category (id, pid, name,code,has_child)
VALUES  ('A030207', 'A0302', '危废处理厂-常规燃料（天然气/液化气/轻柴油/沼气）', 'A030207', '0'),
        ('A030208', 'A0302', '危废处理厂-非标燃料（废气废液/低热值气体等）', 'A030208', '0');


-- 新增固废三级负责人
INSERT INTO sys_dict_data (dict_label, dict_value, dict_type)
VALUES  ('dyd367', 'A030207', 'industry_owner'),
        ('dyd008', 'A030208', 'industry_owner');

package com.dyd.di.marketing.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dyd.di.marketing.domain.DiTerritoryMapping;
import com.dyd.di.marketing.domain.DiTerritoryProductMapping;
import com.dyd.di.marketing.domain.vo.MarketingTerritoryMappingVO;
import com.dyd.di.marketing.mapper.DiTerritoryMappingMapper;
import com.dyd.di.marketing.mapper.DiTerritoryProductMappingMapper;
import com.dyd.di.marketing.service.MarketingTerritoryMappingService;
import com.dyd.di.storage.redisson.RedissonLock;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 营销版图映射服务删除功能测试
 * 专门测试外键约束问题的修复
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class MarketingTerritoryMappingServiceImplDeleteTest {

    @Autowired
    private MarketingTerritoryMappingService marketingTerritoryMappingService;

    @Autowired
    private DiTerritoryMappingMapper territoryMappingMapper;

    @Autowired
    private DiTerritoryProductMappingMapper diTerritoryProductMappingMapper;

    private static final String TEST_REF_ID = "DYD-SJ250529-0007";
    private static final Long TEST_TERRITORY_ID = 23L;
    private static final Long TEST_PRODUCT_ID = 100L;

    @BeforeEach
    void setUp() {
        // 清理测试数据
        cleanupTestData();
        
        // 准备测试数据
        setupTestData();
    }

    /**
     * 测试删除整个业务版图映射（包括产品映射）
     * 验证外键约束问题已解决
     */
    @Test
    void testDeleteEntireTerritoryMapping() {
        // 验证测试数据存在
        assertTestDataExists();

        // 创建删除请求
        MarketingTerritoryMappingVO deleteVO = new MarketingTerritoryMappingVO();
        deleteVO.setRefId(TEST_REF_ID);
        deleteVO.setTerritoryId(TEST_TERRITORY_ID);
        // productId 和 productIds 都为空，表示删除整个业务版图

        // 执行删除操作
        assertDoesNotThrow(() -> {
            marketingTerritoryMappingService.del(deleteVO);
        }, "删除整个业务版图映射不应该抛出外键约束异常");

        // 验证删除结果
        verifyDataDeleted();
    }

    /**
     * 测试删除单个产品映射
     */
    @Test
    void testDeleteSingleProductMapping() {
        // 验证测试数据存在
        assertTestDataExists();

        // 创建删除请求
        MarketingTerritoryMappingVO deleteVO = new MarketingTerritoryMappingVO();
        deleteVO.setRefId(TEST_REF_ID);
        deleteVO.setTerritoryId(TEST_TERRITORY_ID);
        deleteVO.setProductId(TEST_PRODUCT_ID);

        // 执行删除操作
        assertDoesNotThrow(() -> {
            marketingTerritoryMappingService.del(deleteVO);
        }, "删除单个产品映射不应该抛出异常");

        // 验证只删除了产品映射，业务版图映射仍然存在
        Long productMappingCount = diTerritoryProductMappingMapper.selectCount(
            Wrappers.<DiTerritoryProductMapping>lambdaQuery()
                .eq(DiTerritoryProductMapping::getRefId, TEST_REF_ID)
                .eq(DiTerritoryProductMapping::getTerritoryId, TEST_TERRITORY_ID)
                .eq(DiTerritoryProductMapping::getProductId, TEST_PRODUCT_ID)
        );
        assertEquals(0L, productMappingCount, "产品映射应该被删除");

        Long territoryMappingCount = territoryMappingMapper.selectCount(
            Wrappers.<DiTerritoryMapping>lambdaQuery()
                .eq(DiTerritoryMapping::getRefId, TEST_REF_ID)
                .eq(DiTerritoryMapping::getTerritoryId, TEST_TERRITORY_ID)
        );
        assertEquals(1L, territoryMappingCount, "业务版图映射应该仍然存在");
    }

    /**
     * 准备测试数据
     */
    private void setupTestData() {
        // 插入业务版图映射
        DiTerritoryMapping territoryMapping = DiTerritoryMapping.builder()
                .refId(TEST_REF_ID)
                .territoryId(TEST_TERRITORY_ID)
                .territoryJson("{\"test\": \"data\"}")
                .territoryInfo("{\"info\": \"test\"}")
                .build();
        territoryMappingMapper.insert(territoryMapping);

        // 插入产品映射
        DiTerritoryProductMapping productMapping = DiTerritoryProductMapping.builder()
                .refId(TEST_REF_ID)
                .territoryId(TEST_TERRITORY_ID)
                .productId(TEST_PRODUCT_ID)
                .productJson("{\"product\": \"data\"}")
                .productInfo("{\"product_info\": \"test\"}")
                .build();
        diTerritoryProductMappingMapper.insert(productMapping);

        log.info("测试数据准备完成：refId={}, territoryId={}, productId={}", 
                TEST_REF_ID, TEST_TERRITORY_ID, TEST_PRODUCT_ID);
    }

    /**
     * 清理测试数据
     */
    private void cleanupTestData() {
        // 先删除产品映射（子表）
        diTerritoryProductMappingMapper.delete(
            Wrappers.<DiTerritoryProductMapping>lambdaQuery()
                .eq(DiTerritoryProductMapping::getRefId, TEST_REF_ID)
        );

        // 再删除业务版图映射（父表）
        territoryMappingMapper.delete(
            Wrappers.<DiTerritoryMapping>lambdaQuery()
                .eq(DiTerritoryMapping::getRefId, TEST_REF_ID)
        );
    }

    /**
     * 验证测试数据存在
     */
    private void assertTestDataExists() {
        Long territoryCount = territoryMappingMapper.selectCount(
            Wrappers.<DiTerritoryMapping>lambdaQuery()
                .eq(DiTerritoryMapping::getRefId, TEST_REF_ID)
                .eq(DiTerritoryMapping::getTerritoryId, TEST_TERRITORY_ID)
        );
        assertEquals(1L, territoryCount, "业务版图映射测试数据应该存在");

        Long productCount = diTerritoryProductMappingMapper.selectCount(
            Wrappers.<DiTerritoryProductMapping>lambdaQuery()
                .eq(DiTerritoryProductMapping::getRefId, TEST_REF_ID)
                .eq(DiTerritoryProductMapping::getTerritoryId, TEST_TERRITORY_ID)
                .eq(DiTerritoryProductMapping::getProductId, TEST_PRODUCT_ID)
        );
        assertEquals(1L, productCount, "产品映射测试数据应该存在");
    }

    /**
     * 验证数据已被删除
     */
    private void verifyDataDeleted() {
        Long territoryCount = territoryMappingMapper.selectCount(
            Wrappers.<DiTerritoryMapping>lambdaQuery()
                .eq(DiTerritoryMapping::getRefId, TEST_REF_ID)
                .eq(DiTerritoryMapping::getTerritoryId, TEST_TERRITORY_ID)
        );
        assertEquals(0L, territoryCount, "业务版图映射应该被删除");

        Long productCount = diTerritoryProductMappingMapper.selectCount(
            Wrappers.<DiTerritoryProductMapping>lambdaQuery()
                .eq(DiTerritoryProductMapping::getRefId, TEST_REF_ID)
                .eq(DiTerritoryProductMapping::getTerritoryId, TEST_TERRITORY_ID)
        );
        assertEquals(0L, productCount, "产品映射应该被删除");

        log.info("验证完成：所有相关数据已被正确删除");
    }
}

package com.dyd.di.requisition.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.dyd.common.security.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 采购_请购单对象 di_requisition_form
 *
 * <AUTHOR>
 * @date 2024-06-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DiRequisitionForm extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    /**
     * 请购单号
     */
    private String requisitionNo;

    /**
     * 请购来源：1.订单继承，2.手动创建
     */
    private String requisitionSource;

    /**
     * 请购类型：1.委外请购，2.外购请购，3.固定资产
     */
    private String requisitionType;

    /**
     * 状态：0.未提交审批，1.待审批，2.审批通过，3.审批驳回，4.已生成采购单
     */
    private String state;

    /**
     * 链路id
     */
    private String traceId;

}

package com.dyd.di.purchase.domain.dto;

import com.dyd.di.project.domain.BaseParam;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 采购_采购单DTO
 *
 * @Classname: FindDiPurchaseFormDto
 * @Author: cuichenglong
 * @Create: 2024-06-23 14:50
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FindDiPurchaseFormDto extends BaseParam {

    /**
     * 采购单号
     */
    private String purchaseNo;

    /**
     * 采购单主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 项目编号
     */
    private String projectNo;

    /**
     * 主键ID集合
     */
    private List<String> idList;

    /**
     * 创建人及下属层级工号List
     */
    private List<String> jobNumberList;

    /**
     * 创建人及下属层级工号StrSql
     */
    private String jobNumberStrSql;

}

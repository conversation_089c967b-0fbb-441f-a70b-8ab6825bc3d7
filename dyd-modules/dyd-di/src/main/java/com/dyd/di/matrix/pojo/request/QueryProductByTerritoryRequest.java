package com.dyd.di.matrix.pojo.request;

import com.dyd.common.core.web.page.PageDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
public class QueryProductByTerritoryRequest extends PageDomain {
    /**
     * 版图Id
     */
    private Long territoryId;
    /**
     * 产品分类
     */
    private Integer productCategoryId;
    /**
     * 产品名称
     */
    private String productName;

    private Map<String,String> requestMap;

    /**
     * int值
     * 自主产品:1
     * 代理产品:2
     * 外购产品:3
     */
    private Integer type;

    /**
     * 线索或者线索的ID
     */
    private String refId;


}

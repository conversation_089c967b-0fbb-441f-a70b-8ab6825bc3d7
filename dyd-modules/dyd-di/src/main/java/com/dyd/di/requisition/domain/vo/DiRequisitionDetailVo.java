package com.dyd.di.requisition.domain.vo;

import com.dyd.common.core.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;

/**
 * 采购_请购单明细VO
 *
 * @Classname: DiRequisitionDetailVo
 * @Author: cuichenglong
 * @Create: 2024-06-23 15:07
 */
@Data
public class DiRequisitionDetailVo {

    /**
     * 主键
     */
    private String id;

    /**
     * 请购单主键ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long requisitionId;

    /**
     * 采购计划表主键ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long purchasePlanId;

    /**
     * 计划类型：1.生产待配货，2.贸易订单
     */
    @Excel(name = "计划类型")
    private String planType;

    /**
     * 采购计划单号
     */
    @Excel(name = "采购计划单号")
    private String purchasePlanNo;

    /**
     * 项目ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long projectId;

    /**
     * 项目编码
     */
    @Excel(name = "项目编码")
    private String projectNo;

    /**
     * 期望交付日期
     */
    @Excel(name = "期望交付日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expectDeliveryDate;

    /**
     * 物料ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long materialId;

    /**
     * 物料编码
     */
    @Excel(name = "物料编码")
    private String materielNo;

    /**
     * 物料名称
     */
    @Excel(name = "物料名称")
    private String materielName;

    /**
     * 版本号
     */
    @Excel(name = "版本号")
    private String versionNo;

    /**
     * 品牌
     */
    @Excel(name = "品牌")
    private String brand;

    /**
     * 描述
     */
    @Excel(name = "描述")
    private String materielDescription;

    /**
     * 产品规格
     */
    @Excel(name = "产品规格")
    private String productStandard;

    /**
     * 需求数量
     */
    @Excel(name = "需求数量")
    private Integer demandQuantity;

    /**
     * 物料库存
     */
    @Excel(name = "物料库存")
    private Integer materialStock;

    /**
     * 计划需求总数
     */
    @Excel(name = "计划需求总数")
    private Integer planDemandTotal;

    /**
     * 待采购数量
     */
    @Excel(name = "待采购数量")
    private Integer waitPurchaseQuantity;

    /**
     * 在途数量
     */
    @Excel(name = "在途数量")
    private Integer transitQuantity;

    /**
     * 是否有bom：1.是，2.否
     */
    @Excel(name = "是否有bom")
    private String isBom;

    /**
     * 定制描述
     */
    @Excel(name = "定制描述")
    private String customizedDescribe;

    /**
     * 请购数量
     */
    @Excel(name = "请购数量")
    private Integer requisitionQuantity;

    /**
     * 链路id
     */
    private String traceId;

    /**
     * 创建者
     */
    @Excel(name = "创建者")
    private String createBy;

    /**
     * 创建者姓名
     */
    @Excel(name = "创建者姓名")
    private String createByName;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    @Excel(name = "更新者")
    private String updateBy;

    /**
     * 更新者姓名
     */
    @Excel(name = "更新者姓名")
    private String updateByName;

    /**
     * 更新时间
     */
    @Excel(name = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}

package com.dyd.di.pre.domain.request;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class PreSaleMaterielSelectionUpdateRequest {
    @ApiModelProperty("主键")
    @NotNull(message = "编号不能为空")
    private Long id;

    @ApiModelProperty("物料询价单主键ID")
    private Long diPreSaleId;

    @ApiModelProperty("指导物料货期")
    private BigDecimal guideMaterielDuration;

    @ApiModelProperty("选型说明")
    private String selectionExplain;

    @ApiModelProperty("选型附件")
    private String attachmentKey;

    @ApiModelProperty("物料指导单价")
    private BigDecimal guideCosts;

    @ApiModelProperty("物料ID")
    private Long materialId;

    @ApiModelProperty("物料号")
    private String materielNo;

    @ApiModelProperty("物料名称")
    private String materielName;

    @ApiModelProperty("品牌")
    private String brand;

    @ApiModelProperty("型号")
    private String patternNo;

    @ApiModelProperty("产品规格")
    private String productStandard;

    @ApiModelProperty("预计用量")
    private Integer consumeQuantity;

    @ApiModelProperty("报价")
    private BigDecimal quotedPrice;

    @ApiModelProperty("供应商")
    private String supplier;

    @ApiModelProperty("供应商id")
    private String supplierId;

    @ApiModelProperty("选型有效期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate validityDate;

    @ApiModelProperty("关联的报价明细ID")
    private Long materialQuotedDetailId;


    @ApiModelProperty("指导制作费")
    private BigDecimal guideProductionCosts;
    @ApiModelProperty("定制需求")

    private String customizedDemand;

}

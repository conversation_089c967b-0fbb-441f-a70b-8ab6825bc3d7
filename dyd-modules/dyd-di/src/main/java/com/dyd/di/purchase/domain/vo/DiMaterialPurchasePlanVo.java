package com.dyd.di.purchase.domain.vo;

import com.dyd.common.core.annotation.Excel;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * 物料采购计划
 */
@Data
public class DiMaterialPurchasePlanVo {

    /**
     * 物料ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long materialId;

    /**
     * 物料代码
     */
    @Excel(name = "物料代码")
    private String materielNo;

    /**
     * 版本号
     */
    @Excel(name = "版本号")
    private String versionNo;

    /**
     * 物料名称
     */
    @Excel(name = "物料名称")
    private String materielName;

    /**
     * 规格
     */
    @Excel(name = "规格")
    private String productStandard;

    /**
     * 品牌
     */
    @Excel(name = "品牌")
    private String brand;

    /**
     * 描述
     */
    @Excel(name = "描述")
    private String materielDescription;

    /**
     * 需求数量
     */
    @Excel(name = "需求数量")
    private Integer demandQuantity;

    /**
     * 库存数量
     */
    @Excel(name = "库存数量")
    private Integer materialStock;

    /**
     * 该物料计划需求总数
     */
    @Excel(name = "该物料计划需求总数")
    private Integer planDemandTotal;

    /**
     * 该总需求待采购数
     */
    @Excel(name = "该总需求待采购数")
    private Integer waitPurchaseQuantity;

    /**
     * 在途数量
     */
    @Excel(name = "在途数量")
    private Integer transitQuantity;

    /**
     * 是否包含定制化：1.是，2.否
     */
    @Excel(name = "是否包含定制化")
    private String isCustomized;

}

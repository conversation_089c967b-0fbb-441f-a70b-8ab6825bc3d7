package com.dyd.di.matrix.domain;

import com.dyd.common.core.annotation.Dict;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 业务版图保存/编辑
 */
@Data
public class MatrixTerritoryListResponse {

    /**
     * 主键
     */
    private Long id;


    /**
     * 分类：1:系统、2:组件、3:配件、4:零件、5:服务、6:工程
     */
    private Integer cateId;

    /**
     * 负责人
     */
    @Dict(type = Dict.DictType.EMPLOYEE)
    private String owner;

    /**
     * 负责人名字
     */
    private String ownerName;

    /**
     * 部门名称
     */

    private String deptName;

    /**
     * 所属部门
     */
    @Dict(type = Dict.DictType.DEPARTMENT)
    private String dept;

    /**
     * 等级
     */
    private Integer territoryLevel;

    /**
     * 版图领域
     */
    private Long territoryDomain;

    /**
     * 版图行业
     */
    private Long territoryIndustry;

    /**
     * 版图应用
     */
    private Long territoryApplication;

    /**
     * 版图对象
     */
    private Long territoryObject;

    /**
     * 版图工艺
     */
    private Long territoryProcess;

    /**
     * 业务名称
     */
    private String bizName;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 配件id
     */
    private Long territoryPart;

    /**
     * 配件名
     */
    private String territoryPartName;

    /**
     * 品类id
     */
    private Long territoryPartCategory;

    /**
     * 品类名
     */
    private String territoryPartCategoryName;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    private String territoryDomainName;
    private String territoryIndustryName;
    private String territoryApplicationName;
    private String territoryObjectName;
    private String territoryProcessName;

    /**
     * 品牌
     */
    private String brandName;
}

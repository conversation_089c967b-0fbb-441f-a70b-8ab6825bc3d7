package com.dyd.di.material.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dyd.common.core.enums.OssSysCodeEnum;
import com.dyd.common.core.exception.ServiceException;
import com.dyd.common.core.utils.PageHelp;
import com.dyd.common.core.utils.PageWrapper;
import com.dyd.di.material.constants.MaterialConstants;
import com.dyd.di.material.domain.dto.FindDiMaterialQuotedDetailDto;
import com.dyd.di.material.domain.dto.OperateMaterialQuotedContrastDTO;
import com.dyd.di.material.domain.dto.OperationQuotedDetailDto;
import com.dyd.di.material.domain.dto.UpdateDiMaterialQuotedDetailDto;
import com.dyd.di.material.domain.vo.DiMaterialQuotedDetailVo;
import com.dyd.di.material.entity.DiMaterialInquiryDetail;
import com.dyd.di.material.entity.DiMaterialQuotedDetail;
import com.dyd.di.material.entity.DiMaterialStockInfo;
import com.dyd.di.material.iservice.IDiMaterialInquiryDetailService;
import com.dyd.di.material.mapper.DiMaterialQuotedDetailMapper;
import com.dyd.di.material.service.CommonService;
import com.dyd.di.material.service.DiMaterialQuotedDetailService;
import com.dyd.di.materiel.domain.DiMateriel;
import com.dyd.di.oss.FileVo;
import com.dyd.di.oss.OssService;
import com.dydtec.base.oss.api.dto.response.OssPreviewDTO;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 采购_物料报价明细 Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-22
 */
@Service
@Slf4j
public class DiMaterialQuotedDetailServiceImpl extends ServiceImpl<DiMaterialQuotedDetailMapper, DiMaterialQuotedDetail> implements DiMaterialQuotedDetailService {

    @Resource
    private DiMaterialQuotedDetailMapper diMaterialQuotedDetailMapper;

    @Resource
    private IDiMaterialInquiryDetailService diMaterialInquiryDetailService;

    @Resource
    private IDiMaterialInquiryDetailService iDiMaterialInquiryDetailService;

    @Resource
    private CommonService commonService;

    @Autowired
    private OssService ossService;

    /**
     * 获取报价明细列表
     *
     * @param dto 查询条件DTO
     * @return VO集合
     */
    @Override
    public PageWrapper<List<DiMaterialQuotedDetailVo>> findQuotedDetailList(FindDiMaterialQuotedDetailDto dto) {
        Page<Object> page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<DiMaterialQuotedDetailVo> detailVoList = diMaterialQuotedDetailMapper.findQuotedDetailList(dto);
        if (CollectionUtil.isNotEmpty(detailVoList)) {
            //判断是新物料还是老的物料,老的物料没存物料信息,需要单独获取
            List<Long> materialIdList = detailVoList.stream().map(DiMaterialQuotedDetailVo::getMaterialId).distinct().toList();
            //获取物料信息
            Map<Long, DiMateriel> materielMap = commonService.getMaterielMapByIds(materialIdList);
            //获取库存信息
            Map<Long, DiMaterialStockInfo> stockInfoMap = commonService.getMaterialStockMapByIds(materialIdList);
//            //获取用户姓名
//            Map<String, String> userMap = commonService.getUserNameByJob(detailVoList.stream().map(DiMaterialQuotedDetailVo::getCreateBy).filter(StrUtil::isNotBlank).toList(),
//                    detailVoList.stream().map(DiMaterialQuotedDetailVo::getUpdateBy).filter(StrUtil::isNotBlank).toList());
            //构建返回数据
            detailVoList.forEach(detailVo -> {
                //附件处理
                if (StringUtils.isNotBlank(detailVo.getAttachmentKey())) {
                    List<String> fileList = Arrays.asList(detailVo.getAttachmentKey().split(","));
                    detailVo.setFileList(getOssFileInfo(fileList, OssSysCodeEnum.MATERIEL.getType()));
                }
                if (CollectionUtil.isNotEmpty(stockInfoMap) && stockInfoMap.containsKey(detailVo.getMaterialId())) {
                    detailVo.setMaterialStock(stockInfoMap.get(detailVo.getMaterialId()).getMaterialStock());
                }
                if (CollectionUtil.isNotEmpty(materielMap) && materielMap.containsKey(detailVo.getMaterialId())) {
                    detailVo.setMaterielNo(materielMap.get(detailVo.getMaterialId()).getMaterielNo());
                    detailVo.setMaterielName(materielMap.get(detailVo.getMaterialId()).getMaterielName());
                    detailVo.setBrand(materielMap.get(detailVo.getMaterialId()).getBrand());
                    detailVo.setPatternNo(materielMap.get(detailVo.getMaterialId()).getPatternNo());
                    detailVo.setProductStandard(materielMap.get(detailVo.getMaterialId()).getProductStandard());
                    detailVo.setMaterielProperty(materielMap.get(detailVo.getMaterialId()).getMaterielProperty());
                    detailVo.setMaterialId(detailVo.getMaterialId());
                    detailVo.setMaterielVersionNo(materielMap.get(detailVo.getMaterialId()).getShowVersionNo());
                }
//                if (CollectionUtil.isNotEmpty(userMap) && userMap.containsKey(detailVo.getCreateBy())) {
//                    detailVo.setCreateByName(userMap.get(detailVo.getCreateBy()));
//                }
//                if (CollectionUtil.isNotEmpty(userMap) && userMap.containsKey(detailVo.getUpdateBy())) {
//                    detailVo.setUpdateByName(userMap.get(detailVo.getUpdateBy()));
//                }
                if (CollectionUtil.isNotEmpty(detailVo.getQuotedOssKeyList())) {
                    detailVo.setFileVoList(getOssFileInfo(detailVo.getQuotedOssKeyList(), OssSysCodeEnum.INQUIRY_FORM.getType()));
                }
            });
        }
        return PageHelp.render(page, detailVoList);
    }

    /**
     * 根据OSS文件key获取OSS文件信息
     *
     * @param ossFileKeyList oss文件key
     * @return 文件信息
     */
    private List<FileVo> getOssFileInfo(List<String> ossFileKeyList, String type) {
        List<OssPreviewDTO> previewDTOList = ossService.getOssFileByList(type, ossFileKeyList, 0);
        if (CollectionUtil.isEmpty(previewDTOList)) {
            log.error("DiMaterialQuotedDetailServiceImpl---getOssFileInfo()---根据文件KEY未获取OSS文件信息，文件KEY：{}，文件类型：{}", JSONUtil.toJsonStr(ossFileKeyList), type);
            return new ArrayList<>();
        }
        List<FileVo> fileVoList = new ArrayList<>();
        previewDTOList.forEach(item -> {
            FileVo fileVo = new FileVo();
            fileVo.setFileName(item.getMimeName());
            fileVo.setFileUrl(item.getShowUrl());
            fileVo.setIsPic(item.getMimeType().contains("image") ? 1 : 0);
            fileVo.setFileKey(item.getOssKey());
            fileVoList.add(fileVo);
        });
        return fileVoList;
    }

    /**
     * 修改报价单
     *
     * @param dto 修改DTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editQuotedDetail(UpdateDiMaterialQuotedDetailDto dto) {
        //获取报价明细
        DiMaterialQuotedDetail quotedDetail = diMaterialQuotedDetailMapper.selectById(dto.getId());
        if (null == quotedDetail) {
            throw new ServiceException("报价单不存在");
        }
        quotedDetail.setQuotedPrice(new BigDecimal(dto.getQuotedPrice()));
        quotedDetail.setDeliveryTime(dto.getDeliveryTime());
        quotedDetail.setSupplier(dto.getSupplier());
        diMaterialQuotedDetailMapper.updateById(quotedDetail);
        //判断是不是供应商
        if (MaterialConstants.STR_ONE.equals(dto.getIsChoice())) {
            //同步修改询价单的明细
            DiMaterialInquiryDetail inquiryDetail = diMaterialInquiryDetailService.getById(quotedDetail.getMaterialInquiryDetailId());
            if (null == inquiryDetail) {
                throw new ServiceException("询价单不存在");
            }
//            inquiryDetail.setQuotedPrice(new BigDecimal(dto.getQuotedPrice()));
            inquiryDetail.setDeliveryTime(dto.getDeliveryTime());
            inquiryDetail.setSupplier(dto.getSupplier());
            inquiryDetail.setMaterialQuotedDetailId(Long.valueOf(quotedDetail.getId()));
            diMaterialInquiryDetailService.updateById(inquiryDetail);
        }
    }

    /**
     * 比价新增报价明细
     *
     * @param dto 比价新增DTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void contrastSaveQuotedDetail(OperationQuotedDetailDto dto) {
        //获取报价明细信息
        DiMaterialQuotedDetail quotedDetail = diMaterialQuotedDetailMapper.selectById(dto.getId());
        if (null == quotedDetail) {
            throw new ServiceException("报价单不存在");
        }
        //构建新增信息
        quotedDetail.setId(null);
        //比价新增的数据一定不是原始数据
        quotedDetail.setIsRawData(MaterialConstants.STR_TWO);
        quotedDetail.setQuotedPrice(null);
        quotedDetail.setDeliveryTime(null);
        quotedDetail.setSupplier(null);
        diMaterialQuotedDetailMapper.insert(quotedDetail);
    }

    /**
     * 删除报价明细
     *
     * @param dto 操作DTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteQuotedDetail(OperationQuotedDetailDto dto) {
        //获取报价明细信息
        DiMaterialQuotedDetail quotedDetail = diMaterialQuotedDetailMapper.selectById(dto.getId());
        if (null == quotedDetail || quotedDetail.getIsRawData().equals(MaterialConstants.STR_ONE)) {
            throw new ServiceException("原始数据不能删除");
        }
        //查询报价是否已经绑定询价明细(已同步到询价单)
        List<DiMaterialInquiryDetail> inquiryDetailList = iDiMaterialInquiryDetailService.list(new LambdaQueryWrapper<DiMaterialInquiryDetail>()
                .eq(DiMaterialInquiryDetail::getMaterialQuotedDetailId, quotedDetail.getId()));
        if (CollectionUtil.isNotEmpty(inquiryDetailList)) {
            throw new ServiceException("比价单已选择供应商，不允许删除");
        }
        quotedDetail.setIsDeleted(Integer.valueOf(MaterialConstants.STR_ONE));
        diMaterialQuotedDetailMapper.updateById(quotedDetail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchEditQuotedDetail(List<UpdateDiMaterialQuotedDetailDto> dtoList) {
        Map<Long, UpdateDiMaterialQuotedDetailDto> idMap = dtoList.stream().collect(Collectors.toMap(UpdateDiMaterialQuotedDetailDto::getId, Function.identity()));
        List<DiMaterialQuotedDetail> quotedDetailList = diMaterialQuotedDetailMapper.selectBatchIds(idMap.keySet());
        if (CollectionUtil.isEmpty(quotedDetailList)) {
            throw new ServiceException("报价明细为空");
        }
        if (quotedDetailList.size() != dtoList.size()) {
            throw new ServiceException("报价明细不正确");
        }
        quotedDetailList.forEach(quotedDetail -> {
            UpdateDiMaterialQuotedDetailDto updateDto = idMap.get(Long.valueOf(quotedDetail.getId()));
            quotedDetail.setSupplier(updateDto.getSupplierName());
            quotedDetail.setGuideProductionCosts(updateDto.getGuideMakeCost());
            quotedDetail.setDeliveryTime(updateDto.getGuideDeliveryDate());
            quotedDetail.setValidityDate(updateDto.getQuotedEffectiveDate());
//            quotedDetail.setCurrencyName(updateDto.getCurrencyName());
//            quotedDetail.setCurrencyType(updateDto.getCurrencyType());
//            quotedDetail.setPriceCoefficient(updateDto.getPriceCoefficient());
//            quotedDetail.setDeliveryTimeCoefficient(updateDto.getDeliveryTimeCoefficient());
            if (CollectionUtil.isNotEmpty(updateDto.getQuotedOssKeyList())) {
                quotedDetail.setQuotedOssKeyList(updateDto.getQuotedOssKeyList());
            } else {
                quotedDetail.setQuotedOssKeyList(new ArrayList<>());
            }
            if (StringUtils.isNotBlank(updateDto.getQuotedRemarks())) {
                quotedDetail.setQuotedRemarks(updateDto.getQuotedRemarks());
            } else {
                quotedDetail.setQuotedRemarks("");
            }
        });
        this.updateBatchById(quotedDetailList);
    }

}

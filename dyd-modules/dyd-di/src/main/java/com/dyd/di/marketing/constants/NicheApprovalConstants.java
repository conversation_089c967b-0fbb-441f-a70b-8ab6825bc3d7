package com.dyd.di.marketing.constants;

/**
 * 商机审批常量类
 */
public class NicheApprovalConstants {

    public final static String STR_ZERO = "0";

    public final static String STR_ONE = "1";

    public final static String STR_TWO = "2";

    public final static String STR_THREE = "3";

    public final static String STR_FOUR = "4";

    public final static String STR_FIVE = "5";

    public final static String STR_SIX = "6";

    public static final String DELIMITER_COLON = ":";

    public static final String DELIMITER_BAR = "-";

    /**
     * 可行性验证锁
     */
    public final static String FEASIBILITY_VERIFY_LOCK = "feasibility_verify_lock";

    /**
     * 订单项目交付日期变更锁
     */
    public final static String POD_CHANGE_LOCK = "pod_change_lock";

    /**
     * 可行性-商务审批流程KEY
     */
    public final static String FEASIBILITY_BUSINESS_APPROVAL_PROCESS = "feasibility_business_approval_process";

    /**
     * 可行性-技术审批流程KEY
     */
    public final static String FEASIBILITY_TECHNIQUE_APPROVAL_PROCESS = "feasibility_technique_approval_process";

    /**
     * 技术支持主管角色KEY
     */
    public final static String TECHNICAL_SUPPORT_MANAGE_ROLE_KEY = "technicalSupportManageRole";

    /**
     * 技术总监角色KEY
     */
    public final static String TECHNICAL_DIRECTOR_ROLE_KEY = "technicalDirectorRole";

    /**
     * CTO角色KEY
     */
    public final static String CTO_ROLE_KEY = "ctoRole";

    /**
     * CEO角色KEY
     */
    public final static String CEO_ROLE_KEY = "ceoRole";

    /**
     * 技术难度KEY
     */
    public final static String TECHNICAL_DIFFICULTY = "technicalDifficulty";

    /**
     * 可行性验证-商务审批流关闭原因
     */
    public final static String BUSINESS_APPROVAL_ERROR_TECHNICAL_APPROVAL_ADD_ERROR = "可行性验证-技术审批流创建失败，将同时启动的可行性验证-商务审批流关闭";

    /**
     * 可行性验证-技术审批流关闭原因
     */
    public final static String TECHNICAL_APPROVAL_ERROR_BUSINESS_APPROVAL_ADD_ERROR = "可行性验证-商务审批流创建失败，将同时启动的可行性验证-技术审批流关闭";

    /**
     * 可行性验证-商务审批流关闭原因
     */
    public final static String BUSINESS_APPROVAL_ERROR_TECHNICAL_REFUSE_ERROR = "可行性验证-技术审批拒绝，将可行性验证-商务审批流关闭";

    /**
     * 可行性验证-技术审批流关闭原因
     */
    public final static String TECHNICAL_APPROVAL_ERROR_BUSINESS_REFUSE_ERROR = "可行性验证-商务审批拒绝，将可行性验证-技术审批流关闭";

    /**
     * 可行性验证-商务审批流关闭原因
     */
    public final static String BUSINESS_APPROVAL_ERROR_CANCEL = "可行性验证-商务审批，手动取消";

    /**
     * 可行性验证-技术审批流关闭原因
     */
    public final static String TECHNICAL_APPROVAL_ERROR_CANCEL = "可行性验证-技术审批，手动取消";

    /**
     * 可行性验证-商务审批流关闭原因
     */
    public final static String BUSINESS_APPROVAL_ERROR_DISCARD_CANCEL = "商机丢单或弃单关闭可行性验证-商务审批";

    /**
     * 可行性验证-技术审批流关闭原因
     */
    public final static String TECHNICAL_APPROVAL_ERROR_DISCARD_CANCEL = "商机丢单或弃单关闭可行性验证-技术审批";

    /**
     * 可行性验证-商务审批结果MQ消费组
     */
    public static final String FR_BUSINESS_APPROVAL_RESULT_GROUP = "fr_business_approval_result_group";

    /**
     * 可行性验证-技术审批结果MQ消费组
     */
    public static final String FR_TECHNIQUE_APPROVAL_RESULT_GROUP = "fr_technique_approval_result_group";

}

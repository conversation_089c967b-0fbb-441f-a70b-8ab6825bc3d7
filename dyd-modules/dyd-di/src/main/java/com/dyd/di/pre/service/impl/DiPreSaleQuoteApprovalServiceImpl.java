package com.dyd.di.pre.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dyd.common.core.enums.PreSaleQuoteStatusEnum;
import com.dyd.common.core.exception.ServiceException;
import com.dyd.common.security.utils.SecurityUtils;
import com.dyd.di.agency.constants.AgencyConstants;
import com.dyd.di.agency.domain.dto.AgencyTaskInfoDTO;
import com.dyd.di.agency.entity.DiAgencyApproval;
import com.dyd.di.agency.enums.AgencyTaskTypeEnum;
import com.dyd.di.agency.enums.ApprovalTypeEnum;
import com.dyd.di.agency.service.DiAgencyApprovalService;
import com.dyd.di.approval.entity.DiApprovalLog;
import com.dyd.di.approval.service.DiApprovalLogService;
import com.dyd.di.contract.constants.ContractConstants;
import com.dyd.di.marketing.domain.DiMarketingNiche;
import com.dyd.di.marketing.service.IDiMarketingNicheService;
import com.dyd.di.materiel.pojo.dto.MaterielChecklistNewDTO;
import com.dyd.di.message.domain.DiMessageList;
import com.dyd.di.message.service.IDiMessageListService;
import com.dyd.di.pre.domain.DiPreSaleQuoteEstimatingGp;
import com.dyd.di.pre.domain.DiPreSaleQuoteGpGroup;
import com.dyd.di.pre.domain.request.ApprovalProcessRequest;
import com.dyd.di.pre.domain.request.ApprovalResultRequest;
import com.dyd.di.pre.entity.DiPreSale;
import com.dyd.di.pre.entity.DiPreSaleQuote;
import com.dyd.di.pre.enums.PreSaleTypeEnum;
import com.dyd.di.pre.enums.SaleOfferDingTalkEnum;
import com.dyd.di.pre.mapper.DiPreSaleQuoteMapper;
import com.dyd.di.pre.service.DiPreSaleQuoteApprovalService;
import com.dyd.di.pre.service.DiPreSaleQuoteGpService;
import com.dyd.di.pre.service.IDiPreSaleService;
import com.dyd.di.pre.service.IPreSaleQuoteService;
import com.dyd.system.api.RemoteUserService;
import com.dydtec.base.camunda.api.domain.dto.ApprovalTaskDTO;
import com.dydtec.base.camunda.api.domain.dto.CheckUserPermissionDTO;
import com.dydtec.base.camunda.api.domain.dto.StartProcessDTO;
import com.dydtec.base.camunda.api.feign.IProcessClient;
import com.dydtec.base.camunda.api.feign.IProcessTaskClient;
import com.dydtec.infras.core.base.bean.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 销售报价单审批实现类
 */
@Slf4j
@Service
public class DiPreSaleQuoteApprovalServiceImpl extends ServiceImpl<DiPreSaleQuoteMapper, DiPreSaleQuote> implements DiPreSaleQuoteApprovalService {

    @Resource
    private DiPreSaleQuoteMapper diPreSaleQuoteMapper;

    @Resource
    private IProcessClient processClient;

    @Resource
    private RemoteUserService remoteUserService;

    @Resource
    private IProcessTaskClient processTaskClient;

    @Resource
    private DiAgencyApprovalService diAgencyApprovalService;

    @Resource
    private DiApprovalLogService diApprovalLogService;

    @Resource
    private IDiMarketingNicheService iDiMarketingNicheService;

    @Resource
    private IDiMessageListService iDiMessageListService;

    @Resource
    private DiPreSaleQuoteGpService diPreSaleQuoteGpService;

    @Value("${camunda.sale.director.role.id}")
    private String saleDirectorRoleId;

    @Value("${camunda.sales.director.role.id}")
    private String salesDirectorRoleId;

    @Value("${camunda.finance.director.role.id}")
    private String financeDirectorRoleId;

    @Value("${camunda.general.office.role.id}")
    private String generalOfficeRoleId;

    @Value("${camunda.export.business.role.id}")
    private String exportBusinessRoleId;

    @Value("${environment.url}")
    private String environmentUrl;

    @Autowired
    private IDiPreSaleService iDiPreSaleService;

    @Autowired
    private IPreSaleQuoteService preSaleQuoteService;

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    /**
     * 启动审批流程
     *
     * @param request 流程参数
     */
    @Override
    public void startApprovalProcess(ApprovalProcessRequest request) {
        //获取销售报价单数据
        DiPreSaleQuote preSaleQuote = diPreSaleQuoteMapper.selectOne(new LambdaQueryWrapper<DiPreSaleQuote>().eq(DiPreSaleQuote::getPreSaleQuoteCode, request.getPreSaleQuoteCode()));
        if (null == preSaleQuote) {
            throw new ServiceException("销售报价单不存在");
        }
        //增加校验
        DiMarketingNiche diMarketingNicheT = iDiMarketingNicheService.selectDiMarketingNicheByNo(preSaleQuote.getNicheCode());
        if (Objects.isNull(diMarketingNicheT)) {
            throw new RuntimeException("商机不存在");
        }
        if (CollectionUtil.isEmpty(diMarketingNicheT.getDiMarketingContactsList())) {
            throw new RuntimeException("客户联系人不存在");
        }
        List<String> contactInfoList = diMarketingNicheT.getDiMarketingContactsList().stream().map(contacts -> contacts.getContactsName() + "-" + contacts.getContactsPhone()).toList();
        if (!contactInfoList.contains(preSaleQuote.getContactsName() + "-" + preSaleQuote.getContactsPhone())) {
            throw new RuntimeException("客户联系人不正确");
        }
        if (StringUtils.isBlank(SecurityUtils.getUsername()) || null == SecurityUtils.getLoginUser().getSysUser()) {
            throw new ServiceException("用户信息不正确");
        }
        //判断状态
        if (preSaleQuote.getApprovalStatus().equals(1) || preSaleQuote.getApprovalStatus().equals(2)) {
            throw new ServiceException("当前状态不允许发起审批");
        }
        //更新报价单数据
        LambdaUpdateChainWrapper<DiPreSaleQuote> updateWrapper = new LambdaUpdateChainWrapper<>(diPreSaleQuoteMapper);
        updateWrapper.eq(DiPreSaleQuote::getId, preSaleQuote.getId());


        List<DiPreSale> preSales = preSaleQuoteService.queryPreSaleList(preSaleQuote.getId());
        //Boolean isTrade = PreSaleTypeEnum.TRADE.getCode().equals(preSales.get(0).getPreSaleType());
        var tradePreSaleIdList = preSales.stream().filter(x -> x.getPreSaleType().equals(PreSaleTypeEnum.TRADE.getCode())).map(DiPreSale::getId).toList();
        //判断毛利率
        DiPreSaleQuoteGpGroup estimatingGp = diPreSaleQuoteGpService.queryByQuoteId(Long.valueOf(preSaleQuote.getId()), CollectionUtils.isNotEmpty(tradePreSaleIdList));
        if (null == estimatingGp || null == estimatingGp.getSummaryEstimating().getGp31EstimatingGrossProfitRate()) {
            throw new ServiceException("发起失败!GP3.1测算毛利率为空");
        }
        Set<Long> badPreSaleQuoteIdSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(tradePreSaleIdList)) {
            List<MaterielChecklistNewDTO> list = iDiPreSaleService.queryTobeQuotedMaterialList(tradePreSaleIdList, badPreSaleQuoteIdSet);
            if (CollectionUtils.isNotEmpty(list)) {
                String preSaleQuoteCodes = preSales.stream().filter(x -> badPreSaleQuoteIdSet.contains(x.getId()))
                        .map(DiPreSale::getPreSaleCode).collect(Collectors.joining(","));
                throw new ServiceException("发起失败!方案[" + preSaleQuoteCodes + "]尚有物料价格、货期或有效期不合法，请询价后再试");
            }
        }
        List<DiPreSale> preSaleList = preSaleQuoteService.hasDeliveryReviewPreSale(preSaleQuote.getId());
        if (CollectionUtils.isNotEmpty(preSaleList)) {
            String codeList = preSaleList.stream().map(DiPreSale::getPreSaleCode).collect(Collectors.joining(","));
            throw new ServiceException("方案【" +codeList + "】未完成交期复核,无法报价，请完成交期复核");
        }
        /**
         * 判断是否通过货期校验
         * 如果货期校验不通过，则校验是否做过交期复核，如果做过，则通过
         */
        if (!preSaleQuoteService.passDeliveryTime(preSaleQuote)) {
            throw new ServiceException("期望交期与核算交期不符，无法报价，请发起交期复核");
        }
        //}
        //暂存 标品与贸易类数据
        //iDiPreSaleService.saveFees(preSales);
        preSaleQuote.setCalculateDeliveryWeek(preSaleQuoteService.queryCalculateDeliveryTimeUnitWeek(preSales));
        BigDecimal gP31EstimatingGrossProfitRate = estimatingGp.getSummaryEstimating().getGp31EstimatingGrossProfitRate().multiply(new BigDecimal("100"));
        log.info("DiPreSaleQuoteApprovalServiceImpl---startApprovalProcess()---发起销售报价单审批，销售报价单编号：{}，GP3.1测算毛利率：{}", preSaleQuote.getPreSaleQuoteCode(), gP31EstimatingGrossProfitRate);
//        if (gP31EstimatingGrossProfitRate.compareTo(new BigDecimal("40")) >= 0) {
//            //毛利率大于等于40，直接审批通过
//            updateWrapper.set(DiPreSaleQuote::getApprovalStatus, 2);
//            updateWrapper.set(DiPreSaleQuote::getQuoteStatus, PreSaleQuoteStatusEnum.QUOTED.getCode());
//            updateWrapper.set(DiPreSaleQuote::getCalculateDeliveryWeek, preSaleQuote.getCalculateDeliveryWeek());
//            //if (preSaleQuote.getFinallyDeliveryWeek() == null) {
//            updateWrapper.set(DiPreSaleQuote::getFinallyDeliveryWeek, preSaleQuoteService.queryAfterCheckDeliveryTimeUnitWeek(preSales));
//            //}
//            updateWrapper.update();
//            return;
//        }
        Integer processType = getProcessType(gP31EstimatingGrossProfitRate);
        log.info("DiPreSaleQuoteApprovalServiceImpl---startApprovalProcess()---发起销售报价单审批，销售报价单编号：{}，流程类型：{}", preSaleQuote.getPreSaleQuoteCode(), processType);
        //判断是国内还是国外
        boolean isInChina = "C04".equals(preSaleQuote.getCountryCode());
        StartProcessDTO costProcessDto = getStartProcessDTO(preSaleQuote, processType, isInChina);
        BaseResponse<String> costResponse = processClient.startProcess(costProcessDto);
        if (null == costResponse || !costResponse.isSuccess()) {
            throw new ServiceException("创建成本审批流失败");
        }
        if (!processType.equals(0) || !isInChina) {
            updateWrapper.set(DiPreSaleQuote::getApprovalStatus, 1);
            updateWrapper.set(DiPreSaleQuote::getQuoteStatus, PreSaleQuoteStatusEnum.APPROVALING.getCode());
        }
        updateWrapper.set(DiPreSaleQuote::getCostApprovalProcessId, costResponse.getData());
        updateWrapper.set(DiPreSaleQuote::getCompanyName, diMarketingNicheT.getCompanyName());
        updateWrapper.set(DiPreSaleQuote::getCalculateDeliveryWeek, preSaleQuote.getCalculateDeliveryWeek());
        //if (preSaleQuote.getFinallyDeliveryWeek() == null) {
        updateWrapper.set(DiPreSaleQuote::getFinallyDeliveryWeek, preSaleQuoteService.queryAfterCheckDeliveryTimeUnitWeek(preSales));
        updateWrapper.update();
    }

    private Integer getProcessType(BigDecimal gP31EstimatingGrossProfitRate) {
        if (gP31EstimatingGrossProfitRate.compareTo(new BigDecimal("40")) >= 0) {
            return 0;
        }
        if (gP31EstimatingGrossProfitRate.compareTo(new BigDecimal("40")) < 0
                && gP31EstimatingGrossProfitRate.compareTo(new BigDecimal("20")) > 0) {
            return 3;
        }
        if (gP31EstimatingGrossProfitRate.compareTo(new BigDecimal("20")) <= 0) {
            if (gP31EstimatingGrossProfitRate.compareTo(new BigDecimal("0")) <= 0) {
                return 1;
            } else {
                return 2;
            }
        }
        throw new ServiceException("GP3.1测算毛利率计算错误");
    }

    /**
     * 构建启动审批流程需要参数
     *
     * @param preSaleQuote 销售报价单
     * @return 启动参数
     */
    private StartProcessDTO getStartProcessDTO(DiPreSaleQuote preSaleQuote, Integer processType, Boolean isInChina) {
        StartProcessDTO costProcessDto = new StartProcessDTO();
        costProcessDto.setProcessDefinitionKey(ContractConstants.NEW_SALE_OFFER_COST_APPROVAL_PROCESS);
        costProcessDto.setBusinessKey(preSaleQuote.getPreSaleQuoteCode());
        Map<String, Object> costMap = new HashMap<>(7);
//        //整单毛利率，需乘以100获取利率
//        BigDecimal intactGrossMargin = preSaleQuote.getGrossMarginTotal().multiply(new BigDecimal(ContractConstants.ONE_HUNDRED));
        costMap.put(ContractConstants.IS_IN_CHINA, isInChina);
        costMap.put(ContractConstants.EXPORT_BUSINESS_ROLE, exportBusinessRoleId);
        costMap.put(ContractConstants.PROCESS_TYPE_KEY, processType);
        costMap.put(ContractConstants.LAUNCH_BY, SecurityUtils.getUsername());
        costMap.put(ContractConstants.SALE_DIRECTOR_ROLE_KEY, saleDirectorRoleId);
        costMap.put(ContractConstants.SALES_DIRECTOR_ROLE, salesDirectorRoleId);
        costMap.put(ContractConstants.GENERAL_OFFICE_ROLE_KEY, generalOfficeRoleId);
        //发起人姓名
        String launchByName = "";
        if (StringUtils.isNotBlank(SecurityUtils.getLoginUser().getSysUser().getNickName())) {
            launchByName = SecurityUtils.getLoginUser().getSysUser().getNickName();
        }
        costMap.put(ContractConstants.LAUNCH_BY_NAME, launchByName);
        costProcessDto.setVariableMap(costMap);
        return costProcessDto;
    }

    /**
     * 校验当前登录者是否有审批权限
     *
     * @param request 流程参数
     * @return 结果-true：有审批权限；false：没有审批权限
     */
    @Override
    public Boolean checkApprovalBy(ApprovalProcessRequest request) {
        //获取当前登录者
        if (null == SecurityUtils.getLoginUser() || null == SecurityUtils.getLoginUser().getSysUser()) {
            throw new ServiceException("用户信息不正确");
        }
        //获取登录用户角色权限
        String userId = SecurityUtils.getLoginUser().getSysUser().getUserId().toString();
        List<Long> roleIdList = remoteUserService.getUserRoleId(userId);
        if (CollectionUtil.isEmpty(roleIdList)) {
            log.error("DiPreSaleQuoteApprovalServiceImpl---checkApprovalBy()---当前用户未绑定角色，用户ID：{}", userId);
            return false;
        }
        log.info("DiPreSaleQuoteApprovalServiceImpl---checkApprovalBy()---当前用户绑定的角色ID：{}", JSONUtil.toJsonStr(roleIdList));
        //获取销售报价单数据
        DiPreSaleQuote preSaleQuote = diPreSaleQuoteMapper.selectOne(new LambdaQueryWrapper<DiPreSaleQuote>().eq(DiPreSaleQuote::getPreSaleQuoteCode, request.getPreSaleQuoteCode()));
        if (null == preSaleQuote) {
            throw new ServiceException("销售报价单不存在");
        }
        if (StringUtils.isBlank(preSaleQuote.getCostApprovalProcessId())) {
            throw new ServiceException("报价单数据不正确，未获取到流程实例");
        }
        //构建校验用户审批权限入参DTO
        List<String> roleIdStrList = roleIdList.stream().map(Object::toString).distinct().toList();
        CheckUserPermissionDTO dto = new CheckUserPermissionDTO();
        dto.setBusinessKey(preSaleQuote.getPreSaleQuoteCode());
        dto.setProcessInstanceId(preSaleQuote.getCostApprovalProcessId());
        dto.setApprovalBy(userId);
        dto.setApprovalRoleId(roleIdStrList);
        BaseResponse<Boolean> response = processTaskClient.checkUserPermission(dto);
        if (null == response || !response.isSuccess()) {
            log.info("DiPreSaleQuoteApprovalServiceImpl---checkApprovalBy()---校验用户审批权限失败");
            return false;
        }
        return response.getData();
    }

    /**
     * 报销单审批
     *
     * @param request 审批参数
     */
    @Override
    public void saleOfferApproval(ApprovalResultRequest request) {
        //获取销售报价单数据
        DiPreSaleQuote preSaleQuote = diPreSaleQuoteMapper.selectOne(new LambdaQueryWrapper<DiPreSaleQuote>().eq(DiPreSaleQuote::getPreSaleQuoteCode, request.getPreSaleQuoteCode()));
        if (null == preSaleQuote) {
            throw new ServiceException("销售报价单不存在");
        }
        if (StringUtils.isBlank(preSaleQuote.getCamundaTaskId())) {
            throw new ServiceException("销售报价单信息有误");
        }
        //根据合同存储的任务ID获取任务信息
        List<DiAgencyApproval> taskList = diAgencyApprovalService.list(new LambdaQueryWrapper<DiAgencyApproval>().eq(DiAgencyApproval::getCamundaTaskId, preSaleQuote.getCamundaTaskId()));
        if (CollectionUtil.isEmpty(taskList)) {
            throw new ServiceException("未获取到待审批任务");
        }
        String userId = SecurityUtils.getLoginUser().getSysUser().getUserId().toString();
        //获取当前登录用户所有角色
        List<Long> roleIdList = remoteUserService.getUserRoleId(userId);
        if (CollectionUtil.isEmpty(roleIdList)) {
            throw new ServiceException("当前登录者没有权限");
        }
        //获取代办审批角色
        String approvalRoleId = taskList.get(ContractConstants.ZERO).getLiabilityRoleId();
        //校验登录者是否有权限审批
        List<String> roleIdStrList = roleIdList.stream().map(Object::toString).distinct().toList();
        if (!roleIdStrList.contains(approvalRoleId)) {
            throw new ServiceException("当前登录者没有审批权限");
        }
        //构建审批任务入参DTO
        ApprovalTaskDTO approvalTaskDto = new ApprovalTaskDTO();
        approvalTaskDto.setTaskId(preSaleQuote.getCamundaTaskId());
        approvalTaskDto.setApprovalResult(request.getApprovalResult());
        if (StringUtils.isNotBlank(request.getApprovalRemark())) {
            approvalTaskDto.setRejectReason(request.getApprovalRemark());
        } else {
            approvalTaskDto.setRejectReason("");
        }
        approvalTaskDto.setApprovalBy(SecurityUtils.getUsername());
        approvalTaskDto.setApprovalRoleId(approvalRoleId);
        //调用审批流
        BaseResponse<Void> response = processTaskClient.approvalTask(approvalTaskDto);
        //判断是否成功
        if (null == response) {
            throw new ServiceException("销售报价单审批失败");
        }
        if (response.isSuccess()) {
            //成功后修改代办审批状态
            taskList.forEach(task -> task.setIsDeleted(ContractConstants.STR_ONE));
            //新增审批日志
            DiApprovalLog diApprovalLog = getDiApprovalLog(request, preSaleQuote, taskList.get(0).getLiabilityRoleId());
            diAgencyApprovalService.updateBatchById(taskList);
            diApprovalLogService.save(diApprovalLog);
            //审批通过发送钉钉消息
            approvalSendDingTalkMessage(request, preSaleQuote, taskList.get(0).getLaunchBy(), taskList.get(0));
        } else {
            throw new ServiceException(response.getMsg());
        }
    }

    /**
     * 构建审批日志对象
     *
     * @param request        请求参数
     * @param preSaleQuote   销售报价单信息
     * @param approvalByRole 审批人角色
     * @return 审批日志对象
     */
    private static DiApprovalLog getDiApprovalLog(ApprovalResultRequest request, DiPreSaleQuote preSaleQuote, String approvalByRole) {
        DiApprovalLog diApprovalLog = new DiApprovalLog();
        diApprovalLog.setBusinessKey(preSaleQuote.getPreSaleQuoteCode());
        diApprovalLog.setApprovalType(ApprovalTypeEnum.SALE_OFFER_COST_APPROVAL.getTypeDesc());
        diApprovalLog.setApprovalTypeCode(ApprovalTypeEnum.SALE_OFFER_COST_APPROVAL.getTypeCode());
        diApprovalLog.setApprovalBy(SecurityUtils.getUsername());
        diApprovalLog.setApprovalByRole(approvalByRole);
        boolean approvalResult = null == request.getApprovalResult() || request.getApprovalResult();
        if (approvalResult) {
            diApprovalLog.setApprovalResult(ContractConstants.STR_ONE);
        } else {
            diApprovalLog.setApprovalResult(ContractConstants.STR_TWO);
        }
        if (StringUtils.isNotBlank(request.getApprovalRemark())) {
            diApprovalLog.setRemark(request.getApprovalRemark());
        }
        return diApprovalLog;
    }

    /**
     * 审批结果发送钉钉消息
     *
     * @param request      请求参数
     * @param preSaleQuote 销售报价单信息
     * @param launchBy     审批发起人
     */
    private void approvalSendDingTalkMessage(ApprovalResultRequest request, DiPreSaleQuote preSaleQuote, String launchBy, DiAgencyApproval agencyApproval) {
        if (StringUtils.isBlank(launchBy)) {
            log.error("DiPreSaleQuoteApprovalServiceImpl---approvalSendDingTalkMessage()---审批发起人为空，销售报价单编号：{}", preSaleQuote.getPreSaleQuoteCode());
            return;
        }
        DiMessageList diMessageList = new DiMessageList();
        diMessageList.setBusinessModule("销售报价单");
        String title = StrUtil.format(SaleOfferDingTalkEnum.SALE_OFFER_COST_APPROVAL_ADOPT.getTitle(), preSaleQuote.getPreSaleQuoteCode());
        diMessageList.setTitle(title);
        diMessageList.setSendingTime(new Date());
        diMessageList.setRemarks(diMessageList.getTitle());
        String content;
        boolean approvalResult = null == request.getApprovalResult() || request.getApprovalResult();
        if (approvalResult) {
            content = StrUtil.format(SaleOfferDingTalkEnum.SALE_OFFER_COST_APPROVAL_ADOPT.getMessage(), preSaleQuote.getPreSaleQuoteCode(), SecurityUtils.getLoginUser().getSysUser().getNickName());
        } else {
            content = StrUtil.format(SaleOfferDingTalkEnum.SALE_OFFER_COST_APPROVAL_REFUSE.getMessage(), preSaleQuote.getPreSaleQuoteCode(), SecurityUtils.getLoginUser().getSysUser().getNickName());
        }
        diMessageList.setContent(content);
        diMessageList.setSendingUser(launchBy);
        String jumpPathArg = null;
        try {
            jumpPathArg = "&businessKey=" + agencyApproval.getBusinessKey() +
                    "&jumpKey=" + agencyApproval.getJumpKey() +
                    "&approvalTypeCode=" + agencyApproval.getApprovalTypeCode() +
                    "&agencyType=2";
        } catch (Exception e) {
            log.error("DiPreSaleQuoteApprovalServiceImpl---sendDingTalkNotice()---构建跳转路径失败，失败原因{}", e.toString());
        }
        if (StringUtils.isNotBlank(jumpPathArg)) {
            diMessageList.setJumpPathArg(jumpPathArg);
        }
        iDiMessageListService.insertDiMessageList(diMessageList);
    }

    /**
     * 修改报价单状态以及审批任务ID
     *
     * @param approvalStatus 状态：0.待审批,1.审批中,2.审批通过,3.审批驳回
     * @param taskId         camunda任务ID
     * @param preSaleQuoteId 销售报价单主键ID
     */
    @Override
    public void updateApprovalInfo(Integer approvalStatus, String taskId, Long preSaleQuoteId) {
        if (null == preSaleQuoteId) {
            log.error("DiPreSaleQuoteApprovalServiceImpl---updateApprovalInfo()---销售报价单主键为空，不更新");
            return;
        }
        DiPreSaleQuote preSaleQuote = diPreSaleQuoteMapper.selectById(preSaleQuoteId);
        LambdaUpdateChainWrapper<DiPreSaleQuote> updateWrapper = new LambdaUpdateChainWrapper<>(diPreSaleQuoteMapper);
        updateWrapper.eq(DiPreSaleQuote::getId, preSaleQuoteId);
        if (null != approvalStatus) {
            updateWrapper.set(DiPreSaleQuote::getApprovalStatus, approvalStatus);
            if (Integer.valueOf(3).equals(approvalStatus)) {
                // 驳回时 不修改 核算交期
                //updateWrapper.set(DiPreSaleQuote::getFinallyDeliveryWeek, null);
                //updateWrapper.set(DiPreSaleQuote::getDeliveryReviewFlag, 2);
                updateWrapper.set(DiPreSaleQuote::getQuoteStatus, PreSaleQuoteStatusEnum.WAIT_QUOTE.getCode());
            } else {
                if (preSaleQuote.getFinallyDeliveryWeek() == null) {
                    updateWrapper.set(DiPreSaleQuote::getFinallyDeliveryWeek, preSaleQuote.getCalculateDeliveryWeek());
                }
                updateWrapper.set(DiPreSaleQuote::getQuoteStatus, PreSaleQuoteStatusEnum.QUOTED.getCode());
                //将候补销售报价单代办任务删除
                AgencyTaskInfoDTO agencyTaskInfoDto = new AgencyTaskInfoDTO();
                agencyTaskInfoDto.setTaskTypeEnum(AgencyTaskTypeEnum.SUPPLY_PRE_SALE_QUOTE);
                agencyTaskInfoDto.setBusinessKey(preSaleQuote.getId().toString());
                agencyTaskInfoDto.setJumpKey(preSaleQuote.getPreSaleQuoteCode());
                agencyTaskInfoDto.setType("2");
                String topic = AgencyConstants.AGENCY_TASK_ORDERLY_TOPIC + AgencyConstants.DELIMITER_COLON + AgencyConstants.AGENCY_TASK_ORDERLY_TAG;
                rocketMQTemplate.syncSend(topic, agencyTaskInfoDto);
            }
        }
        updateWrapper.set(DiPreSaleQuote::getCamundaTaskId, taskId);
        updateWrapper.update();
    }

}

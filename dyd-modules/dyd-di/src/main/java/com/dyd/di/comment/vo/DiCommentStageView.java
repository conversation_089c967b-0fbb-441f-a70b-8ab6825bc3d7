package com.dyd.di.comment.vo;

import com.dyd.di.comment.entity.DiComment;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

import static com.dyd.di.comment.controller.CommentController.stageNameMap;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DiCommentStageView {

    /**
     * 阶段ID
     */
    private Long stageId;

    /**
     * 阶段名称，字典：comment_stage
     */
    private String stage;

    private String stageName;

    /**
     * 该阶段的所有评论，倒序
     */
    private List<DiComment> commentList;

    public String getStageName() {
        return stageNameMap.get(getStage());
    }
}

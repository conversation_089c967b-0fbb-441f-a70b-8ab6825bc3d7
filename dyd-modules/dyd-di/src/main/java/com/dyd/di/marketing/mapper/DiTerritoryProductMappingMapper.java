package com.dyd.di.marketing.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dyd.di.marketing.domain.DiTerritoryProductMapping;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

/**
 * 产品矩阵映射 Mapper
 */
public interface DiTerritoryProductMappingMapper extends BaseMapper<DiTerritoryProductMapping> {

    /**
     * 根据refId和territoryId删除产品映射记录
     * @param refId 引用ID
     * @param territoryId 业务版图ID
     * @return 删除的记录数
     */
    @Delete("DELETE FROM di_territory_product_mapping WHERE ref_id = #{refId} AND territory_id = #{territoryId}")
    int deleteByRefIdAndTerritoryId(@Param("refId") String refId, @Param("territoryId") Long territoryId);

}
package com.dyd.di.rd.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dyd.common.core.utils.PageWrapper;
import com.dyd.di.rd.domain.request.*;
import com.dyd.di.rd.domain.response.*;
import com.dyd.di.rd.entity.DiRdNeed;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 研发需求Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-19
 */
public interface IDiRdNeedService extends IService<DiRdNeed>
{
    /**
     * 查询研发需求
     * 
     * @param id 研发需求主键
     * @return 研发需求
     */
    RdNeedDetailResponse selectDiRdNeedById(Long id,String rdNeedCode,String orderCode);

    /**
     * 查询方案配置
     * @param manifestId
     * @return
     */
    RdNeedDetailResponse getRdDisposition(Integer manifestId);

    /**
     * 模块配置
     * @param treeId
     * @return
     */
    List<RdModuleDetailResponse> getModuleDetailList(Integer treeId);

    /**
     * 查询研发需求列表
     * 
     * @param request 研发需求
     * @return 研发需求集合
     */
    PageWrapper<List<RdNeedListResponse>> selectDiRdNeedList(RdNeedListRequest request);

    /**
     * 新增研发需求
     * 
     * @param request 研发需求
     * @return 结果
     */
     void insertDiRdNeed(RdNeedAddRequest request);

    /**
     * 修改研发需求
     * 
     * @param request 研发需求
     * @return 结果
     */
     void updateDiRdNeed(RdNeedUpdateRequest request);

     void editRdNeedDisposition( RdNeedEditDispositionRequest request);

    /**
     * 锁定研发需求
     * @param id
     */
    void lockRdNeed(Integer id);

    /**
     * 批量删除研发需求
     * 
     * @param ids 需要删除的研发需求主键集合
     * @return 结果
     */
     int deleteDiRdNeedByIds(Long[] ids);

    /**
     * 删除研发需求信息
     * 
     * @param id 研发需求主键
     * @return 结果
     */
     int deleteDiRdNeedById(Long id);


    /**
     * 研发详情
     * @param id
     * @return
     */
     DiRdInquiryDetailResponse rdInquiryDetail(Integer id,String inquiryCode);

    /**
     * 新增询价
     * @param diRdInquiryAddRequest
     */
    void addRdInquiry(DiRdInquiryAddRequest diRdInquiryAddRequest);

    /**
     * 编辑询价
     * @param request
     */
    void updateRdInquiry(RdInquiryUpdateRequest request);

    /**
     * 研发询价列表
     * @param request
     * @return
     */
    PageWrapper<List<RdInquiryListResponse>> getRdInquiryList(RdInquiryListRequest request);

    /**
     * 询价报价
     * @param id
     */
    void rdInquiryBj(Integer id);
}

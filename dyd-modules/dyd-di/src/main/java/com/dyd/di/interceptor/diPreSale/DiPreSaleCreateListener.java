package com.dyd.di.interceptor.diPreSale;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dyd.common.core.utils.StringUtils;
import com.dyd.di.comment.enums.CommentStageEnum;
import com.dyd.di.interceptor.entity.DiProjectStageChange;
import com.dyd.di.interceptor.mapper.DiProjectStageChangeMapper;
import com.dyd.di.marketing.domain.DiMarketingNiche;
import com.dyd.di.marketing.mapper.DiMarketingNicheMapper;
import com.dyd.di.pre.entity.DiPreSale;
import com.dyd.di.pre.mapper.DiPreSaleMapper;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

@Component
public class DiPreSaleCreateListener {
    @Autowired
    private DiPreSaleMapper preSaleMapper;
    @Autowired
    private DiMarketingNicheMapper nicheMapper;
    @Autowired
    private DiProjectStageChangeMapper projectStageChangeMapper;

    @Async
    @EventListener
    public void handleDataChange(DiPreSaleCreateEvent event) {
        handleDataChange0(event);
    }

    public void handleDataChange0(DiPreSaleCreateEvent event) {
        String preSaleCode = event.getPreSaleCode();
        DiPreSale preSale =
                preSaleMapper.selectOne(Wrappers.<DiPreSale>lambdaQuery().eq(DiPreSale::getPreSaleCode, preSaleCode));
        if (preSale != null) {
            DiMarketingNiche niche =
                    nicheMapper.selectOne(Wrappers.<DiMarketingNiche>lambdaQuery().eq(DiMarketingNiche::getNicheNo, preSale.getNicheCode()));
            if (niche != null) {
                DiProjectStageChange diProjectStageChange = projectStageChangeMapper.selectOne(Wrappers.<DiProjectStageChange>lambdaQuery()
                        .in(DiProjectStageChange::getRefNo,
                                Lists.newArrayList(niche.getNicheNo(), niche.getClueNo())
                                        .stream()
                                        .filter(StringUtils::hasText).toList())
                        .orderByDesc(DiProjectStageChange::getCreateTime)
                        .last("limit 1"));
                if (diProjectStageChange == null ||
                        Objects.equals(diProjectStageChange.getStage(), CommentStageEnum.niche.getCode())) {
                    projectStageChangeMapper.insert(DiProjectStageChange.builder()
                            .stage(CommentStageEnum.plan.getCode())
                            .refNo(niche.getNicheNo())
                            .createTime(new Date())
                            .build());
                }
            }
        }
    }

}

package com.dyd.di.order.pojo.dto;

import com.dyd.common.core.utils.MaterielUtils;
import com.dyd.di.api.base.IShowMaterielNo;
import com.dyd.di.materiel.domain.DiMaterielVersion;
import com.dyd.di.materiel.pojo.dto.MaterielVersionBomGroupDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;
import java.util.Objects;

@Data
public class MaterielProduceBomDTO implements IShowMaterielNo {

    /**
     * 订单生产计划id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long orderProduceBomId;
    /**
     * 主键id bom的
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bomId;

    /**
     * 物料id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long materielId;

    /**
     * 父级id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long parentId;

    /**
     * 物料版本id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long materielVersionId;

    /**
     * 母料代码
     */
    private String parentMaterielNo;

    /**
     * 母料版本
     */
    private String parentVersionNo;

    /**
     * 岱鼎分类 系统/组件/部件/零件
     */
    private String dydClassification;

    /**
     * 物料名称
     */
    private String materielName;

    /**
     * 物料号
     */
    private String materielNo;

    /**
     * 是否替代料 1主料2替代料字典pre_sale_support_materiel_type
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Integer isSubstitute;

    /**
     * 路径
     */
    private String routePath;

    /**
     * 版本号
     */
    private String versionNo;
    /**
     * 0 W 1L 2V
     */
    private Integer bomStatus;
    /**
     * 显示的versionNo
     */
    private String showVersionNo;
    /**
     * 来源属性
     */
    private String sourceProperty;

    /**
     * 是否可以委外加工 0不可以 1可以
     */
    private Integer isEntrustProduce;

    /**
     * 品牌
     */
    private String brand;
    /**
     * 型号
     */
    private String patternNo;

    /**
     * 产品规格
     */
    private String productStandard;

    /**
     * 采购定性  purchase_property 字典
     */
    private String purchaseProperty = "materiel_purchase";

    /**
     * 是否展示采购定性  0 不展示 1展示
     */
    private Integer isShowPurchaseProperty = 0;

    /**
     * 分组节点列表
     */
    private List<MaterielVersionBomGroupDTO> groupList = Lists.newArrayList();
    /**
     * bom 子节点列表
     */
    private List<MaterielProduceBomDTO> childList = Lists.newArrayList();

    /**
     * 当前物料的版本号列表
     */
    private List<DiMaterielVersion> materielVersionList;

    public String getShowVersionNo() {
        showVersionNo= MaterielUtils.getShowVersionNo(bomStatus,versionNo);
        return showVersionNo;
    }
    /**
     * 显示物料号
     * @return
     */
    public String getShowMaterielNo(){
        return MaterielUtils.getShowMaterielNo(materielNo, bomStatus, versionNo);
    }
}

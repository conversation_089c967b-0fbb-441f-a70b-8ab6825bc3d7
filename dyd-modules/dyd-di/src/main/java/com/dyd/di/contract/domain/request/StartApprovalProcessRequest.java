package com.dyd.di.contract.domain.request;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 启动审批流程入参
 */
@Data
public class StartApprovalProcessRequest {

    /**
     * 合同主键
     */
//    @NotNull(message = "合同主键不能为空")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 合同编号
     */
    @NotBlank(message = "合同编号不能为空")
    private String contractNo;

    /**
     * 审批流程类型：1.成本审批，2.风险审批
     */
    @NotBlank(message = "审批流程类型不能为空")
    private String processType;

}

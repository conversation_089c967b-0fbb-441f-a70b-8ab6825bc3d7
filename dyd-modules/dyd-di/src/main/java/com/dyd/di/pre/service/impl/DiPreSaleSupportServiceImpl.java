package com.dyd.di.pre.service.impl;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dyd.common.core.domain.R;
import com.dyd.common.core.enums.PlannedSpeedEnum;
import com.dyd.common.core.enums.RelationTypeEnum;
import com.dyd.common.core.enums.SequenceEnum;
import com.dyd.common.core.utils.PageHelp;
import com.dyd.common.core.utils.PageWrapper;
import com.dyd.common.core.utils.StringUtils;
import com.dyd.common.datascope.annotation.DataScope;
import com.dyd.common.security.utils.SecurityUtils;
import com.dyd.dbc.api.RemoteDbcService;
import com.dyd.dbc.api.model.DdDept;
import com.dyd.dbc.api.model.DdUserDTO;
import com.dyd.dbc.api.model.QueryDdUserDTO;
import com.dyd.di.marketing.domain.DiMarketingNiche;
import com.dyd.di.marketing.service.IDiMarketingNicheService;
import com.dyd.di.material.entity.DiMaterialStockInfo;
import com.dyd.di.material.service.CommonService;
import com.dyd.di.material.service.DiMaterialStockInfoService;
import com.dyd.di.materiel.domain.DiMateriel;
import com.dyd.di.materiel.pojo.dto.MaterielChecklistDTO;
import com.dyd.di.materiel.service.IDiMaterielService;
import com.dyd.di.mq.MqProcessBody;
import com.dyd.di.mq.MqRelationBody;
import com.dyd.di.mq.MqSendMsg;
import com.dyd.di.pre.conver.PreSaleConverUtil;
import com.dyd.di.pre.domain.request.PreSaleSupportAddRequest;
import com.dyd.di.pre.domain.request.PreSaleSupportListRequest;
import com.dyd.di.pre.domain.request.PreSaleSupportUpdateDispositionRequest;
import com.dyd.di.pre.domain.request.PreSaleSupportUpdateRequest;
import com.dyd.di.pre.domain.response.PreSaleDetailModuleResponse;
import com.dyd.di.pre.domain.response.PreSaleSupportDetailForContractResponse;
import com.dyd.di.pre.domain.response.PreSaleSupportDetailResponse;
import com.dyd.di.pre.domain.response.PreSaleSupportListResponse;
import com.dyd.di.pre.entity.*;
import com.dyd.di.pre.mapper.*;
import com.dyd.di.process.pojo.dto.ProcessRelationUserMessageDTO;
import com.dyd.di.process.pojo.vo.ProcessProjectInfoVO;
import com.dyd.di.process.service.IDiProcessProjectService;
import com.dyd.di.sequence.SequenceService;
import com.dyd.system.api.RemoteSysService;
import com.dyd.system.api.RemoteUserService;
import com.dyd.system.api.domain.SysUser;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dyd.di.pre.service.IDiPreSaleSupportService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 售前方案支持Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
@Slf4j
@Service
public class DiPreSaleSupportServiceImpl extends ServiceImpl<DiPreSaleSupportMapper, DiPreSaleSupport> implements IDiPreSaleSupportService {
    @Autowired
    private DiPreSaleSupportMapper diPreSaleSupportMapper;

    @Autowired
    private IDiMarketingNicheService iDiMarketingNicheService;

    @Autowired
    private PreSaleConverUtil preSaleConverUtil;

    @Autowired
    private DiPreSaleSupportManifestMapper diPreSaleSupportManifestMapper;

    @Autowired
    private IDiMaterielService iDiMaterielService;

    @Autowired
    private DiPreSaleMapper diPreSaleMapper;

    @Autowired
    private DiPreSaleUrlMapper diPreSaleUrlMapper;

    @Autowired
    private DiPreSaleSupportDispositionMapper diPreSaleSupportDispositionMapper;

    @Autowired
    private DiPreSaleSupportModuleMapper diPreSaleSupportModuleMapper;

    @Autowired
    private DiPreSaleSupportTreeMapper diPreSaleSupportTreeMapper;

    @Autowired
    private DiMaterialStockInfoService diMaterialStockInfoService;

    @Autowired
    private SequenceService sequenceService;

    @Autowired
    private DiPreSaleSupportUrlMapper diPreSaleSupportUrlMapper;

    @Autowired
    private MqSendMsg mqSendMsg;

    @Autowired
    private DiPreSaleManifestMapper diPreSaleManifestMapper;


    @Autowired
    private RemoteDbcService remoteDbcService;

    @Autowired
    private IDiProcessProjectService diProcessProjectService;

    @Autowired
    private RemoteSysService remoteSysService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private CommonService commonService;


    /**
     * 查询售前方案支持
     *
     * @param paraId 售前方案支持主键
     * @return 售前方案支持
     */
    @Override
    public PreSaleSupportDetailResponse selectDiPreSaleSupportById(Long paraId, String code) {
        //查询技术支持详情
        DiPreSaleSupport diPreSaleSupport = null;
        if (Objects.nonNull(paraId)) {
            diPreSaleSupport = diPreSaleSupportMapper.selectDiPreSaleSupportById(paraId);
        } else {
            diPreSaleSupport = diPreSaleSupportMapper.selectOne(Wrappers.<DiPreSaleSupport>lambdaQuery().eq(DiPreSaleSupport::getPreSaleSupportCode, code));
        }
        if (Objects.isNull(diPreSaleSupport)) {
            return null;
        }
        Integer id = diPreSaleSupport.getId();
        PreSaleSupportDetailResponse preSaleSupportDetailResponse = preSaleConverUtil.converToSupportDetail(diPreSaleSupport);
        if (StringUtils.isNotEmpty(diPreSaleSupport.getSponsor())) {
            R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(Arrays.asList(diPreSaleSupport.getSponsor())).build());
            log.info("查询部门用户返回{}", JSON.toJSONString(userListResult));
            if (userListResult.isSuccess() && CollectionUtils.isNotEmpty(userListResult.getData())) {
                preSaleSupportDetailResponse.setSponsor(userListResult.getData().get(0).getName());
            }
        }
        //部门编码
        if (StringUtils.isNotEmpty(diPreSaleSupport.getBuName())) {
            R<List<DdDept>> deptListResult = remoteDbcService.queryDdDeptByNos(Arrays.asList(diPreSaleSupport.getBuName()));
            log.info("查询部门返回{}", JSON.toJSONString(deptListResult));
            if (deptListResult.isSuccess() && CollectionUtils.isNotEmpty(deptListResult.getData())) {
                preSaleSupportDetailResponse.setBuName(deptListResult.getData().get(0).getDeptName());
            }
        }

        //查询商机
        DiMarketingNiche diMarketingNiche = iDiMarketingNicheService.selectDiMarketingNicheById(String.valueOf(diPreSaleSupport.getNicheId()), true);
        if (Objects.nonNull(diMarketingNiche)) {
            preSaleSupportDetailResponse.setBuName(diMarketingNiche.getNicheOwnerDept());
        }
        //技术支持方案清单
        List<DiPreSaleSupportManifest> diPreSaleSupportManifests = diPreSaleSupportManifestMapper.selectList(Wrappers.<DiPreSaleSupportManifest>lambdaQuery().eq(DiPreSaleSupportManifest::getDiPreSaleSupportId, id));
        if (CollectionUtils.isNotEmpty(diPreSaleSupportManifests)) {
            //物料号
            List<String> materialCode = diPreSaleSupportManifests.stream().map(DiPreSaleSupportManifest::getMaterialCode).collect(Collectors.toList());
            //物料ids
            List<Long> materialIds = iDiMaterielService.queryMaterielByNos(materialCode).stream().map(DiMateriel::getId).collect(Collectors.toList());

            //物料map，key为物料号
            Map<String, List<DiMateriel>> materielMap = iDiMaterielService.queryMaterielByNos(materialCode).stream().collect(Collectors.groupingBy(DiMateriel::getMaterielNo));

            List<DiMaterialStockInfo> diMaterialStockInfos = null;
            if (CollectionUtils.isNotEmpty(materialIds)) {
                //物料id集合查询库存信息
                diMaterialStockInfos = diMaterialStockInfoService.queryStockByMaterialIds(materialIds);

            }
            //库存信息，key为物料id
            Map<Long, List<DiMaterialStockInfo>> materialStockMap = null;
            if (CollectionUtils.isNotEmpty(diMaterialStockInfos)) {
                materialStockMap = diMaterialStockInfos.stream().collect(Collectors.groupingBy(DiMaterialStockInfo::getMaterialId));
            }

            //方案清单
            List<PreSaleSupportDetailResponse.PreSaleSupportManifest> preSaleSupportManifests = new ArrayList<>();
            for (DiPreSaleSupportManifest diPreSaleSupportManifest : diPreSaleSupportManifests) {
                PreSaleSupportDetailResponse.PreSaleSupportManifest preSaleSupportManifest = preSaleConverUtil.converToSupportDetailManifest(diPreSaleSupportManifest);
                if (MapUtil.isNotEmpty(materialStockMap) && CollectionUtils.isNotEmpty(materielMap.get(diPreSaleSupportManifest.getMaterialCode()))) {
                    //获取库存信息
                    List<DiMaterialStockInfo> diMaterialStockInfoList = materialStockMap.get(materielMap.get(diPreSaleSupportManifest.getMaterialCode()).get(0).getId());
                    if (CollectionUtils.isNotEmpty(diMaterialStockInfoList)) {
                        //库存
                        preSaleSupportManifest.setInventory(Long.valueOf(diMaterialStockInfoList.get(0).getMaterialStock()));
                    }
                }
                List<DiPreSaleSupportUrl> diPreSaleSupportUrls = diPreSaleSupportUrlMapper.selectList(Wrappers.<DiPreSaleSupportUrl>lambdaQuery().eq(DiPreSaleSupportUrl::getPreSaleSupportManifestId, diPreSaleSupportManifest.getId()));
                if (CollectionUtils.isNotEmpty(diPreSaleSupportUrls)) {
                    preSaleSupportManifest.setPidPicUrls(diPreSaleSupportUrls.stream().filter(diPreSaleSupportUrl -> 1 == diPreSaleSupportUrl.getType()).map(DiPreSaleSupportUrl::getFileKey).collect(Collectors.toList()));
                    preSaleSupportManifest.setTechnicalUrls(diPreSaleSupportUrls.stream().filter(diPreSaleSupportUrl -> 2 == diPreSaleSupportUrl.getType()).map(DiPreSaleSupportUrl::getFileKey).collect(Collectors.toList()));
                }
                preSaleSupportManifests.add(preSaleSupportManifest);
            }
            preSaleSupportDetailResponse.setPreSaleSupportManifests(preSaleSupportManifests);
        }

        //售前信息
        DiPreSale diPreSale = diPreSaleMapper.selectById(diPreSaleSupport.getDiPreSaleId());
        if (Objects.nonNull(diPreSale)) {
            preSaleSupportDetailResponse.setRemark(diPreSale.getRemark());
        }
        //售前附件信息
        List<DiPreSaleUrl> diPreSaleUrls = diPreSaleUrlMapper.selectList(Wrappers.<DiPreSaleUrl>lambdaQuery().eq(DiPreSaleUrl::getDiPreSaleId, diPreSaleSupport.getDiPreSaleId()));

        if (CollectionUtils.isNotEmpty(diPreSaleUrls)) {
            preSaleSupportDetailResponse.setPreSaleUrls(diPreSaleUrls.stream().filter(diPreSaleUrl -> "2".equals(diPreSaleUrl.getType())).map(DiPreSaleUrl::getFileKey).collect(Collectors.toList()));
        }
        //物料清单
        List<DiPreSaleSupportModule> diPreSaleSupportModules = diPreSaleSupportModuleMapper.selectList(Wrappers.<DiPreSaleSupportModule>lambdaQuery().eq(DiPreSaleSupportModule::getPreSaleSupportId, diPreSaleSupport.getId()));
        if (CollectionUtils.isNotEmpty(diPreSaleSupportModules)) {
            //物料号
            List<String> materialCodes = diPreSaleSupportModules.stream().map(DiPreSaleSupportModule::getMaterialCode).collect(Collectors.toList());
            List<DiMateriel> diMateriels = iDiMaterielService.queryMaterielByNos(materialCodes);

            if (CollectionUtils.isNotEmpty(diMateriels)) {
                //物料id
                List<Long> materielIds = diMateriels.stream().map(DiMateriel::getId).collect(Collectors.toList());
                //物料库存map，key为物料id
                Map<String, List<DiMaterialStockInfo>> materialStockInfoMap = diMaterialStockInfoService.queryStockByMaterialIds(materielIds).stream().collect(Collectors.groupingBy(DiMaterialStockInfo::getId));

                //基础物料
                Map<String, List<DiMateriel>> diMaterielMap = diMateriels.stream().collect(Collectors.groupingBy(DiMateriel::getMaterielNo));
                //key为父级id
                Map<Integer, List<DiPreSaleSupportModule>> moduleMap = diPreSaleSupportModules.stream().collect(Collectors.groupingBy(DiPreSaleSupportModule::getParentId));
                //物料清单
                List<PreSaleSupportDetailResponse.Materiel> materiels = new ArrayList<>();
                for (DiPreSaleSupportModule diPreSaleSupportModule : diPreSaleSupportModules) {
                    if (CollectionUtils.isNotEmpty(diMaterielMap.get(diPreSaleSupportModule.getMaterialCode()))) {

                        PreSaleSupportDetailResponse.Materiel materiel = preSaleConverUtil.converToModuleDetail(diMaterielMap.get(diPreSaleSupportModule.getMaterialCode()).get(0));
                        if (CollectionUtils.isNotEmpty(materialStockInfoMap.get(String.valueOf(diMaterielMap.get(diPreSaleSupportModule.getMaterialCode()).get(0).getId())))) {
                            materiel.setMaterialStock(materialStockInfoMap.get(String.valueOf(diMaterielMap.get(diPreSaleSupportModule.getMaterialCode()).get(0).getId())).get(0).getMaterialStock());

                        }

                        //替代料
                        List<PreSaleSupportDetailResponse.Materiel> materielList = new ArrayList<>();
                        if (CollectionUtils.isNotEmpty(moduleMap.get(diPreSaleSupportModule.getId()))) {
                            List<DiPreSaleSupportModule> preSaleSupportModules = moduleMap.get(diPreSaleSupportModule.getId());
                            for (DiPreSaleSupportModule diPreSaleSupportModuleT : preSaleSupportModules) {
                                PreSaleSupportDetailResponse.Materiel materielT = preSaleConverUtil.converToModuleDetail(diMaterielMap.get(diPreSaleSupportModuleT.getMaterialCode()).get(0));
                                if (CollectionUtils.isNotEmpty(materialStockInfoMap.get(String.valueOf(diMaterielMap.get(diPreSaleSupportModuleT.getMaterialCode()).get(0).getId())))) {
                                    materielT.setMaterialStock(materialStockInfoMap.get(String.valueOf(diMaterielMap.get(diPreSaleSupportModuleT.getMaterialCode()).get(0).getId())).get(0).getMaterialStock());
                                }
                                materielList.add(materielT);
                            }
                        }
                        materiel.setSubMateriel(materielList);
                        materiels.add(materiel);
                    }
                }

                preSaleSupportDetailResponse.setMateriels(materiels);
            }
        }
        return preSaleSupportDetailResponse;
    }

    /**
     * 售前支持--合同
     *
     * @param projectCode
     * @return
     */
    @Override
    public PreSaleSupportDetailForContractResponse selectDiPreSaleSupportByProject(String projectCode) {

        PreSaleSupportDetailForContractResponse preSaleSupportDetailForContractResponse = new PreSaleSupportDetailForContractResponse();
        //查询技术支持详情
        DiPreSaleSupport diPreSaleSupport = diPreSaleSupportMapper.selectOne(Wrappers.<DiPreSaleSupport>lambdaQuery().eq(DiPreSaleSupport::getProjectCode, projectCode));
        if (Objects.isNull(diPreSaleSupport)) {
            return preSaleSupportDetailForContractResponse;
        }
        preSaleSupportDetailForContractResponse.setId(diPreSaleSupport.getId());

        int id = diPreSaleSupport.getId();
        //技术支持方案清单
        List<DiPreSaleSupportManifest> diPreSaleSupportManifests = diPreSaleSupportManifestMapper.selectList(Wrappers.<DiPreSaleSupportManifest>lambdaQuery().eq(DiPreSaleSupportManifest::getDiPreSaleSupportId, id));
        if (CollectionUtils.isNotEmpty(diPreSaleSupportManifests)) {
            //物料号
            List<String> materialCode = diPreSaleSupportManifests.stream().map(DiPreSaleSupportManifest::getMaterialCode).collect(Collectors.toList());
            //物料ids
            List<Long> materialIds = iDiMaterielService.queryMaterielByNos(materialCode).stream().map(DiMateriel::getId).collect(Collectors.toList());

            //物料map，key为物料号
            Map<String, List<DiMateriel>> materielMap = iDiMaterielService.queryMaterielByNos(materialCode).stream().collect(Collectors.groupingBy(DiMateriel::getMaterielNo));

            List<DiMaterialStockInfo> diMaterialStockInfos = null;
            if (CollectionUtils.isNotEmpty(materialIds)) {
                //物料id集合查询库存信息
                diMaterialStockInfos = diMaterialStockInfoService.queryStockByMaterialIds(materialIds);
            }
            //库存信息，key为物料id
            Map<Long, List<DiMaterialStockInfo>> materialStockMap = null;
            if (CollectionUtils.isNotEmpty(diMaterialStockInfos)) {
                materialStockMap = diMaterialStockInfos.stream().collect(Collectors.groupingBy(DiMaterialStockInfo::getMaterialId));
            }

            //方案清单
            List<PreSaleSupportDetailResponse.PreSaleSupportManifest> preSaleSupportManifests = new ArrayList<>();
            for (DiPreSaleSupportManifest diPreSaleSupportManifest : diPreSaleSupportManifests) {
                PreSaleSupportDetailResponse.PreSaleSupportManifest preSaleSupportManifest = preSaleConverUtil.converToSupportDetailManifest(diPreSaleSupportManifest);
                if (MapUtil.isNotEmpty(materialStockMap)) {
                    //获取库存信息
                    List<DiMaterialStockInfo> diMaterialStockInfoList = materialStockMap.get(materielMap.get(diPreSaleSupportManifest.getMaterialCode()).get(0).getId());
                    if (CollectionUtils.isNotEmpty(diMaterialStockInfoList)) {
                        //库存
                        preSaleSupportManifest.setInventory(Long.valueOf(diMaterialStockInfoList.get(0).getMaterialStock()));
                    }
                }
                preSaleSupportManifests.add(preSaleSupportManifest);
            }
            preSaleSupportDetailForContractResponse.setPreSaleSupportManifests(preSaleSupportManifests);

            //图纸
            List<Integer> manifestIds = diPreSaleSupportManifests.stream().map(DiPreSaleSupportManifest::getId).collect(Collectors.toList());
            List<DiPreSaleSupportUrl> diPreSaleSupportUrls = diPreSaleSupportUrlMapper.selectList(Wrappers.<DiPreSaleSupportUrl>lambdaQuery().in(DiPreSaleSupportUrl::getPreSaleSupportManifestId, manifestIds));
            if (CollectionUtils.isNotEmpty(diPreSaleSupportUrls)) {
                preSaleSupportDetailForContractResponse.setPidPicUrls(diPreSaleSupportUrls.stream().filter(diPreSaleSupportUrl -> 1 == diPreSaleSupportUrl.getType()).map(DiPreSaleSupportUrl::getFileKey).collect(Collectors.toList()));
                preSaleSupportDetailForContractResponse.setTechnicalUrls(diPreSaleSupportUrls.stream().filter(diPreSaleSupportUrl -> 2 == diPreSaleSupportUrl.getType()).map(DiPreSaleSupportUrl::getFileKey).collect(Collectors.toList()));

            }
        }
        //物料清单
        List<DiPreSaleSupportModule> diPreSaleSupportModules = diPreSaleSupportModuleMapper.selectList(Wrappers.<DiPreSaleSupportModule>lambdaQuery().eq(DiPreSaleSupportModule::getPreSaleSupportId, diPreSaleSupport.getId()));
        if (CollectionUtils.isNotEmpty(diPreSaleSupportModules)) {
            //物料号
            List<String> materialCodes = diPreSaleSupportModules.stream().map(DiPreSaleSupportModule::getMaterialCode).collect(Collectors.toList());
            List<DiMateriel> diMateriels = iDiMaterielService.queryMaterielByNos(materialCodes);

            if (CollectionUtils.isNotEmpty(diMateriels)) {
                //物料id
                List<Long> materielIds = diMateriels.stream().map(DiMateriel::getId).collect(Collectors.toList());
                //物料库存map，key为物料id
                Map<String, List<DiMaterialStockInfo>> materialStockInfoMap = diMaterialStockInfoService.queryStockByMaterialIds(materielIds).stream().collect(Collectors.groupingBy(DiMaterialStockInfo::getId));

                //基础物料
                Map<String, List<DiMateriel>> diMaterielMap = diMateriels.stream().collect(Collectors.groupingBy(DiMateriel::getMaterielNo));
                //key为父级id
                Map<Integer, List<DiPreSaleSupportModule>> moduleMap = diPreSaleSupportModules.stream().collect(Collectors.groupingBy(DiPreSaleSupportModule::getParentId));
                //物料清单
                List<PreSaleSupportDetailResponse.Materiel> materiels = new ArrayList<>();
                for (DiPreSaleSupportModule diPreSaleSupportModule : diPreSaleSupportModules) {
                    if (CollectionUtils.isNotEmpty(diMaterielMap.get(diPreSaleSupportModule.getMaterialCode()))) {

                        PreSaleSupportDetailResponse.Materiel materiel = preSaleConverUtil.converToModuleDetail(diMaterielMap.get(diPreSaleSupportModule.getMaterialCode()).get(0));
                        if (CollectionUtils.isNotEmpty(materialStockInfoMap.get(String.valueOf(diMaterielMap.get(diPreSaleSupportModule.getMaterialCode()).get(0).getId())))) {
                            materiel.setMaterialStock(materialStockInfoMap.get(String.valueOf(diMaterielMap.get(diPreSaleSupportModule.getMaterialCode()).get(0).getId())).get(0).getMaterialStock());

                        }

                        //替代料
                        List<PreSaleSupportDetailResponse.Materiel> materielList = new ArrayList<>();
                        if (CollectionUtils.isNotEmpty(moduleMap.get(diPreSaleSupportModule.getId()))) {
                            List<DiPreSaleSupportModule> preSaleSupportModules = moduleMap.get(diPreSaleSupportModule.getId());
                            for (DiPreSaleSupportModule diPreSaleSupportModuleT : preSaleSupportModules) {
                                PreSaleSupportDetailResponse.Materiel materielT = preSaleConverUtil.converToModuleDetail(diMaterielMap.get(diPreSaleSupportModuleT.getMaterialCode()).get(0));
                                if (CollectionUtils.isNotEmpty(materialStockInfoMap.get(String.valueOf(diMaterielMap.get(diPreSaleSupportModuleT.getMaterialCode()).get(0).getId())))) {
                                    materielT.setMaterialStock(materialStockInfoMap.get(String.valueOf(diMaterielMap.get(diPreSaleSupportModuleT.getMaterialCode()).get(0).getId())).get(0).getMaterialStock());
                                }
                                materielList.add(materielT);
                            }
                        }
                        materiel.setSubMateriel(materielList);
                        materiels.add(materiel);
                    }
                }

                preSaleSupportDetailForContractResponse.setMateriels(materiels);
            }
        }
        return preSaleSupportDetailForContractResponse;
    }

    /**
     * 获取方案清单
     *
     * @param manifestId
     * @return
     */
    @Override
    public PreSaleSupportDetailResponse getSupportDisposition(Integer manifestId) {

        PreSaleSupportDetailResponse preSaleSupportDetailResponse = new PreSaleSupportDetailResponse();
        //根据清单id查询方案配置
        DiPreSaleSupportDisposition diPreSaleSupportDisposition = diPreSaleSupportDispositionMapper.selectOne(Wrappers.<DiPreSaleSupportDisposition>lambdaQuery().eq(DiPreSaleSupportDisposition::getDiPreSaleSupportManifestId, manifestId));
        if (Objects.nonNull(diPreSaleSupportDisposition)) {
            //方案配置
            PreSaleSupportDetailResponse.PreSaleSupportDisposition preSaleSupportDisposition = preSaleConverUtil.converToDisposiontionDetail(diPreSaleSupportDisposition);
            preSaleSupportDisposition.setPackageType(String.valueOf(diPreSaleSupportDisposition.getPackageType()));
            //获取方案配置结构树
            List<DiPreSaleSupportTree> diPreSaleSupportTrees = diPreSaleSupportTreeMapper.selectList(Wrappers.<DiPreSaleSupportTree>lambdaQuery().eq(DiPreSaleSupportTree::getDiPreSaleSupportDispositionId, preSaleSupportDisposition.getId()));
            if (CollectionUtils.isNotEmpty(diPreSaleSupportTrees)) {
                DiPreSaleSupportTree diPreSaleSupportTreeT = diPreSaleSupportTrees.stream().filter(diPreSaleSupportTree -> 0 == diPreSaleSupportTree.getParentId()).collect(Collectors.toList()).get(0);
                PreSaleSupportDetailResponse.PreSaleSupportTree preSaleSupportTree = preSaleConverUtil.converToSupportTreeDetail(diPreSaleSupportTreeT);
                preSaleSupportTree.setPreSaleSupportModules(getModuleDetailList(diPreSaleSupportTreeT.getId()));
                List<PreSaleSupportDetailResponse.PreSaleSupportTree> preSaleSupportTrees = subTreeIter(diPreSaleSupportTrees, preSaleSupportTree.getId());
                preSaleSupportTree.setChildList(preSaleSupportTrees);
                preSaleSupportDisposition.setPreSaleSupportTree(preSaleSupportTree);
            }

            DiPreSaleSupportManifest diPreSaleSupportManifest = diPreSaleSupportManifestMapper.selectById(manifestId);
            List<DiPreSaleSupportUrl> diPreSaleSupportUrls = diPreSaleSupportUrlMapper.selectList(Wrappers.<DiPreSaleSupportUrl>lambdaQuery().eq(DiPreSaleSupportUrl::getDiPreSaleSupportId, diPreSaleSupportManifest.getDiPreSaleSupportId()));

            if (CollectionUtils.isNotEmpty(diPreSaleSupportUrls)) {
                preSaleSupportDisposition.setPidPicUrls(diPreSaleSupportUrls.stream().filter(diPreSaleSupportUrl -> 1 == diPreSaleSupportUrl.getType()).map(DiPreSaleSupportUrl::getFileKey).collect(Collectors.toList()));
                preSaleSupportDisposition.setTechnicalUrls(diPreSaleSupportUrls.stream().filter(diPreSaleSupportUrl -> 2 == diPreSaleSupportUrl.getType()).map(DiPreSaleSupportUrl::getFileKey).collect(Collectors.toList()));
            }
            preSaleSupportDetailResponse.setPreSaleSupportDisposition(preSaleSupportDisposition);
        }
        return preSaleSupportDetailResponse;
    }

    public List<PreSaleSupportDetailResponse.PreSaleSupportTree> subTreeIter(List<DiPreSaleSupportTree> diPreSaleSupportTrees, Integer parentId) {
        List<PreSaleSupportDetailResponse.PreSaleSupportTree> preSaleSupportTrees = new ArrayList<>();
        for (DiPreSaleSupportTree diPreSaleSupportTree : diPreSaleSupportTrees) {
            if (diPreSaleSupportTree.getParentId() == parentId) {
                PreSaleSupportDetailResponse.PreSaleSupportTree preSaleSupportTree = preSaleConverUtil.converToSupportTreeDetail(diPreSaleSupportTree);
                preSaleSupportTree.setChildList(subTreeIter(diPreSaleSupportTrees, diPreSaleSupportTree.getId()));
                preSaleSupportTree.setPreSaleSupportModules(getModuleDetailList(diPreSaleSupportTree.getId()));
                preSaleSupportTrees.add(preSaleSupportTree);
            }
        }
        return preSaleSupportTrees;
    }

    /**
     * 获取模块配置集合
     *
     * @param treeId
     * @return
     */
    @Override
    public List<PreSaleDetailModuleResponse> getModuleDetailList(Integer treeId) {
        //结构树id查询对应模块配置
        List<DiPreSaleSupportModule> diPreSaleSupportModules = diPreSaleSupportModuleMapper.selectList(Wrappers.<DiPreSaleSupportModule>lambdaQuery().eq(DiPreSaleSupportModule::getPreSaleSupportTreeId, treeId));
        if (CollectionUtils.isNotEmpty(diPreSaleSupportModules)) {
            //key为父级id
            Map<Integer, List<DiPreSaleSupportModule>> moduleMap = diPreSaleSupportModules.stream().collect(Collectors.groupingBy(DiPreSaleSupportModule::getParentId));
            List<DiPreSaleSupportModule> preSaleSupportModules = diPreSaleSupportModules.stream().filter(diPreSaleSupportModule -> 0 == diPreSaleSupportModule.getParentId()).collect(Collectors.toList());

            return preSaleSupportModules.stream().map(diPreSaleSupportModule -> {
                PreSaleDetailModuleResponse preSaleDetailModuleResponse = preSaleConverUtil.converToModuleResponse(diPreSaleSupportModule);
                preSaleDetailModuleResponse.setModuleType(String.valueOf(diPreSaleSupportModule.getModuleType()));
                preSaleDetailModuleResponse.setMaterialType(String.valueOf(diPreSaleSupportModule.getMaterialType()));
                if (CollectionUtils.isNotEmpty(moduleMap.get(preSaleDetailModuleResponse.getId()))) {
                    if (CollectionUtils.isNotEmpty(moduleMap.get(preSaleDetailModuleResponse.getId()))) {
                        preSaleDetailModuleResponse.setMaterialSub(moduleMap.get(preSaleDetailModuleResponse.getId()).stream().map(diPreSaleSupportModuleT -> {
                            PreSaleDetailModuleResponse.MaterialSub materialSub = preSaleConverUtil.converToModuleSub(diPreSaleSupportModuleT);
                            materialSub.setModuleType(String.valueOf(diPreSaleSupportModuleT.getModuleType()));
                            materialSub.setMaterialType(String.valueOf(diPreSaleSupportModuleT.getMaterialType()));
                            return materialSub;
                        }).collect(Collectors.toList()));
                    }

                }
                return preSaleDetailModuleResponse;
            }).collect(Collectors.toList());
        }
        return null;
    }

    /**
     * 查询售前方案支持列表
     *
     * @param request 售前方案支持
     * @return 售前方案支持
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public PageWrapper<List<PreSaleSupportListResponse>> selectDiPreSaleSupportList(PreSaleSupportListRequest request) {

        /*//当前登录人id
        Long userid = SecurityUtils.getLoginUser().getUserid();
        R<SysUserRoleData> sysUserDataSource = remoteSysService.getSysUserDataSource(userid);
        if (sysUserDataSource.isError()) {
            throw new ServiceException("获取登录人信息失败");
        }

        List<String> jobNumberList = null;
        //判断当亲登录者数据可见范围
        if (MaterialConstants.ZERO.equals(sysUserDataSource.getData().getType())) {
            //如果是限制范围的，构建登录人工号已经他下属层级工号
            jobNumberList = sysUserDataSource.getData().getUserDataList().stream().map(SysUserRoleData.roleData::getJobNumber).toList();
        }*/
        if (null != request.getParams() && ((String) request.getParams().get("dataScope")).contains("AND")) {
            request.getParams().put("dataScope", ((String) request.getParams().get("dataScope")).replace("AND", "OR"));
        } else {
            request.setParams(new HashMap<>());
            request.getParams().put("dataScope", " OR 2 = 2");
        }

        request.getParams().put("loginUser", "'" + SecurityUtils.getUsername() + "'");
        if (Objects.nonNull(SecurityUtils.getLoginUser()) && Objects.nonNull(SecurityUtils.getLoginUser().getSysUser())) {
            request.getParams().put("sysUser", "'" + SecurityUtils.getLoginUser().getSysUser().getNickName() + "'");
            request.getParams().put("deptId", "'" + SecurityUtils.getLoginUser().getSysUser().getDeptId() + "'");
        }

        Page page = PageHelper.startPage(request.getPageNum(), request.getPageSize());
        List<DiPreSaleSupport> diPreSaleSupports = diPreSaleSupportMapper.selectDiPreSaleSupportList(request.getCode(), request.getSjCode(), request.getProjectNo(), request.getParams());

        List<PreSaleSupportListResponse> preSaleSupportListResponses = null;
        if (CollectionUtils.isNotEmpty(diPreSaleSupports)) {
            Map<String, String> userNameMap = Maps.newHashMap();
            List<String> jobNumList = diPreSaleSupports.stream().map(DiPreSaleSupport::getSponsor).collect(Collectors.toList());
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(jobNumList)) {
                R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(jobNumList).build());
                log.info("查询部门用户返回{}", JSON.toJSONString(userListResult));
                if (userListResult.isSuccess()) {
                    userNameMap = userListResult.getData().stream().collect(Collectors.toMap(DdUserDTO::getJobNumber, DdUserDTO::getName));
                }
            }
            //部门编码
            List<String> buNos = diPreSaleSupports.stream().map(DiPreSaleSupport::getBuName).collect(Collectors.toList());
            Map<Integer, String> deptMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(buNos)) {
                R<List<DdDept>> deptListResult = remoteDbcService.queryDdDeptByNos(buNos);
                log.info("查询部门返回{}", JSON.toJSONString(deptListResult));
                if (deptListResult.isSuccess()) {
                    deptMap = deptListResult.getData().stream().collect(Collectors.toMap(DdDept::getDeptId, DdDept::getDeptName));
                }
            }
            Map<String, String> finalUserNameMap = userNameMap;
            Map<Integer, String> finalDeptMap = deptMap;
            preSaleSupportListResponses = diPreSaleSupports.stream().map(diPreSaleSupport -> {
                PreSaleSupportListResponse preSaleSupportListResponse = new PreSaleSupportListResponse();
                preSaleSupportListResponse.setId(diPreSaleSupport.getId());
                preSaleSupportListResponse.setApportionDate(diPreSaleSupport.getApportionDate());
                preSaleSupportListResponse.setExpectFinishDate(diPreSaleSupport.getExpecteFinishDate());
                preSaleSupportListResponse.setSupportStatus(String.valueOf(diPreSaleSupport.getSupportStatus()));
                preSaleSupportListResponse.setNicheCode(diPreSaleSupport.getNicheCode());
                preSaleSupportListResponse.setSponsor(finalUserNameMap.get(diPreSaleSupport.getSponsor()));
                preSaleSupportListResponse.setTelSupportHeader(diPreSaleSupport.getTelSupportHeader());
                preSaleSupportListResponse.setPreSaleSupportCode(diPreSaleSupport.getPreSaleSupportCode());
                if (StringUtils.isNotEmpty(diPreSaleSupport.getBuName())) {
                    preSaleSupportListResponse.setBuName(finalDeptMap.get(Integer.valueOf(diPreSaleSupport.getBuName())));
                }
                preSaleSupportListResponse.setProjectCode(diPreSaleSupport.getProjectCode());
                return preSaleSupportListResponse;
            }).collect(Collectors.toList());
        }

        return PageHelp.render(page, preSaleSupportListResponses);
    }

    /**
     * 锁定售前方案支持
     *
     * @param id
     */
    @Transactional
    @Override
    public void lockPreSaleSupport(Integer id) {
        DiPreSaleSupport diPreSaleSupportTT = diPreSaleSupportMapper.selectById(id);
        //已锁定
        if (2 == diPreSaleSupportTT.getSupportStatus()) {
            return;
        }

        DiPreSaleSupport diPreSaleSupport = new DiPreSaleSupport();
        diPreSaleSupport.setId(id);
        diPreSaleSupport.setSupportStatus(2);
        diPreSaleSupportMapper.updateById(diPreSaleSupport);



        /*RdNeedAddRequest rdNeedAddRequest = new RdNeedAddRequest();
        rdNeedAddRequest.setPreSaleSupportId(id);
        rdNeedAddRequest.setProjectCode(diPreSaleSupportTT.getProjectCode());
        rdNeedAddRequest.setTelSupportHeader(diPreSaleSupportTT.getTelSupportHeader());
        rdNeedAddRequest.setSendDate(diPreSaleSupportTT.getApportionDate());
        rdNeedAddRequest.setSponsor(diPreSaleSupportTT.getSponsor());
        rdNeedAddRequest.setBuName(diPreSaleSupportTT.getBuName());

        List<DiPreSaleSupportManifest> diPreSaleSupportManifests = diPreSaleSupportManifestMapper.selectList(Wrappers.<DiPreSaleSupportManifest>lambdaQuery().eq(DiPreSaleSupportManifest::getDiPreSaleSupportId, id));
        if(CollectionUtils.isNotEmpty(diPreSaleSupportManifests)){
            List<RdNeedAddRequest.RdNeedManifest> diRdNeedManifests = diPreSaleSupportManifests.stream().map(diPreSaleSupportManifest -> {
                RdNeedAddRequest.RdNeedManifest rdNeedManifest = rdConverUtil.converToRdManifest(diPreSaleSupportManifest);
                return rdNeedManifest;
            }).collect(Collectors.toList());
            rdNeedAddRequest.setRdNeedManifests(diRdNeedManifests);
        }

        iDiRdNeedService.insertDiRdNeed(rdNeedAddRequest);*/


        try {
            MqProcessBody mqProcessBody = new MqProcessBody();
            mqProcessBody.setProjectNo(diPreSaleSupportTT.getProjectCode());
            mqProcessBody.setPlannedSpeedType(PlannedSpeedEnum.PRE_SALE_SUPPORT.getPlanned());
            mqProcessBody.setProcess(new BigDecimal("100"));
            mqSendMsg.sendMsg(BeanUtil.beanToMap(mqProcessBody), "process");
        } catch (Exception e) {
            log.error("方案支持发送进度消息error{}", e.getMessage());
        }
    }

    /**
     * 新增售前方案支持
     *
     * @param request 售前方案支持
     * @return 结果
     */
    @Transactional
    @Override
    public void insertDiPreSaleSupport(PreSaleSupportAddRequest request) {
        List<String> authData = new ArrayList<>();
        List<DiPreSaleSupport> diPreSaleSupports = diPreSaleSupportMapper.selectList(Wrappers.<DiPreSaleSupport>lambdaQuery().eq(DiPreSaleSupport::getDiPreSaleId, request.getDiPreSaleId()));
        if (CollectionUtils.isNotEmpty(diPreSaleSupports)) {
            return;
        }
        //保存技术支持
        DiPreSaleSupport diPreSaleSupport = preSaleConverUtil.converToAddSupport(request);
        diPreSaleSupport.setSupportStatus(0);
        //技术负责人
        if (StringUtils.isNotEmpty(request.getTelSupportHeader())) {
            R<SysUser> userInfo = remoteUserService.userInfo(request.getTelSupportHeader());
            if (userInfo.isSuccess() && Objects.nonNull(userInfo.getData())) {
                authData.add(userInfo.getData().getUserName());
            }
            //已分配
            diPreSaleSupport.setSupportStatus(1);
        }
        diPreSaleSupport.setPreSaleSupportCode(sequenceService.getSequenceNo(SequenceEnum.DYD_ZC.getCode()));

        DiPreSale diPreSale = diPreSaleMapper.selectById(request.getDiPreSaleId());
        if (Objects.nonNull(diPreSale)) {
            diPreSaleSupport.setProjectCode(diPreSale.getProjectCode());
            diPreSaleSupport.setNicheCode(diPreSale.getNicheCode());
            diPreSaleSupport.setNicheId(diPreSale.getNicheId());
            diPreSaleSupport.setApportionDate(LocalDate.now());
            if (Objects.isNull(request.getExpecteFinishDate())) {
                diPreSaleSupport.setExpecteFinishDate(diPreSale.getExpecteDate());
            }
        }

        if (StringUtils.isEmpty(request.getSponsor())) {
            diPreSaleSupport.setSponsor(SecurityUtils.getUsername());
            if (Objects.nonNull(SecurityUtils.getLoginUser()) && Objects.nonNull(SecurityUtils.getLoginUser().getSysUser())) {
                diPreSaleSupport.setBuName(String.valueOf(SecurityUtils.getLoginUser().getSysUser().getDeptId()));
            }
        }

        DiMarketingNiche diMarketingNiche = iDiMarketingNicheService.selectDiMarketingNicheByNo(request.getNicheCode());
        if (Objects.nonNull(diMarketingNiche)) {
            diPreSaleSupport.setNicheOwer(diMarketingNiche.getCreateBy());
        }

        ProcessProjectInfoVO processProjectInfoVO = new ProcessProjectInfoVO();
        processProjectInfoVO.setProjectNo(request.getProjectCode());
//        ProcessProjectInfoDTO processProjectInfoDTO = diProcessProjectService.processProjectInfo(processProjectInfoVO);
//        if(Objects.nonNull(processProjectInfoDTO)) {
//            diPreSaleSupport.setProjectManagerUserId(processProjectInfoDTO.getProjectManagerUserId());
//            authData.add(processProjectInfoDTO.getProjectManagerUserId());
//        }

        diPreSaleSupport.setCreateBy(SecurityUtils.getUsername());
        authData.add(SecurityUtils.getUsername());
        diPreSaleSupportMapper.insert(diPreSaleSupport);

        //支持方案清单
        List<PreSaleSupportAddRequest.PreSaleSupportManifest> preSaleSupportManifests = request.getPreSaleSupportManifests();

        //删除售前方案的清单，用支持的覆盖
        diPreSaleManifestMapper.delete(Wrappers.<DiPreSaleManifest>lambdaUpdate().eq(DiPreSaleManifest::getDiPreSaleId, request.getDiPreSaleId()));


        if (CollectionUtils.isNotEmpty(preSaleSupportManifests)) {
            for (PreSaleSupportAddRequest.PreSaleSupportManifest preSaleSupportManifest : preSaleSupportManifests) {

                DiPreSaleManifest diPreSaleManifest = preSaleConverUtil.converToAddMainfest(preSaleSupportManifest);
                diPreSaleManifest.setDiPreSaleId(request.getDiPreSaleId());
                diPreSaleManifest.setId(null);
                diPreSaleManifestMapper.insert(diPreSaleManifest);

                DiPreSaleSupportManifest diPreSaleSupportManifest = preSaleConverUtil.converToAddSuuortMainfest(preSaleSupportManifest);
                diPreSaleSupportManifest.setDiPreSaleSupportId(diPreSaleSupport.getId());
                diPreSaleSupportManifest.setPreSaleManifestId(diPreSaleManifest.getId());
                diPreSaleSupportManifestMapper.insert(diPreSaleSupportManifest);

                //临时方案清单
                List<MaterielChecklistDTO> materielChecklistDTOS = iDiMaterielService.queryMaterielCheckList(diPreSaleSupportManifest.getMaterialCode());
                if (CollectionUtils.isNotEmpty(materielChecklistDTOS)) {
                    for (MaterielChecklistDTO materielChecklistDTO : materielChecklistDTOS) {
                        DiPreSaleSupportModule diPreSaleSupportModule = new DiPreSaleSupportModule();
                        diPreSaleSupportModule.setPreSaleSupportId(diPreSaleSupport.getId());
                        if ("整套系统".equals(materielChecklistDTO.getModuleType())) {
                            diPreSaleSupportModule.setModuleType(1);
                        } else if ("烧嘴".equals(materielChecklistDTO.getModuleType())) {
                            diPreSaleSupportModule.setModuleType(2);
                        } else if ("电控".equals(materielChecklistDTO.getModuleType())) {
                            diPreSaleSupportModule.setModuleType(3);
                        } else if ("阀组".equals(materielChecklistDTO.getModuleType())) {
                            diPreSaleSupportModule.setModuleType(4);
                        } else if ("炉子".equals(materielChecklistDTO.getModuleType())) {
                            diPreSaleSupportModule.setModuleType(5);
                        }

                        diPreSaleSupportModule.setMaterialName(materielChecklistDTO.getMaterialName());
                        diPreSaleSupportModule.setMaterialCode(materielChecklistDTO.getMaterialCode());
                        diPreSaleSupportModule.setParentId(0);
                        diPreSaleSupportModule.setMaterialType(materielChecklistDTO.getMaterialType());
                        diPreSaleSupportModule.setUseNum(materielChecklistDTO.getUseNum().intValue());
                        diPreSaleSupportModuleMapper.insert(diPreSaleSupportModule);
                    }
                }

                //方案配置
                PreSaleSupportAddRequest.PreSaleSupportDisposition preSaleSupportDisposition = preSaleSupportManifest.getPreSaleSupportDisposition();
                if (Objects.nonNull(preSaleSupportDisposition)) {
                    DiPreSaleSupportDisposition diPreSaleSupportDisposition = preSaleConverUtil.converToAddSupportDisposition(preSaleSupportDisposition);
                    diPreSaleSupportDisposition.setDiPreSaleSupportManifestId(diPreSaleSupportManifest.getId());
                    diPreSaleSupportDispositionMapper.insert(diPreSaleSupportDisposition);

                    List<DiPreSaleSupportUrl> diPreSaleSupportUrls = new ArrayList<>();
                    //PID图纸
                    if (CollectionUtils.isNotEmpty(preSaleSupportDisposition.getPidUrls())) {
                        for (String url : preSaleSupportDisposition.getPidUrls()) {
                            DiPreSaleSupportUrl diPreSaleSupportUrl = new DiPreSaleSupportUrl();
                            diPreSaleSupportUrl.setDiPreSaleSupportId(diPreSaleSupport.getId());
                            diPreSaleSupportUrl.setPreSaleSupportManifestId(diPreSaleSupportManifest.getId());
                            diPreSaleSupportUrl.setType(1);
                            diPreSaleSupportUrl.setFileKey(url);
                            diPreSaleSupportUrls.add(diPreSaleSupportUrl);
                        }
                    }

                    //技术协议
                    if (CollectionUtils.isNotEmpty(preSaleSupportDisposition.getTelUrls())) {
                        for (String url : preSaleSupportDisposition.getTelUrls()) {
                            DiPreSaleSupportUrl diPreSaleSupportUrl = new DiPreSaleSupportUrl();
                            diPreSaleSupportUrl.setDiPreSaleSupportId(diPreSaleSupport.getId());
                            diPreSaleSupportUrl.setPreSaleSupportManifestId(diPreSaleSupportManifest.getId());
                            diPreSaleSupportUrl.setType(2);
                            diPreSaleSupportUrl.setFileKey(url);
                            diPreSaleSupportUrls.add(diPreSaleSupportUrl);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(diPreSaleSupportUrls)) {
                        diPreSaleSupportUrlMapper.insertBatchSomeColumn(diPreSaleSupportUrls);
                    }

                    //结构树
                    PreSaleSupportAddRequest.PreSaleSupportTree preSaleSupportTree = preSaleSupportDisposition.getPreSaleSupportTree();
                    DiPreSaleSupportTree diPreSaleSupportTree = new DiPreSaleSupportTree();
                    diPreSaleSupportTree.setDiPreSaleSupportDispositionId(diPreSaleSupportDisposition.getId());
                    diPreSaleSupportTree.setParentId(0);
                    diPreSaleSupportTree.setMaterialCode(preSaleSupportTree.getMaterialCode());
                    diPreSaleSupportTree.setMaterialName(preSaleSupportTree.getMaterialName());
                    diPreSaleSupportTreeMapper.insert(diPreSaleSupportTree);

                    //模块配置
                    List<PreSaleSupportAddRequest.PreSaleSupportModule> preSaleSupportModules = preSaleSupportTree.getPreSaleSupportModules();
                    if (CollectionUtils.isNotEmpty(preSaleSupportModules)) {
                        for (PreSaleSupportAddRequest.PreSaleSupportModule preSaleSupportModule : preSaleSupportModules) {
                            DiPreSaleSupportModule diPreSaleSupportModule = new DiPreSaleSupportModule();
                            diPreSaleSupportModule.setDiPreSaleSupportDispositionId(diPreSaleSupportDisposition.getId());
                            diPreSaleSupportModule.setPreSaleSupportTreeId(diPreSaleSupportTree.getId());
                            diPreSaleSupportModule.setModuleType(preSaleSupportModule.getModuleType());
                            diPreSaleSupportModule.setMaterialCode(preSaleSupportModule.getMaterialCode());
                            diPreSaleSupportModule.setMaterialName(preSaleSupportModule.getMaterialName());
                            diPreSaleSupportModule.setSerialNumber(preSaleSupportModule.getSerialNumber());
                            diPreSaleSupportModule.setMaterialType(preSaleSupportModule.getMaterialType());
                            diPreSaleSupportModule.setPreSaleSupportId(diPreSaleSupport.getId());
                            diPreSaleSupportModule.setParentId(0);
                            diPreSaleSupportModuleMapper.insert(diPreSaleSupportModule);

                            if (CollectionUtils.isNotEmpty(preSaleSupportModule.getPreSaleSupportModule())) {
                                for (PreSaleSupportAddRequest.PreSaleSupportModule preSaleSupportModuleT : preSaleSupportModule.getPreSaleSupportModule()) {
                                    DiPreSaleSupportModule diPreSaleSupportModuleT = new DiPreSaleSupportModule();
                                    diPreSaleSupportModuleT.setDiPreSaleSupportDispositionId(diPreSaleSupportDisposition.getId());
                                    diPreSaleSupportModuleT.setPreSaleSupportTreeId(diPreSaleSupportTree.getId());
                                    diPreSaleSupportModuleT.setModuleType(preSaleSupportModuleT.getModuleType());
                                    diPreSaleSupportModuleT.setMaterialCode(preSaleSupportModuleT.getMaterialCode());
                                    diPreSaleSupportModuleT.setMaterialName(preSaleSupportModuleT.getMaterialName());
                                    diPreSaleSupportModuleT.setSerialNumber(preSaleSupportModuleT.getSerialNumber());
                                    diPreSaleSupportModuleT.setMaterialType(preSaleSupportModuleT.getMaterialType());
                                    diPreSaleSupportModuleT.setPreSaleSupportId(diPreSaleSupport.getId());
                                    diPreSaleSupportModuleT.setParentId(diPreSaleSupportModule.getId());
                                    diPreSaleSupportModuleMapper.insert(diPreSaleSupportModuleT);
                                }
                            }
                        }
                    }

                    //子树
                    List<PreSaleSupportAddRequest.PreSaleSupportTree> subTrees = preSaleSupportTree.getChildList();
                    if (CollectionUtils.isNotEmpty(subTrees)) {
                        doSupportTree(subTrees, diPreSaleSupportTree.getId(), diPreSaleSupportDisposition.getId(), diPreSaleSupport.getId());
                    }
                }
            }
        }

        try {
            //发送消息
            MqRelationBody mqRelationBody = new MqRelationBody();
            mqRelationBody.setProjectNo(diPreSaleSupport.getProjectCode());
            mqRelationBody.setRelationType(RelationTypeEnum.TECHNICAL_SUPPORT.getRelationType());
            mqRelationBody.setRelationNo(diPreSaleSupport.getPreSaleSupportCode());
            mqRelationBody.setStatus("0");
            mqSendMsg.sendMsg(BeanUtil.beanToMap(mqRelationBody), "relation");
        } catch (Exception e) {
            log.error("售前方案发送关联消息error{}", e.getMessage());
        }

        try {
            String projectNo = null;
            //获取项目编号
            if (StringUtils.isNotEmpty(request.getProjectCode())) {
                projectNo = request.getProjectCode();
            } else {
                //如果为空，根据售前方案id获取售前方案
                DiPreSale preSale = diPreSaleMapper.selectById(Long.valueOf(request.getDiPreSaleId()));
                if (null != preSale && StringUtils.isNotEmpty(preSale.getProjectCode())) {
                    projectNo = preSale.getProjectCode();
                }
            }
            if (StringUtils.isNotEmpty(projectNo)) {
                ProcessRelationUserMessageDTO processRelationUserMessageDTO = new ProcessRelationUserMessageDTO();
                processRelationUserMessageDTO.setProjectNo(projectNo);
                processRelationUserMessageDTO.setStatus("0");
                processRelationUserMessageDTO.setRelationUserList(authData);
                mqSendMsg.sendMsg(BeanUtil.beanToMap(processRelationUserMessageDTO), "relation_user");
            }
        } catch (Exception e) {
            log.error("发送售前数据权限{}", e);
        }

    }

    /**
     * 处理子树
     *
     * @param parentId
     */
    private void doSupportTree(List<PreSaleSupportAddRequest.PreSaleSupportTree> preSaleSupportTrees, Integer parentId, Integer dispositionId, Integer supportId) {
        for (PreSaleSupportAddRequest.PreSaleSupportTree preSaleSupportTree : preSaleSupportTrees) {
            DiPreSaleSupportTree diPreSaleSupportTree = new DiPreSaleSupportTree();
            diPreSaleSupportTree.setDiPreSaleSupportDispositionId(dispositionId);
            diPreSaleSupportTree.setParentId(parentId);
            diPreSaleSupportTree.setMaterialCode(preSaleSupportTree.getMaterialCode());
            diPreSaleSupportTree.setMaterialName(preSaleSupportTree.getMaterialName());
            diPreSaleSupportTreeMapper.insert(diPreSaleSupportTree);

            //模块配置
            List<PreSaleSupportAddRequest.PreSaleSupportModule> preSaleSupportModules = preSaleSupportTree.getPreSaleSupportModules();
            if (CollectionUtils.isNotEmpty(preSaleSupportModules)) {
                for (PreSaleSupportAddRequest.PreSaleSupportModule preSaleSupportModule : preSaleSupportModules) {
                    DiPreSaleSupportModule diPreSaleSupportModule = new DiPreSaleSupportModule();
                    diPreSaleSupportModule.setDiPreSaleSupportDispositionId(dispositionId);
                    diPreSaleSupportModule.setPreSaleSupportTreeId(diPreSaleSupportTree.getId());
                    diPreSaleSupportModule.setModuleType(preSaleSupportModule.getModuleType());
                    diPreSaleSupportModule.setMaterialCode(preSaleSupportModule.getMaterialCode());
                    diPreSaleSupportModule.setMaterialName(preSaleSupportModule.getMaterialName());
                    diPreSaleSupportModule.setSerialNumber(preSaleSupportModule.getSerialNumber());
                    diPreSaleSupportModule.setMaterialType(preSaleSupportModule.getMaterialType());
                    diPreSaleSupportModule.setPreSaleSupportId(supportId);
                    diPreSaleSupportModule.setParentId(0);
                    diPreSaleSupportModuleMapper.insert(diPreSaleSupportModule);

                    if (CollectionUtils.isNotEmpty(preSaleSupportModule.getPreSaleSupportModule())) {
                        for (PreSaleSupportAddRequest.PreSaleSupportModule preSaleSupportModuleT : preSaleSupportModule.getPreSaleSupportModule()) {
                            DiPreSaleSupportModule diPreSaleSupportModuleT = new DiPreSaleSupportModule();
                            diPreSaleSupportModuleT.setDiPreSaleSupportDispositionId(dispositionId);
                            diPreSaleSupportModuleT.setPreSaleSupportTreeId(diPreSaleSupportTree.getId());
                            diPreSaleSupportModuleT.setModuleType(preSaleSupportModuleT.getModuleType());
                            diPreSaleSupportModuleT.setMaterialCode(preSaleSupportModuleT.getMaterialCode());
                            diPreSaleSupportModuleT.setMaterialName(preSaleSupportModuleT.getMaterialName());
                            diPreSaleSupportModuleT.setSerialNumber(preSaleSupportModuleT.getSerialNumber());
                            diPreSaleSupportModuleT.setMaterialType(preSaleSupportModuleT.getMaterialType());
                            diPreSaleSupportModuleT.setPreSaleSupportId(supportId);
                            diPreSaleSupportModuleT.setParentId(diPreSaleSupportModule.getId());
                            diPreSaleSupportModuleMapper.insert(diPreSaleSupportModuleT);
                        }
                    }
                }
            }

            List<PreSaleSupportAddRequest.PreSaleSupportTree> subTrees = preSaleSupportTree.getChildList();
            if (CollectionUtils.isNotEmpty(subTrees)) {
                doSupportTree(subTrees, diPreSaleSupportTree.getId(), dispositionId, supportId);
            }
        }
    }

    /**
     * 修改售前方案支持
     *
     * @param request 售前方案支持
     * @return 结果
     */
    @Transactional
    @Override
    public int updateDiPreSaleSupport(PreSaleSupportUpdateRequest request) {

        DiPreSaleSupport diPreSaleSupportT = diPreSaleSupportMapper.selectById(request.getId());
        if (Objects.isNull(diPreSaleSupportT)) {
            throw new RuntimeException("id不存在");
        }

//        List<String> authData = new ArrayList<>();
        //修改技术支持
        DiPreSaleSupport diPreSaleSupport = new DiPreSaleSupport();
        diPreSaleSupport.setId(request.getId());
        diPreSaleSupport.setTelSupportHeader(request.getTelSupportHeader());
        diPreSaleSupport.setExpecteFinishDate(request.getExpecteFinishDate());
        //技术负责人
        if (StringUtils.isNotEmpty(request.getTelSupportHeader())) {
            //已分配
            diPreSaleSupport.setSupportStatus(1);
        }
        diPreSaleSupport.setUpdateTime(LocalDateTime.now());
        diPreSaleSupport.setUpdateBy(SecurityUtils.getUsername());
        diPreSaleSupportMapper.updateById(diPreSaleSupport);

        DiPreSale diPreSale = new DiPreSale();
        diPreSale.setId(diPreSaleSupportT.getDiPreSaleId());
        diPreSale.setRemark(request.getPreSaleRemark());
        diPreSaleMapper.updateById(diPreSale);

        diPreSaleUrlMapper.delete(Wrappers.<DiPreSaleUrl>lambdaUpdate().eq(DiPreSaleUrl::getDiPreSaleId, diPreSaleSupportT.getDiPreSaleId()).eq(DiPreSaleUrl::getType, "2"));

        if (CollectionUtils.isNotEmpty(request.getPreSaleUrl())) {
            List<DiPreSaleUrl> diPreSaleUrls = request.getPreSaleUrl().stream().map(s -> {
                DiPreSaleUrl diPreSaleUrl = new DiPreSaleUrl();
                diPreSaleUrl.setDiPreSaleId(diPreSaleSupportT.getDiPreSaleId());
                diPreSaleUrl.setType("2");
                diPreSaleUrl.setFileKey(s);
                return diPreSaleUrl;
            }).collect(Collectors.toList());
            diPreSaleUrlMapper.insertBatchSomeColumn(diPreSaleUrls);
        }

        ProcessRelationUserMessageDTO processRelationUserMessageDTO = new ProcessRelationUserMessageDTO();
        processRelationUserMessageDTO.setProjectNo(diPreSaleSupportT.getProjectCode());
        if (StringUtils.isEmpty(diPreSaleSupportT.getTelSupportHeader()) && StringUtils.isNotEmpty(request.getTelSupportHeader())) {
            //获取工号
            R<SysUser> userInfo = remoteUserService.userInfo(request.getTelSupportHeader());
            if (userInfo.isSuccess() && Objects.nonNull(userInfo.getData())
                    && StringUtils.isNotEmpty(userInfo.getData().getUserName())) {
                processRelationUserMessageDTO.setStatus("0");
                processRelationUserMessageDTO.setRelationUserList(Arrays.asList(userInfo.getData().getUserName()));
                mqSendMsg.sendMsg(BeanUtil.beanToMap(processRelationUserMessageDTO), "relation_user");
            }
        } else if (StringUtils.isNotEmpty(diPreSaleSupportT.getTelSupportHeader()) && StringUtils.isNotEmpty(request.getTelSupportHeader()) && !diPreSaleSupportT.getTelSupportHeader().equals(request.getTelSupportHeader())) {
            try {
                //获取老的负责人工号
                R<SysUser> userInfo = remoteUserService.userInfo(diPreSaleSupportT.getTelSupportHeader());
                if (userInfo.isSuccess() && Objects.nonNull(userInfo.getData())
                        && StringUtils.isNotEmpty(userInfo.getData().getUserName())) {
                    processRelationUserMessageDTO.setRelationUserList(Arrays.asList(userInfo.getData().getUserName()));
                    processRelationUserMessageDTO.setStatus("2");
                    mqSendMsg.sendMsg(BeanUtil.beanToMap(processRelationUserMessageDTO), "relation_user");
                }
            } catch (Exception e) {
                log.error("发送修改售前数据权限{}", e);
            }

            try {
                //获取新的负责人工号
                R<SysUser> userInfo = remoteUserService.userInfo(request.getTelSupportHeader());
                if (userInfo.isSuccess() && Objects.nonNull(userInfo.getData())
                        && StringUtils.isNotEmpty(userInfo.getData().getUserName())) {
                    processRelationUserMessageDTO.setRelationUserList(Arrays.asList(userInfo.getData().getUserName()));
                    processRelationUserMessageDTO.setStatus("0");
                    mqSendMsg.sendMsg(BeanUtil.beanToMap(processRelationUserMessageDTO), "relation_user");
                }
            } catch (Exception e) {
                log.error("发送修改售前数据权限{}", e);
            }
        }

        return 1;
    }

    @Override
    public void editDisposition(PreSaleSupportUpdateDispositionRequest request) {

        DiPreSaleSupport diPreSaleSupportT = diPreSaleSupportMapper.selectById(request.getPreSaleSupportId());
        if (Objects.isNull(diPreSaleSupportT)) {
            throw new RuntimeException("id不存在");
        }
        //方案清单
        PreSaleSupportUpdateDispositionRequest.PreSaleSupportManifest preSaleSupportManifest = request.getPreSaleSupportManifest();

        //id不为空。修改
        DiPreSaleSupportManifest diPreSaleSupportManifest = preSaleConverUtil.converToUpdateMainfest(preSaleSupportManifest);
        if (Objects.nonNull(diPreSaleSupportManifest.getId())) {
            diPreSaleSupportManifestMapper.updateById(diPreSaleSupportManifest);
        } else {
            DiPreSaleManifest diPreSaleManifest = preSaleConverUtil.converToUpdatePreMainfest(preSaleSupportManifest);
            diPreSaleManifest.setDiPreSaleId(diPreSaleSupportT.getDiPreSaleId());
            diPreSaleManifest.setId(null);
            diPreSaleManifestMapper.insert(diPreSaleManifest);

            diPreSaleSupportManifest.setPreSaleManifestId(diPreSaleManifest.getId());
            diPreSaleSupportManifestMapper.insert(diPreSaleSupportManifest);
        }

        //方案配置
        PreSaleSupportUpdateDispositionRequest.PreSaleSupportDisposition preSaleSupportDisposition = preSaleSupportManifest.getPreSaleSupportDisposition();
        DiPreSaleSupportDisposition diPreSaleSupportDisposition = preSaleConverUtil.converToUpdateDisposition(preSaleSupportDisposition);
        if (Objects.nonNull(diPreSaleSupportDisposition.getId())) {
            diPreSaleSupportDispositionMapper.updateById(diPreSaleSupportDisposition);

        } else {
            diPreSaleSupportDispositionMapper.insert(diPreSaleSupportDisposition);

        }

        diPreSaleSupportUrlMapper.delete(Wrappers.<DiPreSaleSupportUrl>lambdaUpdate().eq(DiPreSaleSupportUrl::getPreSaleSupportManifestId, preSaleSupportManifest.getId()));
        List<DiPreSaleSupportUrl> diPreSaleSupportUrls = new ArrayList<>();
        //PID图纸
        if (CollectionUtils.isNotEmpty(preSaleSupportDisposition.getPidUrls())) {
            for (String url : preSaleSupportDisposition.getPidUrls()) {
                DiPreSaleSupportUrl diPreSaleSupportUrl = new DiPreSaleSupportUrl();
                diPreSaleSupportUrl.setDiPreSaleSupportId(diPreSaleSupportT.getId());
                diPreSaleSupportUrl.setPreSaleSupportManifestId(preSaleSupportManifest.getId());
                diPreSaleSupportUrl.setType(1);
                diPreSaleSupportUrl.setFileKey(url);
                diPreSaleSupportUrls.add(diPreSaleSupportUrl);
            }
        }

        //技术协议
        if (CollectionUtils.isNotEmpty(preSaleSupportDisposition.getTelUrls())) {
            for (String url : preSaleSupportDisposition.getTelUrls()) {
                DiPreSaleSupportUrl diPreSaleSupportUrl = new DiPreSaleSupportUrl();
                diPreSaleSupportUrl.setDiPreSaleSupportId(diPreSaleSupportT.getId());
                diPreSaleSupportUrl.setPreSaleSupportManifestId(preSaleSupportManifest.getId());
                diPreSaleSupportUrl.setType(2);
                diPreSaleSupportUrl.setFileKey(url);
                diPreSaleSupportUrls.add(diPreSaleSupportUrl);
            }
        }
        if (CollectionUtils.isNotEmpty(diPreSaleSupportUrls)) {
            diPreSaleSupportUrlMapper.insertBatchSomeColumn(diPreSaleSupportUrls);
        }

        //结构树
        PreSaleSupportUpdateDispositionRequest.PreSaleSupportTree preSaleSupportTree = preSaleSupportDisposition.getPreSaleSupportTree();
        if (Objects.nonNull(preSaleSupportTree)) {

            DiPreSaleSupportTree diPreSaleSupportTree = new DiPreSaleSupportTree();
            diPreSaleSupportTree.setDiPreSaleSupportDispositionId(diPreSaleSupportDisposition.getId());
            diPreSaleSupportTree.setParentId(preSaleSupportTree.getParentId());
            diPreSaleSupportTree.setMaterialCode(preSaleSupportTree.getMaterialCode());
            diPreSaleSupportTree.setMaterialName(preSaleSupportTree.getMaterialName());
            diPreSaleSupportTree.setId(preSaleSupportTree.getId());
            if (Objects.nonNull(preSaleSupportTree.getId())) {
                diPreSaleSupportTreeMapper.updateById(diPreSaleSupportTree);
            } else {
                diPreSaleSupportTreeMapper.insert(diPreSaleSupportTree);
            }

            List<PreSaleSupportUpdateDispositionRequest.PreSaleSupportModule> preSaleSupportModules = preSaleSupportTree.getPreSaleSupportModules();
            if (CollectionUtils.isNotEmpty(preSaleSupportModules)) {

                //模块配置
                List<DiPreSaleSupportModule> diPreSaleSupportModules = diPreSaleSupportModuleMapper.selectList(Wrappers.<DiPreSaleSupportModule>lambdaQuery().eq(DiPreSaleSupportModule::getPreSaleSupportTreeId, preSaleSupportTree.getId()));

                //需要更新的
                List<PreSaleSupportUpdateDispositionRequest.PreSaleSupportModule> updateModules = preSaleSupportModules.stream().filter(preSaleSupportModule -> Objects.nonNull(preSaleSupportModule.getId())).collect(Collectors.toList());

                //存在id
                List<Integer> moduleIds = updateModules.stream().map(PreSaleSupportUpdateDispositionRequest.PreSaleSupportModule::getId).collect(Collectors.toList());
                List<DiPreSaleSupportModule> deleteModules = diPreSaleSupportModules.stream().filter(diPreSaleSupportModule -> !moduleIds.contains(diPreSaleSupportModule.getId())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(deleteModules)) {
                    diPreSaleSupportModuleMapper.deleteBatchIds(deleteModules.stream().map(DiPreSaleSupportModule::getId).collect(Collectors.toList()));
                    diPreSaleSupportModuleMapper.delete(Wrappers.<DiPreSaleSupportModule>lambdaUpdate().in(DiPreSaleSupportModule::getParentId, deleteModules.stream().map(DiPreSaleSupportModule::getId).collect(Collectors.toList())));
                }

                for (PreSaleSupportUpdateDispositionRequest.PreSaleSupportModule preSaleSupportModule : updateModules) {
                    DiPreSaleSupportModule diPreSaleSupportModule = preSaleConverUtil.converToUpdateModule(preSaleSupportModule);
                    diPreSaleSupportModuleMapper.updateById(diPreSaleSupportModule);
                    if (CollectionUtils.isNotEmpty(preSaleSupportModule.getSubModule())) {
                        for (PreSaleSupportUpdateDispositionRequest.PreSaleSupportModule preSaleSupportModuleT : preSaleSupportModule.getSubModule()) {
                            DiPreSaleSupportModule diPreSaleSupportModuleT = new DiPreSaleSupportModule();
                            diPreSaleSupportModuleT.setDiPreSaleSupportDispositionId(diPreSaleSupportDisposition.getId());
                            diPreSaleSupportModuleT.setPreSaleSupportTreeId(diPreSaleSupportTree.getId());
                            diPreSaleSupportModuleT.setModuleType(preSaleSupportModuleT.getModuleType());
                            diPreSaleSupportModuleT.setMaterialCode(preSaleSupportModuleT.getMaterialCode());
                            diPreSaleSupportModuleT.setMaterialName(preSaleSupportModuleT.getMaterialName());
                            diPreSaleSupportModuleT.setSerialNumber(preSaleSupportModuleT.getSerialNumber());
                            diPreSaleSupportModuleT.setMaterialType(preSaleSupportModuleT.getMaterialType());
                            diPreSaleSupportModuleT.setPreSaleSupportId(diPreSaleSupportT.getId());
                            diPreSaleSupportModuleT.setParentId(diPreSaleSupportModule.getId());
                            diPreSaleSupportModuleT.setId(preSaleSupportModuleT.getId());
                            if (Objects.isNull(preSaleSupportModuleT.getId())) {

                                diPreSaleSupportModuleMapper.insert(diPreSaleSupportModuleT);
                            } else {
                                diPreSaleSupportModuleMapper.updateById(diPreSaleSupportModuleT);
                            }
                        }
                    }
                }

                //id为空，需要保存的数据
                List<PreSaleSupportUpdateDispositionRequest.PreSaleSupportModule> addModules = preSaleSupportModules.stream().filter(preSaleSupportModule -> Objects.isNull(preSaleSupportModule.getId())).collect(Collectors.toList());

                for (PreSaleSupportUpdateDispositionRequest.PreSaleSupportModule preSaleSupportModule : addModules) {
                    DiPreSaleSupportModule diPreSaleSupportModule = preSaleConverUtil.converToUpdateModule(preSaleSupportModule);
                    diPreSaleSupportModule.setPreSaleSupportTreeId(preSaleSupportTree.getId());
                    diPreSaleSupportModule.setPreSaleSupportId(request.getPreSaleSupportId());
                    diPreSaleSupportModule.setDiPreSaleSupportDispositionId(preSaleSupportDisposition.getId());
                    diPreSaleSupportModuleMapper.insert(diPreSaleSupportModule);

                    if (CollectionUtils.isNotEmpty(preSaleSupportModule.getSubModule())) {
                        for (PreSaleSupportUpdateDispositionRequest.PreSaleSupportModule preSaleSupportModuleT : preSaleSupportModule.getSubModule()) {
                            DiPreSaleSupportModule diPreSaleSupportModuleT = new DiPreSaleSupportModule();
                            diPreSaleSupportModuleT.setDiPreSaleSupportDispositionId(diPreSaleSupportDisposition.getId());
                            diPreSaleSupportModuleT.setPreSaleSupportTreeId(diPreSaleSupportTree.getId());
                            diPreSaleSupportModuleT.setModuleType(preSaleSupportModuleT.getModuleType());
                            diPreSaleSupportModuleT.setMaterialCode(preSaleSupportModuleT.getMaterialCode());
                            diPreSaleSupportModuleT.setMaterialName(preSaleSupportModuleT.getMaterialName());
                            diPreSaleSupportModuleT.setSerialNumber(preSaleSupportModuleT.getSerialNumber());
                            diPreSaleSupportModuleT.setMaterialType(preSaleSupportModuleT.getMaterialType());
                            diPreSaleSupportModuleT.setPreSaleSupportId(diPreSaleSupportT.getId());
                            diPreSaleSupportModuleT.setParentId(diPreSaleSupportModule.getId());
                            diPreSaleSupportModuleMapper.insert(diPreSaleSupportModuleT);
                        }
                    }
                }
            }

            updateTree(preSaleSupportTree.getChildList(), diPreSaleSupportDisposition.getId(), preSaleSupportTree.getId(), request.getPreSaleSupportId());
        }
    }

    private void updateTree(List<PreSaleSupportUpdateDispositionRequest.PreSaleSupportTree> preSaleSupportTrees, Integer dispositionId, Integer parentId, Integer supportId) {
        if (CollectionUtils.isNotEmpty(preSaleSupportTrees)) {
            for (PreSaleSupportUpdateDispositionRequest.PreSaleSupportTree preSaleSupportTree : preSaleSupportTrees) {

                DiPreSaleSupportTree diPreSaleSupportTree = new DiPreSaleSupportTree();
                diPreSaleSupportTree.setDiPreSaleSupportDispositionId(dispositionId);
                diPreSaleSupportTree.setParentId(parentId);
                diPreSaleSupportTree.setMaterialCode(preSaleSupportTree.getMaterialCode());
                diPreSaleSupportTree.setMaterialName(preSaleSupportTree.getMaterialName());
                diPreSaleSupportTree.setId(preSaleSupportTree.getId());
                if (Objects.isNull(preSaleSupportTree.getId())) {
                    diPreSaleSupportTreeMapper.insert(diPreSaleSupportTree);

                    preSaleSupportTree.setId(diPreSaleSupportTree.getId());
                } else {
                    diPreSaleSupportTreeMapper.updateById(diPreSaleSupportTree);
                }

                if (Objects.nonNull(preSaleSupportTree)) {
                    List<PreSaleSupportUpdateDispositionRequest.PreSaleSupportModule> preSaleSupportModules = preSaleSupportTree.getPreSaleSupportModules();
                    if (CollectionUtils.isNotEmpty(preSaleSupportModules)) {

                        //模块配置
                        List<DiPreSaleSupportModule> diPreSaleSupportModules = diPreSaleSupportModuleMapper.selectList(Wrappers.<DiPreSaleSupportModule>lambdaQuery().eq(DiPreSaleSupportModule::getPreSaleSupportTreeId, preSaleSupportTree.getId()));

                        //需要更新的
                        List<PreSaleSupportUpdateDispositionRequest.PreSaleSupportModule> updateModules = preSaleSupportModules.stream().filter(preSaleSupportModule -> Objects.nonNull(preSaleSupportModule.getId())).collect(Collectors.toList());

                        //存在id
                        List<Integer> moduleIds = updateModules.stream().map(PreSaleSupportUpdateDispositionRequest.PreSaleSupportModule::getId).collect(Collectors.toList());
                        List<DiPreSaleSupportModule> deleteModules = diPreSaleSupportModules.stream().filter(diPreSaleSupportModule -> !moduleIds.contains(diPreSaleSupportModule.getId())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(deleteModules)) {
                            diPreSaleSupportModuleMapper.deleteBatchIds(deleteModules.stream().map(DiPreSaleSupportModule::getId).collect(Collectors.toList()));
                            diPreSaleSupportModuleMapper.delete(Wrappers.<DiPreSaleSupportModule>lambdaUpdate().in(DiPreSaleSupportModule::getParentId, deleteModules.stream().map(DiPreSaleSupportModule::getId).collect(Collectors.toList())));
                        }
                        for (PreSaleSupportUpdateDispositionRequest.PreSaleSupportModule preSaleSupportModule : updateModules) {
                            DiPreSaleSupportModule diPreSaleSupportModule = preSaleConverUtil.converToUpdateModule(preSaleSupportModule);
                            diPreSaleSupportModuleMapper.updateById(diPreSaleSupportModule);
                            if (CollectionUtils.isNotEmpty(preSaleSupportModule.getSubModule())) {
                                for (PreSaleSupportUpdateDispositionRequest.PreSaleSupportModule preSaleSupportModuleT : preSaleSupportModule.getSubModule()) {
                                    DiPreSaleSupportModule diPreSaleSupportModuleT = new DiPreSaleSupportModule();
                                    diPreSaleSupportModuleT.setDiPreSaleSupportDispositionId(dispositionId);
                                    diPreSaleSupportModuleT.setPreSaleSupportTreeId(diPreSaleSupportTree.getId());
                                    diPreSaleSupportModuleT.setModuleType(preSaleSupportModuleT.getModuleType());
                                    diPreSaleSupportModuleT.setMaterialCode(preSaleSupportModuleT.getMaterialCode());
                                    diPreSaleSupportModuleT.setMaterialName(preSaleSupportModuleT.getMaterialName());
                                    diPreSaleSupportModuleT.setSerialNumber(preSaleSupportModuleT.getSerialNumber());
                                    diPreSaleSupportModuleT.setMaterialType(preSaleSupportModuleT.getMaterialType());
                                    diPreSaleSupportModuleT.setPreSaleSupportId(supportId);
                                    diPreSaleSupportModuleT.setParentId(diPreSaleSupportModule.getId());
                                    diPreSaleSupportModuleT.setId(preSaleSupportModuleT.getId());
                                    if (Objects.isNull(preSaleSupportModuleT.getId())) {

                                        diPreSaleSupportModuleMapper.insert(diPreSaleSupportModuleT);
                                    } else {
                                        diPreSaleSupportModuleMapper.updateById(diPreSaleSupportModuleT);
                                    }
                                }
                            }
                        }

                        //id为空，需要保存的数据
                        List<PreSaleSupportUpdateDispositionRequest.PreSaleSupportModule> addModules = preSaleSupportModules.stream().filter(preSaleSupportModule -> Objects.isNull(preSaleSupportModule.getId())).collect(Collectors.toList());

                        if (CollectionUtils.isNotEmpty(addModules)) {
                            for (PreSaleSupportUpdateDispositionRequest.PreSaleSupportModule preSaleSupportModule : addModules) {
                                DiPreSaleSupportModule diPreSaleSupportModule = preSaleConverUtil.converToUpdateModule(preSaleSupportModule);
                                diPreSaleSupportModule.setPreSaleSupportTreeId(preSaleSupportTree.getId());
                                diPreSaleSupportModule.setPreSaleSupportId(supportId);
                                diPreSaleSupportModule.setDiPreSaleSupportDispositionId(dispositionId);
                                diPreSaleSupportModule.setParentId(0);
                                diPreSaleSupportModuleMapper.insert(diPreSaleSupportModule);

                                if (CollectionUtils.isNotEmpty(preSaleSupportModule.getSubModule())) {
                                    for (PreSaleSupportUpdateDispositionRequest.PreSaleSupportModule preSaleSupportModuleT : preSaleSupportModule.getSubModule()) {
                                        DiPreSaleSupportModule diPreSaleSupportModuleT = new DiPreSaleSupportModule();
                                        diPreSaleSupportModuleT.setDiPreSaleSupportDispositionId(dispositionId);
                                        diPreSaleSupportModuleT.setPreSaleSupportTreeId(diPreSaleSupportTree.getId());
                                        diPreSaleSupportModuleT.setModuleType(preSaleSupportModuleT.getModuleType());
                                        diPreSaleSupportModuleT.setMaterialCode(preSaleSupportModuleT.getMaterialCode());
                                        diPreSaleSupportModuleT.setMaterialName(preSaleSupportModuleT.getMaterialName());
                                        diPreSaleSupportModuleT.setSerialNumber(preSaleSupportModuleT.getSerialNumber());
                                        diPreSaleSupportModuleT.setMaterialType(preSaleSupportModuleT.getMaterialType());
                                        diPreSaleSupportModuleT.setPreSaleSupportId(supportId);
                                        diPreSaleSupportModuleT.setParentId(diPreSaleSupportModule.getId());
                                        diPreSaleSupportModuleMapper.insert(diPreSaleSupportModuleT);
                                    }
                                }
                            }
                        }
                    }
                    updateTree(preSaleSupportTree.getChildList(), dispositionId, preSaleSupportTree.getId(), supportId);
                }
            }
        }
    }

    /**
     * 批量删除售前方案支持
     *
     * @param ids 需要删除的售前方案支持主键
     * @return 结果
     */
    @Override
    public int deleteDiPreSaleSupportByIds(Long[] ids) {
        return diPreSaleSupportMapper.deleteDiPreSaleSupportByIds(ids);
    }

    /**
     * 删除售前方案支持信息
     *
     * @param id 售前方案支持主键
     * @return 结果
     */
    @Override
    public int deleteDiPreSaleSupportById(Long id) {
        return diPreSaleSupportMapper.deleteDiPreSaleSupportById(id);
    }
}

package com.dyd.di.marketing.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dingtalk.api.response.OapiProcessinstanceGetResponse;
import com.dyd.common.core.utils.DateUtils;
import com.dyd.di.home.domain.DdUser;
import com.dyd.di.home.mapper.DdUserMapper;
import com.dyd.di.marketing.domain.DdOaRecord;
import com.dyd.di.marketing.enums.LeaveStatusEnum;
import com.dyd.di.marketing.mapper.DdOaRecordMapper;
import com.dyd.di.marketing.service.DdOaRecordService;
import com.dyd.di.message.service.IDiMessageListService;
import com.dyd.dingtalk.DingtalkClient;
import com.dyd.dingtalk.bean.attendance.GetleaveStatusRequest;
import com.dyd.dingtalk.bean.attendance.GetleaveStatusResponse;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @description 针对表【dd_oa_record(钉钉考勤OA数据)】的数据库操作Service实现
 * @createDate 2024-12-05 11:02:09
 */
@Slf4j
@Service
public class DdOaRecordServiceImpl extends ServiceImpl<DdOaRecordMapper, DdOaRecord>
        implements DdOaRecordService {

    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Autowired
    private DingtalkClient dingtalkClient;

    @Autowired
    private DdUserMapper ddUserMapper;

    @Autowired
    private IDiMessageListService diMessageListService;

    @Autowired
    private DdOaRecordService ddOaRecordService;

    @Override
    public void syncOaRecord(String syncOaRecordDay) {
        if(null == syncOaRecordDay){
            syncOaRecordDay = DateUtils.parseDateToStr("yyyy-MM-dd", new Date());
        }

        try {
            List<DdOaRecord> ddOaRecords = new ArrayList<>();

            //请假
            getHoliday(ddOaRecords, syncOaRecordDay);

            //外出
            getEvection(ddOaRecords, syncOaRecordDay);

            //商旅出差
            getBusinessTravel(ddOaRecords, syncOaRecordDay);

            //异地/居家办公
            getOffsite(ddOaRecords, syncOaRecordDay);

            Set<DdOaRecord> set = new LinkedHashSet<>(ddOaRecords);
            ddOaRecords.clear();
            ddOaRecords.addAll(set);

            ddOaRecordService.saveBatch(ddOaRecords);
        } catch (Exception e) {
            log.error("同步钉钉OA入库失败" + syncOaRecordDay, e);

            //发送失败信息同步到钉钉消息
            diMessageListService.errorPush("syncOaRecord", "同步钉钉OA入库定时任务", e.getMessage(),"16625119062366702");
        }
    }

    /**
     * 请假
     *
     * @param ddOaRecords
     * @param syncOaRecordDay
     * @throws ParseException
     */
    public void getHoliday(List<DdOaRecord> ddOaRecords, String syncOaRecordDay) throws ParseException {
        String startStr = syncOaRecordDay + " 00:00:00";
        String endStr = syncOaRecordDay + " 23:59:59";

        //请假
        List<DdUser> list = ddUserMapper.selectList(Wrappers.<DdUser>lambdaQuery()
                .like(DdUser::getJobNumber, "dyd")
                .eq(DdUser::getDelFlag, 0));

        for (DdUser ddUser : list) {
            GetleaveStatusRequest getleaveStatusRequest = GetleaveStatusRequest.builder()
                    .userid_list(ddUser.getUserId())
                    .start_time(simpleDateFormat.parse(startStr).getTime())
                    .end_time(simpleDateFormat.parse(endStr).getTime())
                    .offset(0)
                    .size(1)
                    .build();
            GetleaveStatusResponse getleaveStatusResponse = dingtalkClient.dingtalkUserCall(getleaveStatusRequest);
            if(null != getleaveStatusResponse && null != getleaveStatusResponse.getResult()){
                for (GetleaveStatusResponse.LeaveStatusVO leaveStatusVO : getleaveStatusResponse.getResult().getLeave_status()) {
                    String leaveStatus = LeaveStatusEnum.getDesc(leaveStatusVO.getLeave_code());
                    if (StrUtil.isBlank(leaveStatus)) {
                        leaveStatus = leaveStatusVO.getLeave_code();
                    }
                    String durationUnit = "全天";
                    //小时
                    if ("percent_hour".equals(leaveStatusVO.getDuration_unit()) && 800 > leaveStatusVO.getDuration_percent()) {
                        //半天
                        durationUnit = "半天";
                    }
                    //天
                    if ("percent_day".equals(leaveStatusVO.getDuration_unit()) && 100 > leaveStatusVO.getDuration_percent()) {
                        //半天
                        durationUnit = "半天";
                    }
                    createEmployeeHealth(
                            ddOaRecords,
                            new Date(leaveStatusVO.getStart_time()),
                            new Date(leaveStatusVO.getEnd_time()),
                            leaveStatus, ddUser.getUserId(),
                            ddUser.getDeptIdList(),
                            syncOaRecordDay,
                            durationUnit);
                }
            }
        }
    }

    /**
     * 外出
     *
     * @param ddOaRecords
     * @param syncOaRecordDay
     * @throws ParseException
     * @throws ApiException
     */
    public void getEvection(List<DdOaRecord> ddOaRecords, String syncOaRecordDay) throws ParseException, ApiException {
        String startStr = DateUtils.parseDateToStr("yyyy-MM-dd",
                DateUtils.addDays(DateUtils.parseDate(syncOaRecordDay), -1)) + " 09:33:00";

        String endStr = syncOaRecordDay + " 09:32:59";
        String processCode = "PROC-EF6YCS6WO2-LBO32GWWMPD5Q38L02P52-TMW1TKEI-2U";
        List<String> idList = dingtalkClient.getAllProcessIdList(processCode, simpleDateFormat.parse(startStr).getTime(),
                simpleDateFormat.parse(endStr).getTime());

        for (String id : idList) {
            OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstanceTopVo = dingtalkClient.getProcessInfo(id);
            JSONArray jsonArray = JSONArray.parseArray(processInstanceTopVo.getFormComponentValues().get(8).getValue());
            String userId = processInstanceTopVo.getOriginatorUserid();
            String DeptId = processInstanceTopVo.getOriginatorDeptId();
            String startTime = jsonArray.get(0).toString().substring(0, 10);
            String endTime = jsonArray.get(1).toString().substring(0, 10);
            Double sc = Double.valueOf(jsonArray.get(2).toString());
            int days = diffDate(startTime, endTime);
            for (int i = 0; i <= days; i++) {
                String durationUnit = "全天";
                if (0 == days && 8.0 > sc) {
                    //半天
                    durationUnit = "半天";
                }
                Date date = DateUtils.parseDate(startTime);
                createEmployeeHealth(
                        ddOaRecords,
                        DateUtils.parseDate(jsonArray.get(0)),
                        DateUtils.parseDate(jsonArray.get(1)),
                        "外出",
                        userId,
                        DeptId,
                        DateUtils.parseDateToStr("yyyy-MM-dd",
                                DateUtils.addDays(date, i)),
                        durationUnit);
            }
        }
    }

    /**
     * 商旅出差
     *
     * @param ddOaRecords
     * @param syncOaRecordDay
     * @throws ParseException
     * @throws ApiException
     */
    public void getBusinessTravel(List<DdOaRecord> ddOaRecords, String syncOaRecordDay) throws ParseException, ApiException {
        String startStr = DateUtils.parseDateToStr("yyyy-MM-dd",
                DateUtils.addDays(DateUtils.parseDate(syncOaRecordDay), -1)) + " 09:33:00";
        String endStr = syncOaRecordDay + " 09:32:59";
        String processCode = "PROC-919A7F8C-2B1F-4BAA-9417-EC42CDE56E83";
        List<String> idList = dingtalkClient.getAllProcessIdList(processCode, simpleDateFormat.parse(startStr).getTime(),
                simpleDateFormat.parse(endStr).getTime());
        for (String id : idList) {
            OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstanceTopVo = dingtalkClient.getProcessInfo(id);
            String userId = processInstanceTopVo.getOriginatorUserid();
            String DeptId = processInstanceTopVo.getOriginatorDeptId();
            JSONArray infoJsonArray = JSONArray.parseArray(((JSONObject) JSONArray.parseArray(processInstanceTopVo
                    .getFormComponentValues().get(2).getValue()).get(2)).get("value").toString());
            for (int i = 0; i < infoJsonArray.size(); i++) {
                String startTime = ((JSONObject) JSONArray.parseArray(((JSONObject) infoJsonArray.get(i))
                        .get("rowValue").toString()).get(4)).get("value").toString();
                String endTime = ((JSONObject) JSONArray.parseArray(((JSONObject) infoJsonArray.get(i))
                        .get("rowValue").toString()).get(5)).get("value").toString();
                int days = diffDate(startTime, endTime);
                for (int j = 0; j <= days; j++) {
                    Date date = DateUtils.parseDate(startTime);
                    createEmployeeHealth(ddOaRecords,
                            DateUtils.parseDate(startTime),
                            DateUtils.parseDate(endTime),
                            "商旅出差",
                            userId,
                            DeptId,
                            DateUtils.parseDateToStr("yyyy-MM-dd", DateUtils.addDays(date, j)),
                            "全天");
                }
            }
        }
    }

    /**
     * 异地/居家办公
     *
     * @param ddOaRecords
     * @param syncOaRecordDay
     * @throws ParseException
     * @throws ApiException
     */
    public void getOffsite(List<DdOaRecord> ddOaRecords, String syncOaRecordDay) throws ParseException, ApiException {
        String startStr = DateUtils.parseDateToStr("yyyy-MM-dd", DateUtils.addDays(DateUtils.parseDate(syncOaRecordDay), -1)) + " 09:33:00";
        String endStr = syncOaRecordDay + " 09:32:59";

        String processCode = "PROC-FF6YRLE1N2-WMEJFHP0USR34653KV9M1-K1VK4N0J-3";
        List<String> idList = dingtalkClient.getAllProcessIdList(processCode, simpleDateFormat.parse(startStr).getTime(),
                simpleDateFormat.parse(endStr).getTime());
        for (String id : idList) {
            OapiProcessinstanceGetResponse.ProcessInstanceTopVo processInstanceTopVo = dingtalkClient.getProcessInfo(id);
            JSONArray infoJsonArray = JSONArray.parseArray((JSONArray.parseArray(processInstanceTopVo
                    .getFormComponentValues().get(3).getValue())).toString());
            String userId = processInstanceTopVo.getOriginatorUserid();
            String DeptId = processInstanceTopVo.getOriginatorDeptId();
            String startTime = infoJsonArray.get(0).toString();
            String endTime = infoJsonArray.get(1).toString();
            int days = diffDate(startTime, endTime);
            for (int i = 0; i <= days; i++) {
                Date date = DateUtils.parseDate(startTime);
                createEmployeeHealth(
                        ddOaRecords,
                        DateUtils.parseDate(startTime),
                        DateUtils.parseDate(endTime),
                        "异地/居家办公",
                        userId,
                        DeptId,
                        DateUtils.parseDateToStr("yyyy-MM-dd",
                                DateUtils.addDays(date, i)),
                        "全天");
            }
        }
    }

    public void createEmployeeHealth(List<DdOaRecord> ddOaRecords, Date startTime, Date endTime,
                                     String leaveStatus, String userName, String userDept, String date, String durationUnit) {
        DdOaRecord oaRecord = new DdOaRecord();
        oaRecord.setDuration(durationUnit);
        oaRecord.setUserId(userName);
        oaRecord.setOaType(leaveStatus);
        oaRecord.setOaDate(date);
        oaRecord.setStartTime(startTime);
        oaRecord.setEndTime(endTime);
        oaRecord.setSyncTime(new Date());
        ddOaRecords.add(oaRecord);
    }

    /**
     * 计算两个字符串日期之间的天数差
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public int diffDate(String startDate, String endDate) {
        // 获取日历对象
        Calendar calendar1 = Calendar.getInstance();
        Calendar calendar2 = Calendar.getInstance();
        Date date1;
        Date date2;
        // 字符串转Date
        date1 = DateUtils.parseDate(startDate);
        date2 = DateUtils.parseDate(endDate);
        // 设置日历
        calendar1.setTime(date1);
        calendar2.setTime(date2);
        long time1 = calendar1.getTimeInMillis();
        long time2 = calendar2.getTimeInMillis();
        // 计算相差天数
        return (int) ((time2 - time1) / (1000 * 3600 * 24));
    }
}

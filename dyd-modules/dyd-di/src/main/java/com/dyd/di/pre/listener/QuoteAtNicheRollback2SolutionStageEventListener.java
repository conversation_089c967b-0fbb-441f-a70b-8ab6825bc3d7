package com.dyd.di.pre.listener;

import com.dyd.common.core.enums.PreSaleQuoteStatusEnum;
import com.dyd.di.marketing.domain.event.NicheRollback2SolutionStageEvent;
import com.dyd.di.marketing.enums.NicheStatusEnum;
import com.dyd.di.pre.domain.request.PreSaleQuoteEditRequest;
import com.dyd.di.pre.entity.DiPreSaleQuote;
import com.dyd.di.pre.service.IPreSaleQuoteService;
import com.dyd.di.pre.service.impl.PreSaleQuoteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class QuoteAtNicheRollback2SolutionStageEventListener {

    @Autowired
    private IPreSaleQuoteService quoteService;

    @Autowired
    private PreSaleQuoteService preSaleQuoteService;

    @EventListener(classes = NicheRollback2SolutionStageEvent.class)
    public void handle(NicheRollback2SolutionStageEvent event) {
        List<DiPreSaleQuote> quoteList = quoteService.queryByNicheCode(event.getNicheCode());
        if (CollectionUtils.isEmpty(quoteList)) {
            log.info("没有关联的方案报价列表");
            return;
        }
        for (DiPreSaleQuote quote : quoteList) {
            if (PreSaleQuoteStatusEnum.GIVE_UP.getCode().equals(quote.getQuoteStatus())) {
                log.info("报价单{},{}已经放弃，不进行回滚", quote.getId(), quote.getPreSaleQuoteCode());
                continue;
            }
            PreSaleQuoteEditRequest request = new PreSaleQuoteEditRequest();
            request.setId(quote.getId());
            preSaleQuoteService.abandonQuote(request, NicheStatusEnum.SOLUTION_STAGE.getCode());
        }
    }
}

package com.dyd.di.materiel.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

@Data
public class MaterielAddSupplierVo {


    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供应商等级
     */
    private String supplierLevel;

    /**
     * 供应商状态
     */
    private Long supplierStatus;

    /**
     * 优先级权重
     */
    private Long supplierPriority;

    /**
     * 负责人
     */
    private String majorBy;

    /**
     * 供应商报价 分
     */
    private Long materialCosts;

    /**
     * 供应商货期 天
     */
    private Long supplierDeliveryDate;

    /**
     * 供应商指标
     */
    private String supplierTarget;

    /**
     * 报价有效期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date costsValidTime;
}

package com.dyd.di.material.iservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dyd.di.material.domain.dto.SaveDiMaterialInquiryFormDto;
import com.dyd.di.material.entity.DiMaterialInquiryForm;

import java.util.List;

/**
 * 采购_物料询价单 IService接口用来做隔离,防止循环依赖
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
public interface IDiMaterialInquiryFormService extends IService<DiMaterialInquiryForm> {

    /**
     * 询询价单是否包含该物料
     *
     * @param selectionId
     * @return
     */
    boolean inInquiryForm(Long selectionId);
}

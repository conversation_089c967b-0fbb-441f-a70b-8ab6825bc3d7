package com.dyd.di.interceptor.diPreSaleQuote;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dyd.common.core.utils.StringUtils;
import com.dyd.di.comment.enums.CommentStageEnum;
import com.dyd.di.interceptor.entity.DiProjectStageChange;
import com.dyd.di.interceptor.mapper.DiProjectStageChangeMapper;
import com.dyd.di.marketing.domain.DiMarketingNiche;
import com.dyd.di.marketing.mapper.DiMarketingNicheMapper;
import com.dyd.di.pre.entity.DiPreSaleQuote;
import com.dyd.di.pre.mapper.DiPreSaleQuoteMapper;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

@Component
public class DiPreSaleQuoteCreateListener {
    @Autowired
    private DiPreSaleQuoteMapper preSaleQuoteMapper;
    @Autowired
    private DiMarketingNicheMapper nicheMapper;
    @Autowired
    private DiProjectStageChangeMapper projectStageChangeMapper;

    @Async
    @EventListener
    public void handleDataChange(DiPreSaleQuoteCreateEvent event) {
        handleDataChange0(event);
    }

    public void handleDataChange0(DiPreSaleQuoteCreateEvent event) {
        String preSaleQuoteCode = event.getPreSaleQuoteCode();
        DiPreSaleQuote preSaleQuote =
                preSaleQuoteMapper.selectOne(Wrappers.<DiPreSaleQuote>lambdaQuery().eq(DiPreSaleQuote::getPreSaleQuoteCode, preSaleQuoteCode));
        if (preSaleQuote != null) {
            DiMarketingNiche niche =
                    nicheMapper.selectOne(Wrappers.<DiMarketingNiche>lambdaQuery().eq(DiMarketingNiche::getNicheNo, preSaleQuote.getNicheCode()));
            if (niche != null) {
                DiProjectStageChange diProjectStageChange = projectStageChangeMapper.selectOne(Wrappers.<DiProjectStageChange>lambdaQuery()
                        .in(DiProjectStageChange::getRefNo,
                                Lists.newArrayList(niche.getNicheNo(), niche.getClueNo())
                                        .stream()
                                        .filter(StringUtils::hasText).toList())
                        .orderByDesc(DiProjectStageChange::getCreateTime)
                        .last("limit 1"));
                if (diProjectStageChange == null ||
                        Objects.equals(diProjectStageChange.getStage(), CommentStageEnum.plan.getCode())) {
                    projectStageChangeMapper.insert(DiProjectStageChange.builder()
                            .stage(CommentStageEnum.quote.getCode())
                            .refNo(niche.getNicheNo())
                            .createTime(new Date())
                            .build());
                }
            }
        }
    }

}

package com.dyd.di.materiel.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dyd.common.core.utils.DateUtils;
import com.dyd.di.materiel.domain.DiMateriel;
import com.dyd.di.materiel.domain.DiMaterielPrice;
import com.dyd.di.materiel.mapper.DiMaterielPriceMapper;
import com.dyd.di.materiel.service.IDiMaterielPriceService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 物料价格Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
@Service
public class DiMaterielPriceServiceImpl extends ServiceImpl<DiMaterielPriceMapper, DiMaterielPrice> implements IDiMaterielPriceService {
    @Autowired
    private DiMaterielPriceMapper diMaterielPriceMapper;

    /**
     * 查询物料价格
     *
     * @param id 物料价格主键
     * @return 物料价格
     */
    @Override
    public DiMaterielPrice selectDiMaterielPriceById(String id) {
        return diMaterielPriceMapper.selectDiMaterielPriceById(id);
    }

    /**
     * 查询物料价格列表
     *
     * @param diMaterielPrice 物料价格
     * @return 物料价格
     */
    @Override
    public List<DiMaterielPrice> selectDiMaterielPriceList(DiMaterielPrice diMaterielPrice) {
        return diMaterielPriceMapper.selectDiMaterielPriceList(diMaterielPrice);
    }

    /**
     * 新增物料价格
     *
     * @param diMaterielPrice 物料价格
     * @return 结果
     */
    @Override
    public int insertDiMaterielPrice(DiMaterielPrice diMaterielPrice) {
        diMaterielPrice.setCreateTime(DateUtils.getNowDate());
        return diMaterielPriceMapper.insertDiMaterielPrice(diMaterielPrice);
    }

    /**
     * 修改物料价格
     *
     * @param diMaterielPrice 物料价格
     * @return 结果
     */
    @Override
    public int updateDiMaterielPrice(DiMaterielPrice diMaterielPrice) {
        return diMaterielPriceMapper.updateById(diMaterielPrice);
//        diMaterielPrice.setUpdateTime(DateUtils.getNowDate());
//        return diMaterielPriceMapper.updateDiMaterielPrice(diMaterielPrice);
    }

    /**
     * 批量删除物料价格
     *
     * @param ids 需要删除的物料价格主键
     * @return 结果
     */
    @Override
    public int deleteDiMaterielPriceByIds(String[] ids) {
        return diMaterielPriceMapper.deleteDiMaterielPriceByIds(ids);
    }

    /**
     * 删除物料价格信息
     *
     * @param id 物料价格主键
     * @return 结果
     */
    @Override
    public int deleteDiMaterielPriceById(String id) {
        return diMaterielPriceMapper.deleteDiMaterielPriceById(id);
    }

    /**
     * 根据物料id查询物料价格
     *
     * @param materielIdList
     * @return
     */
    @Override
    public List<DiMaterielPrice> queryByMateriel(List<Long> materielIdList) {
        if (CollectionUtils.isEmpty(materielIdList)) {
            return new ArrayList<>();
        }
        return this.lambdaQuery().in(DiMaterielPrice::getMaterielId, materielIdList).list();
    }

    /**
     * 更新物料为采购物料,指导加工价为0
     *
     * @param leafIds
     */
    @Override
    public void updateAsPurchase(List<Long> leafIds) {

        diMaterielPriceMapper.update(new UpdateWrapper<DiMaterielPrice>().lambda()
                .in(DiMaterielPrice::getMaterielId, leafIds)
                .set(DiMaterielPrice::getGuideProductionCosts, 0));
    }

    /**
     * 更新物料为加工物料,指导加工价为0的设置为空
     *
     * @param midlist
     */
    @Override
    public void updateAsProcess(List<Long> midlist) {
        diMaterielPriceMapper.update(new UpdateWrapper<DiMaterielPrice>().lambda()
                .in(DiMaterielPrice::getMaterielId, midlist)
                .eq(DiMaterielPrice::getGuideProductionCosts, 0)
                .set(DiMaterielPrice::getGuideProductionCosts, null)
        );
    }
}

package com.dyd.di.pre.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dyd.di.pre.domain.request.ApprovalProcessRequest;
import com.dyd.di.pre.domain.request.ApprovalResultRequest;
import com.dyd.di.pre.entity.DiPreSaleQuote;

/**
 * 销售报价单审批Service接口
 */
public interface DiPreSaleQuoteApprovalService extends IService<DiPreSaleQuote> {

    /**
     * 启动审批流程
     *
     * @param request 流程参数
     */
    void startApprovalProcess(ApprovalProcessRequest request);

    /**
     * 校验当前登录者是否有审批权限
     *
     * @param request 流程参数
     * @return 结果-true：有审批权限；false：没有审批权限
     */
    Boolean checkApprovalBy(ApprovalProcessRequest request);

    /**
     * 报销单审批
     *
     * @param request 审批参数
     */
    void saleOfferApproval(ApprovalResultRequest request);

    /**
     * 修改报价单状态以及审批任务ID
     *
     * @param approvalStatus 状态：0.待审批,1.审批中,2.审批通过,3.审批驳回
     * @param taskId         camunda任务ID
     * @param preSaleQuoteId 销售报价单主键ID
     */
    void updateApprovalInfo(Integer approvalStatus, String taskId, Long preSaleQuoteId);

}

package com.dyd.di.contract.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 合同生成枚举
 */
@Getter
@AllArgsConstructor
public enum ContractGenerateEnum {

    CONTRACT_PROJECT_ANNEX_GENERATE("ContractProjectAnnexTemplate.ftl", "pdf", "{}.pdf", "/tmp/{}"),

    CONTRACT_TRADE_ANNEX_GENERATE("ContractTradeAnnexTemplate.ftl", "pdf", "{}.pdf", "/tmp/{}"),

    ;

    /**
     * 模板名称
     */
    private final String templateName;

    /**
     * 生成类型
     */
    private final String generateType;

    /**
     * 文件名
     */
    private final String fileName;

    /**
     * 生成文件路径
     */
    private final String generatePath;

}

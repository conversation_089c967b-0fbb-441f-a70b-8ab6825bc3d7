package com.dyd.di.order.service.impl;

import cn.hutool.core.lang.Tuple;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dyd.common.core.constant.DestinationConstants;
import com.dyd.common.core.domain.R;
import com.dyd.common.core.exception.ServiceException;
import com.dyd.common.core.utils.AfterCommitExecutor;
import com.dyd.common.security.utils.SecurityUtils;
import com.dyd.dbc.api.RemoteDbcService;
import com.dyd.dbc.api.model.DdUserDTO;
import com.dyd.dbc.api.model.QueryDdUserDTO;
import com.dyd.di.agency.constants.AgencyConstants;
import com.dyd.di.agency.domain.dto.AgencyTaskInfoDTO;
import com.dyd.di.agency.domain.vo.TaskArgs;
import com.dyd.di.agency.domain.vo.TaskChangeResult;
import com.dyd.di.agency.enums.AgencyTaskTypeEnum;
import com.dyd.di.agency.service.DiAgencyTaskService;
import com.dyd.di.material.service.CommonService;
import com.dyd.di.order.convert.OrderConvert;
import com.dyd.di.order.domain.DiOrder;
import com.dyd.di.order.domain.DiOrderMachineDesign;
import com.dyd.di.order.domain.DiOrderMaterielPlan;
import com.dyd.di.order.domain.DiOrderProduce;
import com.dyd.di.order.enums.OrderDingTalkEnum;
import com.dyd.di.order.enums.OrderScheduleStatusEnum;
import com.dyd.di.order.enums.OrderStageEnum;
import com.dyd.di.order.enums.OrderTaskScheduleStatusEnum;
import com.dyd.di.order.mapper.DiOrderMaterielPlanMapper;
import com.dyd.di.order.pojo.*;
import com.dyd.di.order.pojo.dto.*;
import com.dyd.di.order.service.*;
import com.dyd.di.pre.domain.response.PreSaleQuoteProductCostInfoVO;
import com.dyd.di.pre.entity.DiPreSale;
import com.dyd.di.pre.entity.DiPreSaleManifest;
import com.dyd.di.pre.service.IDiPreSaleManifestService;
import com.dyd.di.pre.service.IDiPreSaleService;
import com.dyd.di.pre.service.impl.PreSaleQuoteService;
import com.dyd.system.api.RemoteUserService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.dyd.di.order.service.impl.DiOrderServiceImpl.NOT_NEED_FEE;
import static com.dyd.di.order.service.impl.DiOrderServiceImpl.OPERATE_TYPE_START;
import static com.dyd.di.order.service.impl.DiOrderServiceImpl.OPERATE_TYPE_END;

/**
 * <AUTHOR>
 * @description 针对表【di_order_materiel_plan(订单物料计划表)】的数据库操作Service实现
 * @createDate 2024-09-20 09:36:48
 */
@Service
public class DiOrderMaterielPlanServiceImpl extends ServiceImpl<DiOrderMaterielPlanMapper, DiOrderMaterielPlan>
        implements DiOrderMaterielPlanService {
    @Autowired
    private IDiOrderService orderService;

    @Autowired
    private PreSaleQuoteService preSaleQuoteService;

    @Autowired
    private RemoteDbcService remoteDbcService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private DiOrderMaterielPlanMapper diOrderMaterielPlanMapper;

    @Autowired
    private IDiPreSaleManifestService diPreSaleManifestService;

    @Autowired
    private DiOrderProduceService diOrderProduceService;

    @Autowired
    private IDiPreSaleService diPreSaleService;

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    @Resource
    private OrderCommonService orderCommonService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private DiOrderStageService diOrderStageService;

    @Autowired
    private DiAgencyTaskService diAgencyService;

    @Override
    public OrderMaterielPlanDetailDTO detail(OrderInstallDetailVO orderInstallDetailVO) {
        DiOrder diOrder = orderService.getOne(Wrappers.lambdaQuery(DiOrder.class)
                .eq(Objects.nonNull(orderInstallDetailVO.getId()), DiOrder::getId, orderInstallDetailVO.getId())
                .eq(StringUtils.isNotBlank(orderInstallDetailVO.getOrderNo()), DiOrder::getOrderNo, orderInstallDetailVO.getOrderNo())
                .last("limit 1"));
        if (Objects.isNull(diOrder)) {
            throw new ServiceException("订单不存在");
        }
        Map<String, String> userNameMap = Maps.newHashMap();
        if (StringUtils.isNotBlank(diOrder.getPurchaseUserId())) {
            R<List<DdUserDTO>> userListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(Lists.newArrayList(diOrder.getPurchaseUserId())).build());
            if (userListResult.isSuccess()) {
                userNameMap = userListResult.getData().stream().collect(Collectors.toMap(DdUserDTO::getJobNumber, DdUserDTO::getName));
            }
        }
        OrderMaterielPlanDetailDTO.OrderMaterielPlanDetailDTOBuilder planDetailDTOBuilder = OrderMaterielPlanDetailDTO.builder().orderDeliveryDate(diOrder.getOrderDeliveryDate())
                .purchaseUserId(diOrder.getPurchaseUserId()).purchaseUserIdName(userNameMap.get(diOrder.getPurchaseUserId()))
                .orderStatus(diOrder.getOrderStatus());
        if (StringUtils.isBlank(diOrder.getPreSaleQuoteNo())) {
            return planDetailDTOBuilder.build();
        }
        //报价单里的方案
        PreSaleQuoteProductCostInfoVO productCostInfoVO = preSaleQuoteService.getQuoteProductCostInfoByNo(diOrder.getPreSaleQuoteNo(), NOT_NEED_FEE);
        List<OrderProductDetailDTO> orderProductDetailList = OrderConvert.INSTANCE.productInfoListToDetailList(productCostInfoVO.getDetailedList());
        List<Long> preSaleIdList = orderProductDetailList.stream().map(OrderProductDetailDTO::getPreSaleId).toList();
        if (CollectionUtils.isEmpty(preSaleIdList)) {
            return planDetailDTOBuilder.productDetailDTOList(orderProductDetailList).build();
        }

        Map<String, DiPreSale> diPreSaleMap = diPreSaleService.queryDiPreSaleByIds(preSaleIdList).stream().collect(Collectors.toMap(DiPreSale::getPreSaleCode, Function.identity()));
        List<DiOrderMaterielPlan> diOrderMaterielPlanList = diOrderMaterielPlanMapper.selectList(Wrappers.lambdaQuery(DiOrderMaterielPlan.class)
                .eq(DiOrderMaterielPlan::getOrderNo, diOrder.getOrderNo())
                .in(DiOrderMaterielPlan::getPreSaleId, preSaleIdList).eq(DiOrderMaterielPlan::getDelFlag, 0));
        List<String> purchaseUserIdList = diOrderMaterielPlanList.stream().map(DiOrderMaterielPlan::getPurchaseUserId).filter(StringUtils::isNotBlank).toList();
        Map<String, String> purchaseUserMap = Maps.newHashMap();
        R<List<DdUserDTO>> enforceUserListResult = remoteDbcService.queryDdUserList(QueryDdUserDTO.builder().jobNumberList(purchaseUserIdList).build());
        if (enforceUserListResult.isSuccess()) {
            purchaseUserMap = enforceUserListResult.getData().stream().collect(Collectors.toMap(DdUserDTO::getJobNumber, DdUserDTO::getName));
        }
        Map<String, List<DiOrderMaterielPlan>> materielPlanMap = diOrderMaterielPlanList.stream().collect(Collectors.groupingBy(DiOrderMaterielPlan::getPreSaleCode));
        //生产
        Map<String, List<DiOrderProduce>> produceMap = diOrderProduceService.list(Wrappers.lambdaQuery(DiOrderProduce.class)
                .eq(DiOrderProduce::getOrderNo, diOrder.getOrderNo())
                .in(DiOrderProduce::getPreSaleId, preSaleIdList).eq(DiOrderProduce::getDelFlag, 0)).stream().collect(Collectors.groupingBy(DiOrderProduce::getPreSaleCode));


        for (OrderProductDetailDTO orderProductDetailDTO : orderProductDetailList) {

            DiPreSale diPreSale = diPreSaleMap.get(orderProductDetailDTO.getPreSaleCode());
            orderProductDetailDTO.setOrderPreSaleStatus(Objects.nonNull(diPreSale.getOrderPreSaleStatus()) ? String.valueOf(diPreSale.getOrderPreSaleStatus()) : "");
            Map<String, String> finalElectricUserMap = purchaseUserMap;
            Optional<DiOrderMaterielPlan> materielPlanOptional = Optional.ofNullable(materielPlanMap.get(orderProductDetailDTO.getPreSaleCode())).flatMap(diOrderMaterielPlans
                    -> diOrderMaterielPlans.stream().findFirst());
            if (materielPlanOptional.isPresent()) {
                DiOrderMaterielPlan diOrderMaterielPlan = materielPlanOptional.get();
                OrderMaterielPlanDetailInfoDTO orderMaterielPlanDetailInfoDTO = OrderConvert.INSTANCE.materielPlanEntityToDTO(diOrderMaterielPlan);
                if (Objects.nonNull(orderMaterielPlanDetailInfoDTO)) {
                    orderMaterielPlanDetailInfoDTO.setPurchaseUserIdName(finalElectricUserMap.get(diOrderMaterielPlan.getPurchaseUserId()));
                    Optional.ofNullable(produceMap.get(orderProductDetailDTO.getPreSaleCode())).ifPresent(diOrderProduces -> {
                        if (CollectionUtils.isNotEmpty(diOrderProduces)) {
                            orderMaterielPlanDetailInfoDTO.setIsProduceSchedule(1);

                        }
                    });
                    orderProductDetailDTO.setMaterielPlanDetailInfoDTO(orderMaterielPlanDetailInfoDTO);
                }
            } else {
                OrderMaterielPlanDetailInfoDTO orderMaterielPlanDetailInfoDTO = new OrderMaterielPlanDetailInfoDTO();
                Optional.ofNullable(produceMap.get(orderProductDetailDTO.getPreSaleCode())).ifPresent(diOrderProduces -> {
                    if (CollectionUtils.isNotEmpty(diOrderProduces)) {
                        orderMaterielPlanDetailInfoDTO.setIsProduceSchedule(1);
                        orderMaterielPlanDetailInfoDTO.setLatestCompleteTime(diOrderProduces.get(0).getLatestCompleteTime());
                    }
                });
                orderProductDetailDTO.setMaterielPlanDetailInfoDTO(orderMaterielPlanDetailInfoDTO);
            }
        }
        return planDetailDTOBuilder.productDetailDTOList(orderProductDetailList).build();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long add(OrderMaterielAddVO orderMaterielAddVO) {
        DiOrder diOrder = orderService.getOne(Wrappers.lambdaQuery(DiOrder.class)
                .eq(DiOrder::getOrderNo, orderMaterielAddVO.getOrderNo())
                .last("limit 1"));
        if (Objects.isNull(diOrder)) {
            throw new ServiceException("订单不存在");
        }
        DiOrderMaterielPlan diOrderMaterielPlan = OrderConvert.INSTANCE.materielPlanAddVOTOEntity(orderMaterielAddVO);
        diOrderMaterielPlan.setOrderNo(diOrder.getOrderNo());
        if (StringUtils.isBlank(orderMaterielAddVO.getPurchaseUserId()) && StringUtils.isNotBlank(diOrder.getPurchaseUserId())) {
            diOrderMaterielPlan.setPurchaseUserId(diOrder.getPurchaseUserId());
        }
        diOrderMaterielPlanMapper.insert(diOrderMaterielPlan);
        //履约中
        rocketMQTemplate.syncSend(DestinationConstants.ORDER_TOPIC_TAG, diOrder);

        if (!orderMaterielAddVO.isIgnoreDingDing()) {
            //生成代办
            saveMaterielPlanAgencyTask(diOrderMaterielPlan, diOrderMaterielPlan.getPurchaseUserId(), null != diOrder.getOrderDeliveryDate() ? diOrder.getOrderDeliveryDate() : null, "未开始", diOrder.getProjectNo());
            //if (!diOrder.getPurchaseUserId().equals(diOrderMaterielPlan.getPurchaseUserId())) {
            //发送钉钉
            sendMaterielPlanDingTalk(diOrder.getNicheNo(), diOrder.getProjectManagerUserId(), diOrderMaterielPlan.getPreSaleCode(),
                    orderMaterielAddVO.getPurchaseUserId(), orderMaterielAddVO.getPurchaseUserId());
            //}
        }

        //将人员加入项目用户关联表中(20250305-业务部门提出设置负责人对应负责人订单列表看不到订单数据)
        commonService.personnelSaveProject(diOrder.getProjectNo(), Lists.newArrayList(diOrderMaterielPlan.getPurchaseUserId()), "0");
        return diOrderMaterielPlan.getId();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long updateMateriel(OrderMaterielUpdateVO orderMaterielUpdateVO) {
        DiOrderMaterielPlan diOrderMaterielPlan = diOrderMaterielPlanMapper.selectById(orderMaterielUpdateVO.getId());
        if (Objects.isNull(diOrderMaterielPlan)) {
            throw new ServiceException("物料计划单不存在");
        }
        DiOrder diOrder = orderService.getOne(Wrappers.lambdaQuery(DiOrder.class)
                .eq(DiOrder::getOrderNo, diOrderMaterielPlan.getOrderNo()).last("limit 1"));
        if (Objects.isNull(diOrder)) {
            throw new ServiceException("订单不存在");
        }
        DiOrderMaterielPlan orderMaterielPlan = OrderConvert.INSTANCE.materielPlanUpdateVOTOEntity(orderMaterielUpdateVO);
        diOrderMaterielPlanMapper.updateById(orderMaterielPlan);
        if (orderMaterielUpdateVO.isIgnoreDingDing()) {
            return diOrderMaterielPlan.getId();
        }
        boolean isUpdatePurchaseUser = false;
        boolean isSavePurchaseUser = false;
        if (StringUtils.isNotBlank(diOrderMaterielPlan.getPurchaseUserId())) {
            if (StringUtils.isNotBlank(orderMaterielUpdateVO.getPurchaseUserId()) && !diOrderMaterielPlan.getPurchaseUserId().equals(orderMaterielUpdateVO.getPurchaseUserId())) {
                isUpdatePurchaseUser = true;
                //将原本的关联人员删除(20250305-业务部门提出设置负责人对应负责人订单列表看不到订单数据)
                commonService.personnelSaveProject(diOrder.getProjectNo(), Lists.newArrayList(diOrderMaterielPlan.getPurchaseUserId()), "2");
                //将新的人员加入项目用户关联表中(20250305-业务部门提出设置负责人对应负责人订单列表看不到订单数据)
                commonService.personnelSaveProject(diOrder.getProjectNo(), Lists.newArrayList(orderMaterielUpdateVO.getPurchaseUserId()), "0");
            }
        } else {
            if (StringUtils.isNotBlank(orderMaterielUpdateVO.getPurchaseUserId())) {
                isSavePurchaseUser = true;
                //将人员加入项目用户关联表中(20250305-业务部门提出设置负责人对应负责人订单列表看不到订单数据)
                commonService.personnelSaveProject(diOrder.getProjectNo(), Lists.newArrayList(orderMaterielUpdateVO.getPurchaseUserId()), "0");
            }
        }
        if (isSavePurchaseUser) {
            saveMaterielPlanAgencyTask(diOrderMaterielPlan, orderMaterielUpdateVO.getPurchaseUserId(), null != diOrder.getOrderDeliveryDate() ? diOrder.getOrderDeliveryDate() : null, getTaskStateDesc(diOrderMaterielPlan.getScheduleStatus()), diOrder.getProjectNo());
        }
        //if (isUpdatePurchaseUser) {
        //获取方案名称
        DiPreSale preSale = diPreSaleService.getById(diOrderMaterielPlan.getPreSaleId());
        String preSaleName = null != preSale && StringUtils.isNotBlank(preSale.getPreSaleName()) ? preSale.getPreSaleName() : diOrderMaterielPlan.getPreSaleCode();
        List<String> saveUserList = new ArrayList<>(Collections.singletonList(orderMaterielUpdateVO.getPurchaseUserId()));
//            if (!diOrderMaterielPlan.getPurchaseUserId().equals(diOrder.getPurchaseUserId())) {
//                //删除待办任务
//                AgencyTaskInfoDTO agencyTaskInfoDto = new AgencyTaskInfoDTO();
//                agencyTaskInfoDto.setTaskTypeEnum(AgencyTaskTypeEnum.ORDER_PURCHASE);
//                agencyTaskInfoDto.setBusinessKey(diOrderMaterielPlan.getPreSaleCode());
//                agencyTaskInfoDto.setJumpKey(diOrderMaterielPlan.getOrderNo());
//                agencyTaskInfoDto.setType("2");
//                agencyTaskInfoDto.setLiabilityByList(Collections.singletonList(diOrderMaterielPlan.getPurchaseUserId()));
//                String topic = AgencyConstants.AGENCY_TASK_ORDERLY_TOPIC + AgencyConstants.DELIMITER_COLON + AgencyConstants.AGENCY_TASK_ORDERLY_TAG;
//                rocketMQTemplate.syncSend(topic, agencyTaskInfoDto);
//            } else {
//                saveUserList.add(diOrder.getPurchaseUserId());
//            }
        TaskChangeResult taskResult = diAgencyService.setTask(diOrderMaterielPlan.getPreSaleCode(), AgencyTaskTypeEnum.ORDER_PURCHASE, saveUserList,
                TaskArgs.builder()
                        .projectNo(diOrder.getProjectNo())
                        .jumpKey(diOrderMaterielPlan.getOrderNo())
                        .taskName(preSaleName)
                        .taskStateDesc(getTaskStateDesc(diOrderMaterielPlan.getScheduleStatus()))
                        .expectDeliverDate(null != diOrder.getOrderDeliveryDate() ? diOrder.getOrderDeliveryDate() : null)
                        .build()
        );
//            orderCommonService.saveAgencyTask(diOrderMaterielPlan.getPreSaleCode(), diOrderMaterielPlan.getOrderNo(), null, diOrder.getProjectNo(), AgencyTaskTypeEnum.ORDER_PURCHASE,
//                    preSaleName, getTaskStateDesc(diOrderMaterielPlan.getScheduleStatus()), saveUserList,
//                    null != diOrder.getOrderDeliveryDate() ? diOrder.getOrderDeliveryDate() : null);
        //}
        if (taskResult.hasAdd()) {
            sendMaterielPlanDingTalk(diOrder.getNicheNo(), diOrder.getProjectManagerUserId(), diOrderMaterielPlan.getPreSaleCode(),
                    orderMaterielUpdateVO.getPurchaseUserId(),
                    orderMaterielUpdateVO.getPurchaseUserId());
        }
        return orderMaterielPlan.getId();
    }

    private void saveMaterielPlanAgencyTask(DiOrderMaterielPlan diOrderMaterielPlan, String purchaseUserId, Date orderDeliveryDate, String stateDesc, String projectNo) {
        //获取方案名称
        DiPreSale preSale = diPreSaleService.getById(diOrderMaterielPlan.getPreSaleId());
        String preSaleName = null != preSale && StringUtils.isNotBlank(preSale.getPreSaleName()) ? preSale.getPreSaleName() : diOrderMaterielPlan.getPreSaleCode();
        orderCommonService.saveAgencyTask(diOrderMaterielPlan.getPreSaleCode(), diOrderMaterielPlan.getOrderNo(), null, projectNo, AgencyTaskTypeEnum.ORDER_PURCHASE,
                preSaleName, stateDesc, Collections.singletonList(purchaseUserId), orderDeliveryDate);
    }

    private void sendMaterielPlanDingTalk(String nicheNo, String projectManagerUserId, String preSaleCode, String owner, String purchaseUserId) {
        //查询订单可见范围的人
        List<String> visibleUserList = orderCommonService.getLiabilityUserList(nicheNo);
        if (StringUtils.isNotBlank(projectManagerUserId)) {
            visibleUserList.add(projectManagerUserId);
        }
        String userName = remoteUserService.queryUserNickName(owner);
        //发送钉钉消息
        String title = String.format(OrderDingTalkEnum.ORDER_PRODUCT_PURCHASE_USER_UPDATE.getTitle(), preSaleCode);
        String content = String.format(OrderDingTalkEnum.ORDER_PRODUCT_PURCHASE_USER_UPDATE.getMessage(),
                userName, SecurityUtils.getLoginUser().getSysUser().getNickName());
        orderCommonService.sendDingTalkMessage("产品方案", title, content, visibleUserList, purchaseUserId);
    }

    /**
     * 获取状态中文描述
     *
     * @param scheduleStatus 状态
     * @return 描述
     */
    private String getTaskStateDesc(Integer scheduleStatus) {
        return switch (scheduleStatus) {
            case 0 -> "未开始";
            case 1 -> "进行中";
            case 2 -> "已完成";
            default -> "未知状态";
        };
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> operate(OrderMaterielOperateVO orderMaterielOperateVO) {
        DiOrderMaterielPlan diOrderMaterielPlan = diOrderMaterielPlanMapper.selectById(orderMaterielOperateVO.getId());
        if (Objects.isNull(diOrderMaterielPlan)) {
            return R.fail("物料计划不存在");
        }
        DiOrder diOrder = orderService.getOne(Wrappers.lambdaQuery(DiOrder.class)
                .eq(DiOrder::getOrderNo, diOrderMaterielPlan.getOrderNo()).last("limit 1"));
        if (Objects.isNull(diOrder)) {
            return R.fail("订单不存在");
        }
        //确认开始
        if (OPERATE_TYPE_START == orderMaterielOperateVO.getOperateType()) {
            if (!diPreSaleService.checkOrderSupportComplated(diOrderMaterielPlan.getPreSaleId())) {
                return R.fail("该方案技术支持复核尚未完成，请完成后再试");
            }
            String returnInfo = diOrderStageService.updateOrderStageCheck(diOrder, OrderStageEnum.FOUR);
            if (StringUtils.isNotBlank(returnInfo)) {
                return R.fail(returnInfo);
            }
            //更新开始时间
            DiOrderMaterielPlan orderMaterielPlan = new DiOrderMaterielPlan();
            orderMaterielPlan.setId(diOrderMaterielPlan.getId());
            orderMaterielPlan.setScheduleStatus(1);
            orderMaterielPlan.setRealPlanStartTime(new Date());
            diOrderMaterielPlanMapper.updateById(orderMaterielPlan);
        } else {

            DiOrderMaterielPlan orderMaterielPlan = new DiOrderMaterielPlan();
            orderMaterielPlan.setId(diOrderMaterielPlan.getId());
            orderMaterielPlan.setScheduleStatus(2);
            orderMaterielPlan.setRealPlanEndTime(new Date());
            diOrderMaterielPlanMapper.updateById(orderMaterielPlan);
            //删除待办任务
            AgencyTaskInfoDTO agencyTaskInfoDto = new AgencyTaskInfoDTO();
            agencyTaskInfoDto.setBusinessKey(diOrderMaterielPlan.getPreSaleCode());
            agencyTaskInfoDto.setJumpKey(diOrderMaterielPlan.getOrderNo());
            agencyTaskInfoDto.setTaskTypeEnum(AgencyTaskTypeEnum.ORDER_PURCHASE);
            agencyTaskInfoDto.setType("2");
            String topic = AgencyConstants.AGENCY_TASK_ORDERLY_TOPIC + AgencyConstants.DELIMITER_COLON + AgencyConstants.AGENCY_TASK_ORDERLY_TAG;
            rocketMQTemplate.syncSend(topic, agencyTaskInfoDto);
        }
        diPreSaleService.updatePreSaleStatusByOrderId(diOrderMaterielPlan.getPreSaleId(), diOrder.getId());
//        if (OPERATE_TYPE_END == orderMaterielOperateVO.getOperateType()) {
//            //事务提交后执行
//            AfterCommitExecutor.submit(() -> {
//                //获取下一阶段枚举
//                OrderStageEnum stageEnum = diOrderStageService.getOrderStageEnum(diOrderMaterielPlan.getPreSaleId(), OrderStageEnum.FOUR.getCode());
//                //修改阶段
//                diOrderStageService.updateOrderStage(diOrder, stageEnum);
//            }, e -> log.error("DiOrderMaterielPlanServiceImpl---operate()---更新订单阶段失败,订单号：" + diOrderMaterielPlan.getOrderNo(), e));
//        }
        return R.ok(String.valueOf(diOrderMaterielPlan.getId()));
    }


    @Override
    public MaterielPlanListDTO checkList(OrderProduceBomVO orderProduceBomVO) {
        DiPreSale diPreSale = diPreSaleService.getById(orderProduceBomVO.getPreSaleId());
        if (Objects.isNull(diPreSale)) {
            throw new ServiceException("售前方案不存在");
        }
        List<DiPreSaleManifest> manifests = diPreSaleManifestService.list(Wrappers.lambdaQuery(DiPreSaleManifest.class).eq(DiPreSaleManifest::getDiPreSaleId, diPreSale.getId()));
        if (CollectionUtils.isEmpty(manifests)) {
            throw new ServiceException("售前方案清单不存在");
        }
        List<ProduceCheckListDTO> resultList = Lists.newArrayList();
        List<EntrustProduceCheckListDTO> entrustCheckList = Lists.newArrayList();
        for (DiPreSaleManifest manifest : manifests) {
            Tuple tuple = diOrderProduceService.queryMaterielPlanCheckListByVersion(manifest.getMaterialVersion(), orderProduceBomVO.getPreSaleId());
            resultList.addAll(tuple.get(0));
            entrustCheckList.addAll(tuple.get(1));
        }
        MaterielPlanListDTO materielPlanListDTO = new MaterielPlanListDTO();
        materielPlanListDTO.setEntrustCheckList(entrustCheckList);
        materielPlanListDTO.setProduceCheckList(resultList);
        return materielPlanListDTO;
    }

    @Override
    public List<String> giveUp(String orderNo) {
        List<String> userList = new ArrayList<>();

        List<DiOrderMaterielPlan> list = this.lambdaQuery()
                .eq(DiOrderMaterielPlan::getOrderNo, orderNo)
                .ne(DiOrderMaterielPlan::getScheduleStatus, OrderTaskScheduleStatusEnum.COMPLETED.getCode())
                .list();

        if (CollectionUtils.isNotEmpty(list)) {
            userList = list.stream().map(DiOrderMaterielPlan::getPurchaseUserId).filter(StringUtils::isNotBlank).toList();

            this.lambdaUpdate()
                    .set(DiOrderMaterielPlan::getScheduleStatus, OrderTaskScheduleStatusEnum.GIVE_UP.getCode())
                    .eq(DiOrderMaterielPlan::getOrderNo, orderNo)
                    .ne(DiOrderMaterielPlan::getScheduleStatus, OrderTaskScheduleStatusEnum.COMPLETED.getCode())
                    .update();
        }
        return userList;
    }
}





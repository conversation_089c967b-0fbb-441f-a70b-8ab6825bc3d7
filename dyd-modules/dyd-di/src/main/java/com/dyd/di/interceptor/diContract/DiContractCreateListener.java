package com.dyd.di.interceptor.diContract;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dyd.common.core.utils.StringUtils;
import com.dyd.di.comment.enums.CommentStageEnum;
import com.dyd.di.contract.entity.DiContract;
import com.dyd.di.contract.mapper.DiContractMapper;
import com.dyd.di.interceptor.entity.DiProjectStageChange;
import com.dyd.di.interceptor.mapper.DiProjectStageChangeMapper;
import com.dyd.di.marketing.domain.DiMarketingNiche;
import com.dyd.di.marketing.mapper.DiMarketingNicheMapper;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

@Component
public class DiContractCreateListener {
    @Autowired
    private DiContractMapper contractMapper;
    @Autowired
    private DiMarketingNicheMapper nicheMapper;
    @Autowired
    private DiProjectStageChangeMapper projectStageChangeMapper;

    @Async
    @EventListener
    public void handleDataChange(DiContractCreateEvent event) {
        handleDataChange0(event);
    }

    public void handleDataChange0(DiContractCreateEvent event) {
        String contractNo = event.getContractNo();
        DiContract contract =
                contractMapper.selectOne(Wrappers.<DiContract>lambdaQuery().eq(DiContract::getContractNo, contractNo));
        if (contract != null) {
            DiMarketingNiche niche =
                    nicheMapper.selectOne(Wrappers.<DiMarketingNiche>lambdaQuery().eq(DiMarketingNiche::getNicheNo, contract.getNicheNo()));
            if (niche != null) {
                DiProjectStageChange diProjectStageChange = projectStageChangeMapper.selectOne(Wrappers.<DiProjectStageChange>lambdaQuery()
                        .in(DiProjectStageChange::getRefNo,
                                Lists.newArrayList(contract.getProjectNo(), niche.getNicheNo(), niche.getClueNo())
                                        .stream()
                                        .filter(StringUtils::hasText).toList())
                        .orderByDesc(DiProjectStageChange::getCreateTime)
                        .last("limit 1"));
                if (diProjectStageChange == null ||
                        Objects.equals(diProjectStageChange.getStage(), CommentStageEnum.quote.getCode())) {
                    projectStageChangeMapper.insert(DiProjectStageChange.builder()
                            .stage(CommentStageEnum.contract.getCode())
                            .refNo(niche.getNicheNo())
                            .createTime(new Date())
                            .build());
                }
            }
        }
    }

}

package com.dyd.di.materiel.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.dyd.common.core.annotation.Excel;
import com.dyd.common.security.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 物料bom对象 di_materiel_bom
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MaterielBomInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 父级id
     */
    private Long parentId;
    /**
     * 父级物料id
     */
    private Long parentMaterielId;

    /**
     * 物料ID
     */
    @Excel(name = "物料ID")
    private Long materielId;

    /**
     * 物料号
     */
    @Excel(name = "物料号")
    private String materielNo;

    /**
     * 是否替代料 1主料 2替代料  字典pre_sale_support_materiel_type
     */
    @Excel(name = "是否替代料 1主料 2替代料")
    private Long isSubstitute;

    /**
     * 用量
     */
    @Excel(name = "用量")
    private BigDecimal quantity;

    /**
     * 2删除 0 正常
     */
    private Long delFlag;

    /**
     * 链路id
     */
    @Excel(name = "链路id")
    private String traceId;


    /**
     * 路径
     */
    private String routePath;
}

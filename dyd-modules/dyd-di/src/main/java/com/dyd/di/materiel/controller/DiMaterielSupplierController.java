package com.dyd.di.materiel.controller;

import com.dyd.common.core.domain.R;
import com.dyd.common.core.utils.PageWrapper;
import com.dyd.common.core.web.controller.BaseController;
import com.dyd.di.materiel.convert.MaterielConvert;
import com.dyd.di.materiel.pojo.MaterielSupplierAddVo;
import com.dyd.di.materiel.pojo.MaterielSupplierListVo;
import com.dyd.di.materiel.pojo.MaterielSupplierUpdateVo;
import com.dyd.di.materiel.pojo.dto.MaterielSupplierDTO;
import com.dyd.di.materiel.service.IDiMaterielSupplierService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 供应商
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
@RestController
@RequestMapping("/materiel/supplier")
public class DiMaterielSupplierController extends BaseController {
    @Autowired
    private IDiMaterielSupplierService diSupplierService;

    /**
     * 查询供应商列表
     */
    // @RequiresPermissions("supplier:supplier:list")
    @GetMapping("/list")
    public R<PageWrapper<MaterielSupplierDTO>> list(MaterielSupplierListVo materielSupplierListVo) {
        return R.ok(diSupplierService.selectDiSupplierList(materielSupplierListVo));
    }

    /**
     * 添加物料供应商
     *
     * @param supplierAddVo
     * @return
     */

    @PostMapping("/addMaterielSupplier")
    public R<MaterielSupplierDTO> add(@RequestBody MaterielSupplierAddVo supplierAddVo) {
        return R.ok(diSupplierService.addMaterielSupplier(supplierAddVo));
    }

    /**
     * 修改物料供应商
     *
     * @param supplierUpdateVo
     * @return
     */

    @PostMapping("/updateMaterielSupplier")
    public R<MaterielSupplierDTO> updateMaterielSupplier(@RequestBody MaterielSupplierUpdateVo supplierUpdateVo) {
        return R.ok(diSupplierService.updateMaterielSupplier(supplierUpdateVo));
    }

}

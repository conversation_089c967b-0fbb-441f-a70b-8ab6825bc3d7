package com.dyd.di.grid.query;

import cn.hutool.core.collection.CollectionUtil;
import com.dyd.common.core.aspect.IDictProvider;
import com.dyd.common.core.aspect.ITreeItem;
import com.dyd.common.core.domain.R;
import com.dyd.common.core.utils.DateUtils;
import com.dyd.common.core.utils.PageHelp;
import com.dyd.common.core.utils.PageWrapper;
import com.dyd.common.security.utils.SecurityUtils;
import com.dyd.di.common.DbArgsHelper;
import com.dyd.di.consts.MatchTypeEnum;
import com.dyd.di.grid.constant.FieldTypeEnum;
import com.dyd.di.grid.domain.GridDictRow;
import com.dyd.di.grid.domain.GridRow;
import com.dyd.di.grid.dto.request.QueryGridDataRequest;
import com.dyd.di.grid.entity.DiGridDefine;
import com.dyd.di.grid.entity.DiGridField;
import com.dyd.di.grid.mapper.DiGridMapper;
import com.dyd.di.grid.service.IDiGridDefineService;
import com.dyd.di.material.service.CommonService;
import com.dyd.exchange.RemoteExchangeService;
import com.dyd.exchange.model.ErpItemStoreResponse;
import com.dyd.system.api.RemoteUserService;
import com.dyd.system.api.domain.SysUserRoleData;
import com.dydtec.infras.core.base.exception.BizValidException;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.groovy.jsr223.GroovyScriptEngineFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.script.ScriptEngine;
import javax.script.SimpleBindings;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

@Slf4j
@Service
public class QueryGridDataExecutor {

    @Autowired
    private DiGridMapper gridMapper;
    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private IDiGridDefineService gridDefineService;

    @Autowired
    private IDictProvider dictProvider;

    @Resource
    private CommonService commonService;

    @Autowired
    private RemoteExchangeService remoteExchangeService;

    private ScriptEngine engine;


    @PostConstruct
    public void init() {
        GroovyScriptEngineFactory factory = new GroovyScriptEngineFactory();
        engine = factory.getScriptEngine();
    }


    /**
     * 查询数据
     *
     * @param req
     * @param gridCode
     * @return
     */
    public PageWrapper<List<Map<String, Object>>> queryData(QueryGridDataRequest req, String gridCode, Long userId) {
        int pageNum = req.getPageNum() == null ? 1 : req.getPageNum(); // 假设我们想要查询第二页的数据
        int pageSize = req.getPageSize() == null ? 6 : req.getPageSize(); // 每页显示10条记录

        DiGridDefine grid = gridDefineService.getByCode(gridCode);

        Map<String, Object> args = new HashMap<>();
        args.put("curEmployee", SecurityUtils.getUsername());
        if (SecurityUtils.getLoginUser() != null) {
            args.put("curDept", SecurityUtils.getLoginUser().getSysUser().getDeptId());
        }
        if (StringUtils.isNotBlank(grid.getDataScopeCode())) {
            SysUserRoleData scope = commonService.findLoginJobNumberList(grid.getDataScopeCode(), userId);
            if (scope.empty()) {
                return PageHelp.render(pageNum, pageSize, 0, Collections.emptyList());
            }
            args.put("dataEmployeeList", scope.employeeList());
        } else {
            args.put("dataEmployeeList", commonService.findLoginJobNumberList());
        }
        Map<String, DiGridField> fieldMap = this.gridDefineService.queryFields(gridCode);
        //checkFieldParam(req.getOptionList(), fieldMap);
        QueryResult data = this.query(gridCode, req, args, fieldMap);
        appendData(data.getData(), fieldMap);

        //库存特殊处理
        if (Objects.equals(gridCode, "preSaleList") && CollectionUtils.isNotEmpty(data.getData())) {
            for (Map<String, Object> row : data.getData()) {
                if (row.containsKey("materielNo") && row.containsKey("stockNum")) {
                    R<List<ErpItemStoreResponse>> storeQty = remoteExchangeService.selectItemStoreQty(String.valueOf(row.containsKey("materielNo")));
                    if (storeQty.isSuccess() && CollectionUtil.isNotEmpty(storeQty.getData())) {
                        row.put("stockNum", (Objects.nonNull(storeQty.getData().get(0).getStoreQty()) ? storeQty.getData().get(0).getStoreQty().intValue() : 0));
                    }
                }
            }
        }
        return PageHelp.render(pageNum, pageSize, data.getTotal(), data.getData());

    }


    @SneakyThrows
    private QueryResult query(String grid, QueryGridDataRequest request, Map<String, Object> args, Map<String, DiGridField> fieldMap) {


        DiGridDefine gridDefine = gridDefineService.getByCode(grid);
        if (gridDefine == null) {
            throw new BizValidException("表格配置不存在");
        }
        QueryResult result = new QueryResult();
        SimpleBindings binding = new SimpleBindings();
        binding.put("args", args);
        String baseSql = (String) engine.eval(gridDefine.getQueryScript(), binding);
        if (StringUtils.isBlank(baseSql)) {
            throw new BizValidException("参数错误:" + grid + "不存在");
        }
        DbArgsHelper params = new DbArgsHelper(args);
        String where = this.createWhere(request.getOptionList(), "and", params, fieldMap);
        String orderBy = this.createOrderBy(request.getOrderList());
        log.info("{} dynamicSql,baseSql:{},args:{}", grid, baseSql, request);
        Page page = PageHelper.startPage(request.getPageNum(), request.getPageSize());
        List<Map<String, Object>> data = gridMapper.dynamicSqlV2(baseSql, where, orderBy, params.getArgs());
        if (CollectionUtils.isNotEmpty(data)) {
            result.setData(data.stream().filter(Objects::nonNull).collect(Collectors.toList()));
        }
        result.setTotal(page.getTotal());
        return result;
    }

    private String createOrderBy(List<QueryGridDataRequest.Order> orderList) {

        if (CollectionUtils.isEmpty(orderList)) {
            return "";
        } else {
            return " order by " + orderList.stream().map(order -> {
                return order.getFieldCode() + " " + ((order.getAscending() != null && order.getAscending()) ? "asc" : "desc");
            }).collect(Collectors.joining(","));
        }
    }

    private String createWhere(List<QueryGridDataRequest.Option> opList, String concat, DbArgsHelper args, Map<String, DiGridField> fieldMap) {

        if (CollectionUtils.isEmpty(opList)) {
            return null;
        }
        if (StringUtils.isBlank(concat)) {
            concat = "and";
        }
        List<String> subWhere = new ArrayList<>();
        for (QueryGridDataRequest.Option option : opList) {
            if (!fieldMap.containsKey(option.getFieldCode())) {
                continue;
            }
            if ("group".equalsIgnoreCase(option.getType())) {
                subWhere.add(createWhere(option.getSubOptions(), option.getConcat(), args, fieldMap));
            } else {
                if (StringUtils.isBlank(option.getFieldCode())) {
                    continue;
                }
                if (StringUtils.isBlank(option.getMatchType())) {
                    option.setMatchType(fieldMap.get(option.getFieldCode()).getMatchType());
                }
                if (StringUtils.isBlank(option.getMatchType())) {
                    log.info("未配置匹配方式,字段:" + option.getFieldCode());
                    continue;
                }
                subWhere.add(createWhereField(option, option.getConcat(), args, fieldMap.get(option.getFieldCode())));
            }
        }
        return subWhere.stream().filter(StringUtils::isNotBlank).collect(Collectors.joining(" " + concat + " "));
    }

    private String createWhereField(QueryGridDataRequest.Option option, String concat, DbArgsHelper args, DiGridField field) {
        String matchTypeTxt = option.getMatchType().split(",")[0];
        MatchTypeEnum matchType = MatchTypeEnum.of(matchTypeTxt, MatchTypeEnum.eq);
        if (StringUtils.isBlank(option.getFieldCode())) {
            return null;
        }
        String condition = null;
        switch (matchType) {
            case match:
                throw new IllegalArgumentException("暂不支持匹配的方式");

            case eq:
            case ge:
            case gt:
            case le:
            case lt:
            case notEq: {
                if (option.getArgs() != null && !option.getArgs().isEmpty() && option.getArgs().get(0) != null) {
                    Object arg = option.getArgs().get(0);
                    String argName = args.getArgName(option.getFieldCode(), arg);
                    condition = String.format("%s %s %s", option.getFieldCode(), matchType.getComparison(), argName);
                }
                break;
            }
            case like:
                if (option.getArgs() != null && !option.getArgs().isEmpty() && option.getArgs().get(0) != null) {
                    Object arg = "%" + option.getArgs().get(0).toString() + "%";
                    String argName = args.getArgName(option.getFieldCode(), arg);
                    condition = String.format("%s %s %s", option.getFieldCode(), matchType.getComparison(), argName);
                }
                break;
            case leftLike:
                if (option.getArgs() != null && !option.getArgs().isEmpty() && option.getArgs().get(0) != null) {
                    Object arg = "" + option.getArgs().get(0).toString() + "%";
                    String argName = args.getArgName(option.getFieldCode(), arg);
                    condition = String.format("%s %s %s", option.getFieldCode(), matchType.getComparison(), argName);
                }
                break;
            case rightLike:
                if (option.getArgs() != null && !option.getArgs().isEmpty() && option.getArgs().get(0) != null) {
                    Object arg = "%" + option.getArgs().get(0).toString() + "";
                    String argName = args.getArgName(option.getFieldCode(), arg);
                    condition = String.format("%s %s %s", option.getFieldCode(), matchType.getComparison(), argName);
                }
                break;
            case in:
            case notIn:
                if (option.getArgs() != null && !option.getArgs().isEmpty()) {
                    List<String> argNameList = new ArrayList<>();
                    for (int i = 0; i < option.getArgs().size(); i++) {
                        Object arg = "" + option.getArgs().get(i).toString() + "";
                        argNameList.add(args.getArgName(option.getFieldCode(), arg));
                    }
                    condition = String.format("%s %s (%s)", option.getFieldCode(), matchType.getComparison(), String.join(",", argNameList));
                }
                break;
            case range:
                List<String> part = new ArrayList<>();
                if (option.getArgs() != null && !option.getArgs().isEmpty() && option.getArgs().get(0) != null) {
                    Object arg = option.getArgs().get(0).toString();
                    String argName = args.getArgName(option.getFieldCode(), arg);
                    part.add(String.format("%s >=%s", option.getFieldCode(), argName));
                }
                if (option.getArgs() != null && option.getArgs().size() > 1 && option.getArgs().get(1) != null) {
                    if (FieldTypeEnum.Date.getCode().equals(field.getFiledType()) || FieldTypeEnum.DateTime.getCode().equals(field.getFiledType())) {
                        Object arg = this.addOneDay(option.getArgs().get(1).toString());
                        String argName = args.getArgName(option.getFieldCode(), arg);
                        part.add(String.format("%s < %s", option.getFieldCode(), argName));

                    } else {
                        Object arg = option.getArgs().get(1).toString();
                        String argName = args.getArgName(option.getFieldCode(), arg);

                        part.add(String.format("%s <= %s", option.getFieldCode(), argName));
                    }
                }
                if (CollectionUtils.isNotEmpty(part)) {
                    condition = "(" + String.join(" and ", part) + ")";
                }
                break;
            case isNull:
                condition = String.format("%s is null", option.getFieldCode());
                break;
            case notNull:
                condition = String.format("%s is not null", option.getFieldCode());
                break;
            case contains:
                if (option.getArgs() != null && !option.getArgs().isEmpty() && option.getArgs().get(0) != null) {
                    Object arg = option.getArgs().get(0).toString();
                    String argName = args.getArgName(option.getFieldCode(), arg);
                    condition = String.format("find_in_set( %s, %s)>0", argName, option.getFieldCode());
                }
            default:
                break;
        }
        return condition;

    }

    private String addOneDay(String dateInput) {
        Date date = DateUtils.parseDate(dateInput);
        if (date != null) {
            return DateUtils.format(DateUtils.addDays(date, 1), "yyyy-MM-dd HH:mm:ss");
        }
        return "";
    }


    private void checkFieldParam(List<QueryGridDataRequest.Option> optionList, Map<String, DiGridField> fieldMap) {

        if (CollectionUtils.isEmpty(optionList)) {
            return;
        }
        for (QueryGridDataRequest.Option option : optionList) {
            if ("group".equalsIgnoreCase(option.getType())) {
                this.checkFieldParam(option.getSubOptions(), fieldMap);
            } else if (!fieldMap.containsKey(option.getFieldCode())) {
                throw new BizValidException("表格中不包含字段" + option.getFieldCode());
            }
        }
    }

    private void appendData(List<Map<String, Object>> dataSrc, Map<String, DiGridField> fieldMap) {
        if (CollectionUtils.isEmpty(dataSrc)) {
            return;
        }
        List<GridRow> data = dataSrc.stream().map(GridRow::new).collect(Collectors.toList());
        Map<String, GridDictRow> dictRowMap = new HashMap<>();
        GridDictRow employeeMap = new GridDictRow();
        GridDictRow deptMap = new GridDictRow();
        Map<String, GridDictRow> categoryMap = new HashMap<>();
        Map<String, GridDictRow> tableDictMap = new HashMap<>();
        for (Map.Entry<String, DiGridField> item : fieldMap.entrySet()) {
            if (Boolean.TRUE.equals(item.getValue().getVirtualField())) {
                continue;
            }
            //分类、部门、员工、字典，表字典
            for (GridRow row : data) {
                if (row.containsKey(item.getKey())) {
                    String dictCode = item.getValue().getDict();
                    if (row.get(item.getKey()) == null) {
                        continue;
                    }
                    String dictValue = null;
                    if (row.get(item.getKey()) != null && row.get(item.getKey()) instanceof LocalDateTime) {
                        //部分日期格式化成yyyy-MM-dd HH:mm
                        dictValue = DateTimeFormatter.ISO_DATE_TIME.format((LocalDateTime) row.get(item.getKey()));
                    } else {
                        dictValue = row.get(item.getKey()).toString();
                    }
                    if (StringUtils.isBlank(dictValue)) {
                        continue;
                    }
                    if (FieldTypeEnum.Dict.getCode().equals(item.getValue().getFiledType()) && StringUtils.isNotBlank(item.getValue().getDict())) {
                        if (dictRowMap.containsKey(dictCode)) {
                            dictRowMap.get(dictCode).add(dictValue, item.getKey(), row);
                        } else {
                            GridDictRow dictRow = new GridDictRow();
                            dictRow.add(dictValue, item.getKey(), row);
                            dictRowMap.put(dictCode, dictRow);
                        }
                    } else if (FieldTypeEnum.Employee.getCode().equals(item.getValue().getFiledType())) {
                        employeeMap.add(dictValue, item.getKey(), row);
                    } else if (FieldTypeEnum.Dept.getCode().equals(item.getValue().getFiledType())) {
                        deptMap.add(dictValue, item.getKey(), row);
                    } else if (FieldTypeEnum.Category.getCode().equals(item.getValue().getFiledType())) {
                        if (!categoryMap.containsKey(item.getValue().getDict())) {
                            categoryMap.put(item.getValue().getDict(), new GridDictRow());
                        }
                        categoryMap.get(item.getValue().getDict()).add(dictValue, item.getKey(), row);
                    } else if (FieldTypeEnum.TableDict.getCode().equals(item.getValue().getFiledType())) {
                        String finalDictValue = dictValue;
                        tableDictMap.compute(item.getValue().getDict(), (k, v) -> {
                            if (v == null) {
                                v = new GridDictRow();
                            }
                            v.add(finalDictValue, item.getKey(), row);
                            return v;
                        });
                    } else if (FieldTypeEnum.Date.getCode().equals(item.getValue().getFiledType()) || FieldTypeEnum.DateTime.getCode().equals(item.getValue().getFiledType())) {
                        row.put(item.getKey(), dictValue.replace("T", " "));
                    } else {
                        break;
                    }
                }

            }
        }

        // 字典
        if (CollectionUtils.isNotEmpty(dictRowMap.entrySet())) {
            for (Map.Entry<String, GridDictRow> item : dictRowMap.entrySet()) {
                Map<String, String> dictMap = dictProvider.queryDictList(item.getKey());
                for (GridRow row : item.getValue().getRows()) {
                    for (String field : item.getValue().getFields()) {
                        appendDictName(field, row, dictMap);
                    }
                }
            }
        }
        //分类
        if (CollectionUtils.isNotEmpty(categoryMap.keySet())) {
            for (Map.Entry<String, GridDictRow> item : categoryMap.entrySet()) {
                Map<String, ITreeItem> map = dictProvider.getCategoryMap(item.getKey());
                for (String field : item.getValue().getFields()) {
                    for (GridRow row : item.getValue().getRows()) {
                        if (row.containsKey(field) && row.get(field) != null) {
                            String dictValue = row.get(field).toString();
                            if (StringUtils.isNotBlank(dictValue)) {
                                row.put(field + "DictName", map.get(dictValue).getLabel());
                            }
                        }
                    }
                }
            }
        }
        //部门
        if (CollectionUtils.isNotEmpty(deptMap.getValues())) {
            Map<String, String> dictMap = dictProvider.queryDepartment(deptMap.getValues());
            for (String deptField : deptMap.getFields()) {
                for (GridRow row : deptMap.getRows()) {
                    appendDictName(deptField, row, dictMap);
                }
            }
        }
        //员工
        if (CollectionUtils.isNotEmpty(employeeMap.getValues())) {
            Map<String, String> employeeMap1 = dictProvider.queryEmployee(employeeMap.getValues().stream().toList());
            for (String employeeField : employeeMap.getFields()) {
                for (GridRow row : employeeMap.getRows()) {
                    appendDictName(employeeField, row, employeeMap1);
                }
            }
        }
        //表格
        if (CollectionUtils.isNotEmpty(tableDictMap.entrySet())) {
            for (Map.Entry<String, GridDictRow> item : tableDictMap.entrySet()) {
                Map<String, String> dictMap = dictProvider.queryTableDictList(item.getKey(), item.getValue().getValues());
                for (GridRow row : item.getValue().getRows()) {
                    for (String field : item.getValue().getFields()) {
                        if (row.containsKey(field) && row.get(field) != null) {
                            String dictValue = row.get(field).toString();
                            if (StringUtils.isNotBlank(dictValue)) {
                                row.put(field + "DictName", dictMap.get(dictValue));
                            }
                        }
                    }
                }
            }
        }

    }

    private static void appendDictName(String deptField, GridRow row, Map<String, String> dictMap) {
        if (row.containsKey(deptField) && row.get(deptField) != null) {
            String dictValue = row.get(deptField).toString();
            if (StringUtils.isNotBlank(dictValue)) {
                if (dictValue.contains(",")) {
                    String[] vb = dictValue.split(",");
                    List<String> names = new ArrayList<>();
                    for (String v : vb) {
                        names.add(dictMap.get(v));
                    }
                    row.put(deptField + "DictName", String.join(",", names));
                } else {
                    row.put(deptField + "DictName", dictMap.get(dictValue));
                }
            }
        }
    }

    @Data
    @AllArgsConstructor
    static class KV {
        private String key;
        private BigDecimal value;
    }

    @Data
    static class QueryResult {
        List<Map<String, Object>> data;
        long total;
    }


}

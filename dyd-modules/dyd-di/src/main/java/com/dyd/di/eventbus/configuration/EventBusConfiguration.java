package com.dyd.di.eventbus.configuration;

import com.dyd.di.eventbus.EventBusListener;
import com.google.common.eventbus.EventBus;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/8
 */
@Configuration
public class EventBusConfiguration {
    /**
     * 实例化 EventBus 对象，并自动注册所有订阅类对象
     *
     * @param eventListenerList 所有实现了 EventBusListener 接口的实现类
     * @return
     */
    @Bean
    public EventBus eventBus(List<EventBusListener> eventListenerList) {
        EventBus eventBus = new EventBus();
        if (eventListenerList != null && !eventListenerList.isEmpty()) {
            eventListenerList.iterator().forEachRemaining(eventListener -> eventBus.register(eventListener));
        }
        return eventBus;
    }
}

package com.dyd.di.order.pojo.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class OrderProductDetailDTO {

    /**
     * 商机ID
     */
    private Long nicheId;

    /**
     * 商机编号
     */
    private String nicheNo;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long preSaleId;

    /**
     * 产品方案编号
     */
    private String preSaleCode;
    /**
     * 方案类型，1：标准品；2：非标
     */
    private Integer preSaleType;

    /**
     * 方案名称
     */
    private String preSaleName;

    /**
     * 方案类型 1:项目 2:备件 3:燃配 4:出口  5:标准品
     */
    private String orderType;

    /**
     * 物料号
     */
    private String materialNo;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 版本号
     */
    private String versionNo;

    /**
     * 展示的版本号
     */
    private String showVersionNo;

    /**
     * 需求数量
     */
    private Integer demandNum;

    /**
     * 物料库存
     */
    private Integer materialStock;

    /**
     * 方案交付日期(项目期望交付日期)
     */
    private String expecteDate;

    /**
     * 售价报价  销售报价
     */
    private BigDecimal saleQuote;


    /**
     * 实时合计成本
     */
    private BigDecimal realCostSumCosts;

    /**
     * 实时毛利率
     */
    private BigDecimal realGrossMargin;
    /**
     * 机械设计工期
     */
    private Integer mechanicalDesignDuration;

    /**
     * 电器设计工期
     */
    private Integer electricalDesignPeriod;
    /**
     * 技术支持周期(天)
     */
    private Integer techSupportDay;
    /**
     * 研发周期(天)
     */
    private Integer rdDay;

    /**
     * 供应货期(天)
     */
    private Integer supplyDay;

    /**
     * 生产周期(天)
     */
    private Integer produceDay;

    /**
     * 实施周期(天)
     */
    private Integer implementDay;

    /**
     * 订单方案状态
     */
    private String orderPreSaleStatus;

    /**
     * 技术支持负责人code
     */
    private String techSupportOwnerCode;

    /**
     * 技术支持负责人name
     */
    private String techSupportOwnerName;


    /**
     * 安装调试排期详情  如果没有这个 就代表 没有排期
     */
    private OrderInstallDetailInfoDTO detailInfoDTO;
    /**
     * 电表排期详情  如果没有这个 就代表 没有排期
     */
    private OrderElectricDetailInfoDTO electricDetailInfoDTO;

    /**
     * 机械排期详情  如果没有这个 就代表 没有排期
     */
    private OrderMachineDetailInfoDTO machineDetailInfoDTO;

    /**
     * 技术支持复核
     */
    private OrderSupportCheckDetailInfoDTO orderSupportCheckDetailInfoDTO;

    /**
     * 生产排期详情  如果没有这个 就代表 没有排期
     */
    private OrderProduceDetailInfoDTO produceDetailInfoDTO;

    /**
     * 质量排期详情  如果没有这个 就代表 没有排期
     */
    private OrderQualityDetailInfoDTO qualityDetailInfoDTO;

    /**
     * 物料排期详情  如果没有这个 就代表 没有排期
     */
    private OrderMaterielPlanDetailInfoDTO materielPlanDetailInfoDTO;


}

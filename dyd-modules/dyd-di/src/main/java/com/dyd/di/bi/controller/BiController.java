package com.dyd.di.bi.controller;

import com.dyd.common.core.domain.R;
import com.dyd.di.api.model.DeptRealDateResponse;
import com.dyd.di.bi.service.IBiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RequestMapping("bi")
@RestController
public class BiController {

    @Autowired
    private IBiService iBiService;

    /**
     * 获取部门月实际值
     * @param month
     * @return
     */
    @GetMapping("/getDeptRealData")
    public R<List<DeptRealDateResponse>> getDeptRealData(@RequestParam("month") String month,@RequestParam("bu") String bu){
        return R.ok(iBiService.getDeptRealData(month,bu));
    }


    /**
     * 获取销售员月实际值
     * @param month
     * @return
     */
    @GetMapping("/getSalerMonthDept")
    public R<List<DeptRealDateResponse>> getSalerMonthDept(@RequestParam("month") String month,@RequestParam("saler") String saler){
        return R.ok(iBiService.getSalerMonthDept(month,saler));
    }




}

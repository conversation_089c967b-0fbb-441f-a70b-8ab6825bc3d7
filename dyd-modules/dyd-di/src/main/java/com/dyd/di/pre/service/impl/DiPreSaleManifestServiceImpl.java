package com.dyd.di.pre.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dyd.common.core.utils.DateUtils;
import com.dyd.di.pre.domain.request.SaveManifestRealFeeRequest;
import com.dyd.di.pre.entity.DiPreSaleQuote;
import com.dyd.di.pre.mapper.DiPreSaleQuoteMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dyd.di.pre.mapper.DiPreSaleManifestMapper;
import com.dyd.di.pre.entity.DiPreSaleManifest;
import com.dyd.di.pre.service.IDiPreSaleManifestService;

/**
 * 售前方案清单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
@Service
public class DiPreSaleManifestServiceImpl extends ServiceImpl<DiPreSaleManifestMapper, DiPreSaleManifest> implements IDiPreSaleManifestService {
    @Autowired
    private DiPreSaleManifestMapper diPreSaleManifestMapper;

    /**
     * 查询售前方案清单
     *
     * @param id 售前方案清单主键
     * @return 售前方案清单
     */
    @Override
    public DiPreSaleManifest selectDiPreSaleManifestById(Long id) {
        return diPreSaleManifestMapper.selectDiPreSaleManifestById(id);
    }

    /**
     * 查询售前方案清单列表
     *
     * @param diPreSaleManifest 售前方案清单
     * @return 售前方案清单
     */
    @Override
    public List<DiPreSaleManifest> selectDiPreSaleManifestList(DiPreSaleManifest diPreSaleManifest) {
        return diPreSaleManifestMapper.selectDiPreSaleManifestList(diPreSaleManifest);
    }

    /**
     * 新增售前方案清单
     *
     * @param diPreSaleManifest 售前方案清单
     * @return 结果
     */
    @Override
    public int insertDiPreSaleManifest(DiPreSaleManifest diPreSaleManifest) {
        diPreSaleManifest.setCreateTime(DateUtils.getNowDate());
        return diPreSaleManifestMapper.insertDiPreSaleManifest(diPreSaleManifest);
    }

    /**
     * 修改售前方案清单
     *
     * @param diPreSaleManifest 售前方案清单
     * @return 结果
     */
    @Override
    public int updateDiPreSaleManifest(DiPreSaleManifest diPreSaleManifest) {
        diPreSaleManifest.setUpdateTime(DateUtils.getNowDate());
        return diPreSaleManifestMapper.updateDiPreSaleManifest(diPreSaleManifest);
    }

    /**
     * 批量删除售前方案清单
     *
     * @param ids 需要删除的售前方案清单主键
     * @return 结果
     */
    @Override
    public int deleteDiPreSaleManifestByIds(Long[] ids) {
        return diPreSaleManifestMapper.deleteDiPreSaleManifestByIds(ids);
    }

    /**
     * 删除售前方案清单信息
     *
     * @param id 售前方案清单主键
     * @return 结果
     */
    @Override
    public int deleteDiPreSaleManifestById(Long id) {
        return diPreSaleManifestMapper.deleteDiPreSaleManifestById(id);
    }


}

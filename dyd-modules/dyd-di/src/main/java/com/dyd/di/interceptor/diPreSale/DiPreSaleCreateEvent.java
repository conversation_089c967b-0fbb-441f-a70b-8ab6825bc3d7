package com.dyd.di.interceptor.diPreSale;

import org.springframework.context.ApplicationEvent;

public class DiPreSaleCreateEvent extends ApplicationEvent {

    private String preSaleCode;

    public DiPreSaleCreateEvent(Object source, String preSaleCode) {
        super(source);
        this.preSaleCode = preSaleCode;
    }

    public String getPreSaleCode() {
        return preSaleCode;
    }
}

package com.dyd.di.common;

import com.alibaba.fastjson2.JSON;
import com.dyd.common.core.domain.R;
import com.dyd.common.security.utils.SecurityUtils;
import com.dyd.di.process.service.DiProjectRelationUserService;
import com.dyd.system.api.RemoteSysService;
import com.dyd.system.api.domain.SysUserRoleData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DiCommonService {

    @Autowired
    private RemoteSysService remoteSysService;

    @Autowired
    private DiProjectRelationUserService diProjectRelationUserService;

    /**
     * 获取项目号
     * @return
     */
    public DiCommonResponse getProjectNos(){


        DiCommonResponse diCommonResponse = new DiCommonResponse();

        //当前登录人id
        Long userid = SecurityUtils.getLoginUser().getUserid();
        R<SysUserRoleData> sysUserDataSource = remoteSysService.getSysUserDataSource(userid);


        if(Objects.nonNull(sysUserDataSource) && sysUserDataSource.isSuccess()){

            //限制
            if(0 == sysUserDataSource.getData().getType()){
                List<String> jobNumbers = sysUserDataSource.getData().getUserDataList().stream().map(SysUserRoleData.roleData::getJobNumber).collect(Collectors.toList());
                log.info("合同列表工号权限{}", JSON.toJSONString(jobNumbers));

                diCommonResponse.setType(0);
                diCommonResponse.setProjectNos(diProjectRelationUserService.getProjectNosByJobNumber(jobNumbers));
            }else{
                diCommonResponse.setType(1);
            }
        }

        return diCommonResponse;
    }
}

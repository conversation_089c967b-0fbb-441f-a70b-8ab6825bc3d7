package com.dyd.di.order.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class OrderMaterielUpdateVO {

    /**
     * 主键id
     */
    @NotNull(message = "生产id不能为空")
    private Long id;


    /**
     * 产品方案编号
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long preSaleId;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    private String orderNo;

    /**
     * 供应货期(天)
     */
    private Integer supplyDay;

    /**
     * 采购负责人
     */
    private String purchaseUserId;

    /**
     * 采购负责人姓名
     */
    private String purchaseUserName;

    /**
     * 计划全套到货日期
     */
    private Date latestCompleteTime;
    private Date expectStartTime;
    private Date expectEndTime;


    /**
     * 任务备注
     */
    private String taskNote;

    /**
     * 忽略钉钉消息
     */
    private boolean ignoreDingDing = false;


}

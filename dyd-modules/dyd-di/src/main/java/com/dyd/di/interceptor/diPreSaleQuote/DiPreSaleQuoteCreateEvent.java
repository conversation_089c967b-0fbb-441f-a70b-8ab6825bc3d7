package com.dyd.di.interceptor.diPreSaleQuote;

import org.springframework.context.ApplicationEvent;

public class DiPreSaleQuoteCreateEvent extends ApplicationEvent {

    private String preSaleQuoteCode;

    public DiPreSaleQuoteCreateEvent(Object source, String preSaleQuoteCode) {
        super(source);
        this.preSaleQuoteCode = preSaleQuoteCode;
    }

    public String getPreSaleQuoteCode() {
        return preSaleQuoteCode;
    }
}

package com.dyd.di.marketing.controller;

import com.alibaba.fastjson2.JSONObject;
import com.dyd.common.core.domain.R;
import com.dyd.common.datascope.annotation.DataScope;
import com.dyd.di.marketing.domain.vo.BiSalesWeekAchievementVo;
import com.dyd.di.marketing.domain.vo.JdySalesWeekAchievement;
import com.dyd.di.marketing.domain.vo.SalesDailyOapiReportContentVo;
import com.dyd.di.marketing.domain.vo.StatisticsVo;
import com.dyd.di.marketing.manage.DiMarketingDailySyncDdManage;
import com.dyd.di.marketing.manage.DiMarketingDailySyncDdTestManage;
import com.dyd.di.marketing.service.DiMarketingDailyService;
import com.dyd.di.marketing.service.DiMarketingScheduleService;
import com.dyd.dingtalk.bean.report.DdSalesDailyAddResponse;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 销售日报
 */
@RestController
@RequestMapping("/salesDaily")
public class DiMarkertingSalesDailyController {

    @Resource
    private DiMarketingScheduleService diMarketingScheduleService;

    @Resource
    private DiMarketingDailyService dailyService;

    @Resource
    DiMarketingDailySyncDdManage syncDdDailyManage;

    @Resource
    DiMarketingDailySyncDdTestManage syncDdDailyTestManage;

    /**
     * 同步钉钉排班信息
     */
    @PostMapping("/syncScheduleListbyusers")
    public R<String> syncScheduleListbyusers() {
        diMarketingScheduleService.syncScheduleListbyusers();
        return R.ok("同步成功！");
    }

    /**
     * 日报统计查询接口
     */
    @PostMapping("/statistics")
    @DataScope(deptAlias = "d", userAlias = "u")
    public R<Map<String, Object>> statistics(@RequestBody StatisticsVo statisticsVo) {
        return R.ok(diMarketingScheduleService.statistics(statisticsVo));
    }

    /**
     * 查询请假状态
     */
    @GetMapping("/getQjzt")
    public R<String> getQjzt(String id) {
        return R.ok(diMarketingScheduleService.getQjzt(id));
    }

    /**
     * 钉钉日志与销售日报同步
     */
    @PostMapping("/syncSalesDailyToDD/{dailyId}")
    public R<DdSalesDailyAddResponse> syncSalesDailyToDD(@PathVariable Long dailyId) {
        return R.ok(syncDdDailyManage.manage(dailyId));
    }

    @PostMapping("/syncSalesDailyToDDTest")
    public R<DdSalesDailyAddResponse> syncSalesDailyToDDTest(@RequestBody JSONObject dailyContent) {
        return R.ok(syncDdDailyTestManage.manage(dailyContent));
    }

    /**
     * 钉钉日志与销售日报同步
     */
    @PostMapping("/syncDdSalesDaily")
    public R<DdSalesDailyAddResponse> syncDdSalesDaily() {
        SalesDailyOapiReportContentVo salesDailyOapiReportContentVo = new SalesDailyOapiReportContentVo();
        salesDailyOapiReportContentVo.setCompletionStatus("---\n" +
                "\n" +
                "\n" +
                "## 跟进记录\n" +
                "\n" +
                "1. **第一条记录**\n" +
                "    - **内容**：跟进潜在客户张先生，讨论项目需求。\n" +
                "    - **跟进人**：李销售\n" +
                "    - **跟进日期**：2023-05-15\n" +
                "    - **工时**：2.5小时\n" +
                "    - **跟进类别**：商机跟进\n" +
                "    - **来源**：线上广告\n" +
                "    - **客户名称**：张氏企业有限公司\n" +
                "    - **项目名称**：智能化改造项目\n" +
                "    - **项目ID**：PRJ001\n" +
                "    - **客户ID**：CLI001\n" +
                "\n" +
                "**备注**：跟进李销售与张先生的讨论中，客户对智能化改造项目的兴趣浓厚，计划下周进行技术演示。\n" +
                "\n" +
                "2. **第二条记录**\n" +
                "    - **内容**：与老客户王女士确认合同细节及交付时间。\n" +
                "    - **跟进人**：赵客服\n" +
                "    - **跟进日期**：2023-05-15\n" +
                "    - **工时**：1.5小时\n" +
                "    - **跟进类别**：客户维护\n" +
                "    - **来源**：线下活动\n" +
                "    - **客户名称**：王氏集团\n" +
                "    - **项目名称**：办公系统升级项目\n" +
                "    - **项目ID**：PRJ002\n" +
                "    - **客户ID**：CLI002\n" +
                "\n" +
                "**备注**：赵客服与王女士确认合同细节顺利，预计本周五完成合同签署，项目将于下月启动。\n" +
                "\n" +
                "## 其他工作\n" +
                "\n" +
                "1. **第一项工作**\n" +
                "    - **类型**：市场调研\n" +
                "    - **工作内容**：分析竞争对手新产品功能，撰写报告。\n" +
                "    - **工时**：4小时\n" +
                "    - **需要协调的问题或个人分析**：需与技术部门沟通了解我司产品优势。\n" +
                "\n" +
                "**备注**：跟进李销售与张先生的讨论中，客户对智能化改造项目的兴趣浓厚，计划下周进行技术演示。\n" +
                "\n" +
                "2. **第二项工作**\n" +
                "    - **类型**：内部培训\n" +
                "    - **工作内容**：为新员工进行CRM系统操作培训。\n" +
                "    - **工时**：3小时\n" +
                "    - **需要协调的问题或个人分析**：准备培训材料，确保新员工熟练使用系统。\n" +
                "\n" +
                "**备注**：跟进李销售与张先生的讨论中，客户对智能化改造项目的兴趣浓厚，计划下周进行技术演示。\n" +
                "\n" +
                "---");
        salesDailyOapiReportContentVo.setEncounterProblem("\n" +
                "\n" +
                "\n" +
                " 遇到工作上的问题时，可能会让人感到有些困扰和压力，但这也是成长和学习的机会。以下是一些建议，希望能帮助你更好地应对和解决这些问题：\n" +
                "\n" +
                "1. **冷静分析**：\n" +
                "   - 首先，保持冷静，不要让情绪影响你的判断。\n" +
                "   - 仔细分析问题，确定问题的本质和根源。\n" +
                "\n" +
                "2. **寻求帮助**：\n" +
                "   - 如果问题超出你的能力范围，不要犹豫向同事、上级或专业人士寻求帮助。\n" +
                "   - 团队合作和沟通是解决问题的关键。\n" +
                "\n" +
                "3. **制定解决方案**：\n" +
                "   - 根据问题的性质，制定一个或多个可能的解决方案。\n" +
                "   - 评估每个方案的可行性、成本和效益，选择最合适的方案。\n" +
                "\n" +
                "4. **执行并监控**：\n" +
                "   - 实施选定的解决方案，并确保所有相关人员都了解并遵循计划。\n" +
                "   - 监控解决方案的执行过程，及时调整策略以应对任何新出现的问题。\n" +
                "\n" +
                "5. **反思与学习**：\n" +
                "   - 在问题解决后，花时间反思整个过程，了解哪些方法有效，哪些需要改进。\n" +
                "   - 将这次经历视为一个学习的机会，为未来的挑战做好准备。\n" +
                "\n" +
                "6. **保持积极态度**：\n" +
                "   - 保持乐观和积极的态度，相信自己能够克服困难。\n" +
                "   - 记住，每个挑战都是成长的机会，每一次的困难都会让你变得更加强大。\n" +
                "\n" +
                "7. **寻求支持**：\n" +
                "   - 如果问题给你的心理造成了压力，不妨与亲友或心理咨询师分享你的感受。\n" +
                "   - 有时候，倾诉和获得支持是缓解压力的重要方式。\n" +
                "\n" +
                "总之，面对工作上的问题，关键在于保持冷静、积极寻求帮助、制定并执行解决方案，并从中学习和成长。希望这些建议能对你有所帮助，祝你工作顺利！");
        salesDailyOapiReportContentVo.setWorkThinking("\n" +
                "\n" +
                "\n" +
                "面对今天的工作，进行深入的思考是非常重要的，这不仅能帮助你更好地应对当前的任务，还能促进个人和职业发展。以下是一些关于今天工作思考的建议和框架，希望能激发你的灵感：\n" +
                "\n" +
                "1. **回顾与评估**：\n" +
                "   - 回顾昨天或上周的工作完成情况，评估哪些任务顺利完成，哪些遇到了挑战或未完成。\n" +
                "   - 分析成功和失败的原因，提炼出可复制的成功经验和需要改进的地方。\n" +
                "\n" +
                "2. **明确今日目标**：\n" +
                "   - 根据当前的工作计划和优先级，设定清晰、具体、可衡量的今日工作目标。\n" +
                "   - 确保这些目标与你的长期职业规划和公司目标保持一致。\n" +
                "\n" +
                "3. **策略与计划**：\n" +
                "   - 针对每个目标，制定详细的执行计划和策略，包括时间分配、资源调用和潜在问题的应对策略。\n" +
                "   - 考虑如何优化工作流程，提高工作效率。\n" +
                "\n" +
                "4. **技能提升**：\n" +
                "   - 思考在工作中需要哪些新技能或知识来提升自己，比如专业技能、沟通技巧、时间管理等。\n" +
                "   - 计划如何获取这些技能，比如参加培训、阅读专业书籍、向同事学习等。\n" +
                "\n" +
                "5. **团队协作**：\n" +
                "   - 思考如何与团队成员更有效地沟通和协作，共同推动项目进展。\n" +
                "   - 识别团队中的强项和需要改进的地方，提出建设性的反馈和建议。\n" +
                "\n" +
                "6. **应对变化**：\n" +
                "   - 认识到工作中总会遇到不可预见的变化和挑战，思考如何灵活应对，保持工作的连续性和稳定性。\n" +
                "   - 培养解决问题的能力，学会在压力下保持冷静和专注。\n" +
                "\n" +
                "7. **个人成长与反思**：\n" +
                "   - 在每天的工作结束后，花时间反思自己的表现，记录收获和成长。\n" +
                "   - 定期评估自己的职业目标是否仍然符合个人价值观和兴趣，必要时进行调整。\n" +
                "\n" +
                "8. **健康与平衡**：\n" +
                "   - 思考如何保持工作与生活的平衡，确保有足够的时间休息和放松，避免过度劳累。\n" +
                "   - 关注自己的身心健康，培养积极的生活态度和兴趣爱好。\n" +
                "\n" +
                "通过这样全面的思考，你不仅能更好地应对今天的工作挑战，还能为未来的职业发展打下坚实的基础。希望这些建议能帮助你度过充实而有意义的一天！");
        salesDailyOapiReportContentVo.setUserid("16625119062366702");
        salesDailyOapiReportContentVo.setToChat(true);
        List<String> users = new ArrayList<>();
        users.add("16625119062366702");
        users.add("17223007965839695");
        users.add("17266218045775602");
        users.add("17304228967827079");
        users.add("1717377012095607");
        salesDailyOapiReportContentVo.setToUserids(users);
        return R.ok(diMarketingScheduleService.addDdSalesDaily(salesDailyOapiReportContentVo));
    }

    /**
     * 销售目标行动拆解计划
     * @param jdySalesWeekAchievement
     * @return
     */
    @PostMapping("/getBiSalesWeekAchievement")
    public R<BiSalesWeekAchievementVo> getBiSalesWeekAchievement(@RequestBody JdySalesWeekAchievement jdySalesWeekAchievement) {

        /*BiSalesWeekAchievementVo vo = new BiSalesWeekAchievementVo();
        vo.setNewMonthNicheAmount(10.0);
        vo.setNewMonthNicheTargetValue(10.0);
        vo.setNewMonthNicheActualValue(10.0);
        vo.setNewWeekNicheAmount(10.0);
        vo.setNewWeekNicheTargetValue(10.0);
        vo.setNewWeekNicheActualValue(10.0);
        vo.setMonthNicheDifferenceValue(10.0);
        vo.setWeekNicheDifferenceValue(10.0);

        List<BiPaymentDetailsVo> biPaymentDetailsVoList = new ArrayList<>();

        BiPaymentDetailsVo biPaymentDetailsVo = new BiPaymentDetailsVo();
        biPaymentDetailsVo.setCustomerName("10.0");
        biPaymentDetailsVo.setPaymentStatus("10.0");
        biPaymentDetailsVo.setReceivables(10.0);
        biPaymentDetailsVo.setRepaymentAmount(10.0);
        biPaymentDetailsVo.setRepaymentDate("2024-02-01");
        biPaymentDetailsVoList.add(biPaymentDetailsVo);

        BiPaymentDetailsVo biPaymentDetailsVo1 = new BiPaymentDetailsVo();
        biPaymentDetailsVo1.setCustomerName("10.0");
        biPaymentDetailsVo1.setPaymentStatus("10.0");
        biPaymentDetailsVo1.setReceivables(10.0);
        biPaymentDetailsVo1.setRepaymentAmount(10.0);
        biPaymentDetailsVo1.setRepaymentDate("2024-02-01");
        biPaymentDetailsVoList.add(biPaymentDetailsVo1);

        vo.setBiPaymentDetailsVoList(biPaymentDetailsVoList);

        List<BiSignOffDetailsVo> biSignOffDetailsVoList = new ArrayList<>();

        BiSignOffDetailsVo biSignOffDetailsVo = new BiSignOffDetailsVo();
        biSignOffDetailsVo.setSignNo("10.0");
        biSignOffDetailsVo.setCustomerName("10.0");
        biSignOffDetailsVo.setSignOffDetails("10.0");
        biSignOffDetailsVoList.add(biSignOffDetailsVo);

        BiSignOffDetailsVo biSignOffDetailsVo1 = new BiSignOffDetailsVo();
        biSignOffDetailsVo1.setSignNo("10.0");
        biSignOffDetailsVo1.setCustomerName("10.0");
        biSignOffDetailsVo1.setSignOffDetails("10.0");
        biSignOffDetailsVoList.add(biSignOffDetailsVo1);

        vo.setBiSignOffDetailsVoList(biSignOffDetailsVoList);*/
        return R.ok(dailyService.getBiSalesWeekAchievement(jdySalesWeekAchievement));
    }
}

package com.dyd.di.marketing.manage.aspect;

import com.dyd.di.marketing.domain.DiMarketingNiche;
import com.dyd.di.marketing.manage.context.NicheEditContext;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

/**
 * 商机编辑性能监控切面
 * 
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
public class NicheEditPerformanceAspect {
    
    /**
     * 定义切点：商机编辑处理器的process方法
     */
    @Pointcut("execution(* com.dyd.di.marketing.manage.DiMarketingNicheEditProcessor.process(..))")
    public void nicheEditProcessPointcut() {}
    
    /**
     * 定义切点：所有处理器的handle方法
     */
    @Pointcut("execution(* com.dyd.di.marketing.manage.handler.*.handle(..))")
    public void handlerPointcut() {}
    
    /**
     * 监控主处理流程性能
     */
    @Around("nicheEditProcessPointcut()")
    public Object monitorProcessPerformance(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        String nicheId = extractNicheId(joinPoint.getArgs());
        
        log.info("开始执行商机编辑处理，商机ID: {}", nicheId);
        
        try {
            Object result = joinPoint.proceed();
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            log.info("商机编辑处理完成，商机ID: {}, 耗时: {}ms", nicheId, duration);
            
            // 如果处理时间超过阈值，记录警告
            if (duration > 5000) { // 5秒阈值
                log.warn("商机编辑处理耗时过长，商机ID: {}, 耗时: {}ms", nicheId, duration);
            }
            
            return result;
            
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            log.error("商机编辑处理异常，商机ID: {}, 耗时: {}ms, 异常: {}", 
                nicheId, duration, e.getMessage(), e);
            
            throw e;
        }
    }
    
    /**
     * 监控各个处理器性能
     */
    @Around("handlerPointcut()")
    public Object monitorHandlerPerformance(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        String handlerName = joinPoint.getTarget().getClass().getSimpleName();
        String nicheId = extractNicheIdFromContext(joinPoint.getArgs());
        
        log.debug("开始执行处理器: {}, 商机ID: {}", handlerName, nicheId);
        
        try {
            Object result = joinPoint.proceed();
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            log.debug("处理器执行完成: {}, 商机ID: {}, 耗时: {}ms", handlerName, nicheId, duration);
            
            // 如果单个处理器耗时超过阈值，记录警告
            if (duration > 2000) { // 2秒阈值
                log.warn("处理器执行耗时过长: {}, 商机ID: {}, 耗时: {}ms", handlerName, nicheId, duration);
            }
            
            return result;
            
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            log.error("处理器执行异常: {}, 商机ID: {}, 耗时: {}ms, 异常: {}", 
                handlerName, nicheId, duration, e.getMessage(), e);
            
            throw e;
        }
    }
    
    /**
     * 从方法参数中提取商机ID
     */
    private String extractNicheId(Object[] args) {
        if (args != null && args.length > 0) {
            Object firstArg = args[0];
            if (firstArg instanceof DiMarketingNiche) {
                DiMarketingNiche niche = (DiMarketingNiche) firstArg;
                return niche.getId();
            }
        }
        return "unknown";
    }
    
    /**
     * 从上下文中提取商机ID
     */
    private String extractNicheIdFromContext(Object[] args) {
        if (args != null && args.length > 0) {
            Object firstArg = args[0];
            if (firstArg instanceof NicheEditContext) {
                NicheEditContext context = (NicheEditContext) firstArg;
                if (context.getCurrentNiche() != null) {
                    return context.getCurrentNiche().getId();
                }
            }
        }
        return "unknown";
    }
}

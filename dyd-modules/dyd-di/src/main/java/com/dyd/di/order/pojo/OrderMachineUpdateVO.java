package com.dyd.di.order.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class OrderMachineUpdateVO {

    /**
     * 主键id
     */
    @NotNull(message = "电气设计id不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;


    /**
     * 产品方案编号
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long preSaleId;


    /**
     * 设计进度
     */
    private BigDecimal designProcess;


    /**
     * 机械设计负责人
     */
    private String machineDesignUserId;

    /**
     * 机械设计负责人姓名
     */
    private String machineDesignUserName;

    /**
     * 任务备注
     */
    private String taskNote;


    private Date expectStartTime;

    private Date expectEndTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date bomExpectEndTime;
    private boolean ignoreDingDing;


}

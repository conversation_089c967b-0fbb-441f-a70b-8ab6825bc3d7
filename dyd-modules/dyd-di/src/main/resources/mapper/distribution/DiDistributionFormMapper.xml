<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dyd.di.distribution.mapper.DiDistributionFormMapper">

    <resultMap type="com.dyd.di.distribution.domain.DiDistributionForm" id="DiDistributionFormResult">
        <result property="id"    column="id"    />
        <result property="distributionNumber"    column="distribution_number"    />
        <result property="orderId"    column="order_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="traceId"    column="trace_id"    />
    </resultMap>

    <sql id="selectDiDistributionFormVo">
        select id, distribution_number, order_id, create_by, create_time, update_by, update_time, trace_id from di_distribution_form
    </sql>

    <select id="selectDiDistributionFormList" parameterType="com.dyd.di.distribution.domain.DiDistributionForm" resultMap="DiDistributionFormResult">
        <include refid="selectDiDistributionFormVo"/>
        <where>
            <if test="distributionNumber != null  and distributionNumber != ''"> and distribution_number = #{distributionNumber}</if>
            <if test="orderId != null "> and order_id = #{orderId}</if>
            <if test="traceId != null  and traceId != ''"> and trace_id = #{traceId}</if>
        </where>
    </select>

    <select id="selectDiDistributionFormById" parameterType="String" resultMap="DiDistributionFormResult">
        <include refid="selectDiDistributionFormVo"/>
        where id = #{id}
    </select>

    <insert id="insertDiDistributionForm" parameterType="com.dyd.di.distribution.domain.DiDistributionForm" useGeneratedKeys="true" keyProperty="id">
        insert into di_distribution_form
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="distributionNumber != null and distributionNumber != ''">distribution_number,</if>
            <if test="orderId != null">order_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="traceId != null and traceId != ''">trace_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="distributionNumber != null and distributionNumber != ''">#{distributionNumber},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="traceId != null and traceId != ''">#{traceId},</if>
         </trim>
    </insert>

    <update id="updateDiDistributionForm" parameterType="com.dyd.di.distribution.domain.DiDistributionForm">
        update di_distribution_form
        <trim prefix="SET" suffixOverrides=",">
            <if test="distributionNumber != null and distributionNumber != ''">distribution_number = #{distributionNumber},</if>
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="traceId != null and traceId != ''">trace_id = #{traceId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDiDistributionFormById" parameterType="String">
        delete from di_distribution_form where id = #{id}
    </delete>

    <delete id="deleteDiDistributionFormByIds" parameterType="String">
        delete from di_distribution_form where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>

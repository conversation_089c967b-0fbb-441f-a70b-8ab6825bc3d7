package com.dyd.exchange.erp.jobs.ami;

import com.dyd.ami.api.RemoteAmiService;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 销售绩效
 */
@Component
public class SyncJ<PERSON><PERSON>ob extends I<PERSON><PERSON><PERSON>andler {

    @Autowired
    private RemoteAmiService remoteAmiService;

    @Async
    @XxlJob("SyncJdyJob")
    @Override
    public void execute() throws Exception {
        remoteAmiService.syncJdy();

    }
}

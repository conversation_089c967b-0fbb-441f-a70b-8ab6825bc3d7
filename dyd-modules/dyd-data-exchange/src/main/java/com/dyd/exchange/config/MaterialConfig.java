package com.dyd.exchange.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 物料同步
 */
@Data
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "material")
public class MaterialConfig {

    /**
     * 物料号
     */
    private List<String> materials;

    /**
     * bom白名单
     */
    private List<String> boms;

}

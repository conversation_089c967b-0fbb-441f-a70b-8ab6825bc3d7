package com.dyd.job.task;

import com.dyd.common.core.domain.R;
import com.dyd.notice.api.RemoteNoticeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 消息通知及发布组件定时任务
 *
 * <AUTHOR>
 */
@Component("noticeTask")
public class NoticeTask {
    @Autowired
    private RemoteNoticeService remoteNoticeService;


    /**
     *每日通知
     */
    public void syncRobotNotice()
    {
        R<String> result = remoteNoticeService.robotNotice();
        System.out.println(result.toString());
    }

    /**
     *定时停车收费通知
     */
    public void parkingFee()
    {
        R<String> result = remoteNoticeService.parkingFee();
        System.out.println(result.toString());
    }
}

package com.dyd.notice.domain.REST;

import com.dyd.common.core.web.domain.BaseEntity;
import lombok.Data;


/**
 * @description request
 * @author: wx
 * @date: 2023/2/2 14:03
 */
public class ProductBasicInfoRequest extends BaseEntity {

    private static final long serialVersionUID = -2468467314313492827L;
    private String shareId;
    private String shareCode;
    private String searchStr;
    private String imageType;
    private String itemCode;
    private String itemName;

    public String getShareId() {
        return shareId;
    }

    public void setShareId(String shareId) {
        this.shareId = shareId;
    }

    public String getShareCode() {
        return shareCode;
    }

    public void setShareCode(String shareCode) {
        this.shareCode = shareCode;
    }

    public String getSearchStr() {
        return searchStr;
    }

    public void setSearchStr(String searchStr) {
        this.searchStr = searchStr;
    }

    public String getImageType() {
        return imageType;
    }

    public void setImageType(String imageType) {
        this.imageType = imageType;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }
}

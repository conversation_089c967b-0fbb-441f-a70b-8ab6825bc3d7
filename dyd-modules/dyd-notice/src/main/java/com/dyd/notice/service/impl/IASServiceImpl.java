package com.dyd.notice.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dyd.common.core.utils.StringUtils;
import com.dyd.common.core.utils.bean.BeanUtils;
import com.dyd.jdy.JdyClient;
import com.dyd.jdy.JdyDBCClient;
import com.dyd.jdy.bean.common.Cond;
import com.dyd.jdy.bean.common.Filter;
import com.dyd.jdy.bean.common.ValueString;
import com.dyd.jdy.bean.dbc.operate.JdyDBCCreateRequest;
import com.dyd.jdy.bean.dbc.operate.JdyDBCListRequest;
import com.dyd.jdy.bean.jdy.request.AfterSaleDispatchRequest;
import com.dyd.jdy.bean.jdy.request.AfterSaleDispatchUpdateRequest;
import com.dyd.jdy.bean.jdy.response.AfterSaleDispatchResponse;
import com.dyd.jdy.dbc.JdyDBCRequest;
import com.dyd.notice.domain.ias.*;
import com.dyd.notice.service.IASCustomizeService;
import com.dyd.notice.service.IASService;
import com.dyd.notice.service.ias.BacfieldconfigService;
import com.dyd.notice.service.ias.IasDataConfigAttachedService;
import com.dyd.notice.service.ias.IasDataConfigOtherService;
import com.dyd.notice.service.ias.IasDataConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static com.dyd.jdy.constant.DBCInterfaceOperateConstants.*;
import static com.dyd.jdy.constant.JdyConstants.*;

/**
 * @description 智能助手服务
 * @author: wx
 * @date: 2023/5/16 12:22
 */
@Service
@Slf4j
public class IASServiceImpl implements IASService {

    // todo 差一些类型


    @Autowired
    private IasDataConfigService iasDataConfigService;
    @Autowired
    private IasDataConfigAttachedService iasDataConfigAttachedService;
    @Autowired
    private IasDataConfigOtherService iasDataConfigOtherService;
    @Autowired
    private IasDataGripperPlusServiceImpl iasDataGripperPlusService;
    @Autowired
    private BacfieldconfigService bacfieldconfigService;
    @Autowired
    private IASCustomizeService iasCustomizeService;
    @Resource
    private JdyClient jdyClient;

    @Override
    public void syncWidgets() {
        JdyDBCClient jdyDBCClient = new JdyDBCClient();
        JdyDBCRequest request = new JdyDBCRequest();
        request.setApp_id("600549610e809f00070f2c48");
        request.setEntry_id("60348ef975c0b500075b7d0a");
        String url = String.format("%s%s%s", URL_PREFIX, VERSION, URL_WIDGET_SUFFIX);
        JSONObject jsonObject = jdyDBCClient.dbcCall(JSONObject.parseObject(JSON.toJSONString(request)), url);
        log.info(jsonObject.toString());
        JSONArray widgets = jsonObject.getJSONArray("widgets");
        List<IasDataGripper> entities = new ArrayList<>();
        for (int i = 0; i < widgets.size(); i++) {
            JSONObject widget = widgets.getJSONObject(i);
            String name = widget.getString("name");
            String label = widget.getString("label");
            String type = widget.getString("type");
            IasDataGripper iasDataGripper = new IasDataGripper();
            iasDataGripper.setParentName("0");
            iasDataGripper.setTargetName("");
            iasDataGripper.setWidgetsName(name);
            iasDataGripper.setWidgetsLabel(label);
            iasDataGripper.setWidgetsType(type);
            entities.add(iasDataGripper);
            // 处理子表单
            if (type.equals(TYPE_SUBFORM)) {
                JSONArray items = widget.getJSONArray("items");
                for (int j = 0; j < items.size(); j++) {
                    JSONObject item = items.getJSONObject(j);
                    String itemName = item.getString("name");
                    String itemLabel = item.getString("label");
                    String itemType = item.getString("type");
                    IasDataGripper iasDataGripper_item = new IasDataGripper();
                    iasDataGripper_item.setParentName(name);
                    iasDataGripper_item.setTargetName("");
                    iasDataGripper_item.setWidgetsName(itemName);
                    iasDataGripper_item.setWidgetsLabel(itemLabel);
                    iasDataGripper_item.setWidgetsType(itemType);
                    entities.add(iasDataGripper_item);
                }
            }
        }
        iasDataGripperPlusService.saveBatch(entities);
    }

    @Override
    public void iasSyncProcess(String configCode) throws InterruptedException {
        log.info("开始执行数据同步操作");
        if (StringUtils.isEmpty(configCode)) {
            return;
        }
        QueryWrapper<IasDataConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("config_type", 2);
        queryWrapper.eq("config_code", configCode);
        List<IasDataConfig> configList = iasDataConfigService.list(queryWrapper);
        if (configList.isEmpty()) {
            return;
        }
        IasDataConfig iasDataConfig = configList.get(0);
        QueryWrapper<BacFieldConfig> queryBacConfig = new QueryWrapper<>();
        queryBacConfig.eq("config_code", iasDataConfig.getConfigCode());
        List<BacFieldConfig> bacFieldConfigList = bacfieldconfigService.list(queryBacConfig);
        Map<String, List<BacFieldConfig>> groupBacConfig = groupConfigByField(bacFieldConfigList);
        getSourceJson("", iasDataConfig, groupBacConfig);
        log.info("数据同步执行完毕");
    }

    private void getSourceJson(String data_id, IasDataConfig iasDataConfig, Map<String, List<BacFieldConfig>> groupBacConfig) throws InterruptedException {
        QueryWrapper<IasDataGripper> gripperQueryWrapper = new QueryWrapper<>();
        gripperQueryWrapper.eq("config_code", iasDataConfig.getConfigCode());
        List<IasDataGripper> grippers = iasDataGripperPlusService.list(gripperQueryWrapper);
        List<IasDataGripperGroup> gripperGroups = getIasDataGripperGroups(grippers);
        JdyDBCClient jdyDBCClient = new JdyDBCClient();
        JdyDBCListRequest sourceRequest = new JdyDBCListRequest();
        sourceRequest.setApp_id(iasDataConfig.getTriggerAppId());
        sourceRequest.setEntry_id(iasDataConfig.getTriggerEntryId());
        sourceRequest.setLimit(100);
        if (StringUtils.isNotBlank(data_id)) {
            sourceRequest.setData_id(data_id);
        }
        sourceRequest.setOperate_type(LIST);
        JSONObject listResult = jdyDBCClient.dbcCall(sourceRequest);
        JSONArray sourceDataList = listResult.getJSONArray(DATA);
        for (int x = 0; x < sourceDataList.size(); x++) {
            JSONObject sourceData = sourceDataList.getJSONObject(x);
            Iterator<String> keys = sourceData.keySet().iterator();
            while (keys.hasNext()) {
                String key = keys.next();
                if (groupBacConfig.containsKey(key)) {
                    List<BacFieldConfig> configs = groupBacConfig.get(key);
                    for (BacFieldConfig config : configs) {
                        JdyDBCRequest request = new JdyDBCRequest();
                        request.setApp_id(config.getSourceAppId());
                        request.setEntry_id(config.getSourceEntryId());
                        String url = String.format("%s%s%s", URL_PREFIX, VERSION, URL_WIDGET_SUFFIX);
                        JSONObject jsonObject = jdyDBCClient.dbcCall(JSONObject.parseObject(JSON.toJSONString(request)), url);
                        JSONArray widgets = jsonObject.getJSONArray("widgets");
                        boolean needAddFlag = false;
                        for (int i = 0; i < widgets.size(); i++) {
                            JSONObject widget = widgets.getJSONObject(i);
                            String name = widget.getString("name");
                            String type = widget.getString("type");
                            if (name.equals(key)) {
                                if (type.equals(TYPE_NUMBER)) {
                                    needAddFlag = true;
                                }
                                break;
                            }
                        }
                        JdyDBCListRequest s2 = new JdyDBCListRequest();
                        s2.setApp_id(config.getSourceAppId());
                        s2.setEntry_id(config.getSourceAppId());
                        s2.setOperate_type(LIST);
                        s2.setFilter(Filter.builder().cond(
                                        Collections.singletonList(Cond.builder()
                                                .field(config.getSourceValueWidgetId())
                                                .method("eq")
                                                .value(sourceData.getString(config.getTriggerWidget())).build()))
                                .build());
                        JSONObject r = jdyDBCClient.dbcCall(s2);
                        JSONArray d = r.getJSONArray(DATA);

                        if (needAddFlag) {
                            int sum = 0;
                            for (int y = 0; y < d.size(); y++) {
                                sum = sum + d.getJSONObject(y).getInteger(config.getSourceWidgetId());
                            }
                            sourceData.put(key, sum);
                        } else {
                            if (!d.isEmpty()) {
                                sourceData.put(key, d.getJSONObject(0).getString(config.getSourceWidgetId()));
                            }
                        }
                    }
                }
            }
            if (groupBacConfig.containsKey(null)) {
                List<BacFieldConfig> bacConfigs = groupBacConfig.get(null);
                for (BacFieldConfig bacConfig : bacConfigs) {
                    iasCustomizeService.execute(sourceData, bacConfig.getCustomizeCode());
                }
            }
            JdyDBCCreateRequest targetRequest = new JdyDBCCreateRequest();
            targetRequest.setApp_id(iasDataConfig.getTargetAppId());
            targetRequest.setEntry_id(iasDataConfig.getTargetEntryId());
            targetRequest.setOperate_type(CREATE);
            JSONObject targetJson = new JSONObject();
            JSONObject setValue = new JSONObject();
            setValue.put("value", "是");
            targetJson.put("_widget_1679395635154", setValue);
            for (IasDataGripperGroup processConfig : gripperGroups) {
                processDataGrip(sourceData, targetJson, processConfig);
            }
            targetRequest.setData(targetJson);
            jdyDBCClient.dbcCall(targetRequest);
            Thread.sleep(500);
        }
        if (sourceDataList.size() == 100) {
            JSONObject lastOne = sourceDataList.getJSONObject(100 - 1);
            String jdyId = lastOne.getString("_id");
            getSourceJson(jdyId, iasDataConfig, groupBacConfig);
        }
    }

    private Map<String, List<BacFieldConfig>> groupConfigByField(List<BacFieldConfig> bacFieldConfigList) {
        Map<String, List<BacFieldConfig>> result = new HashMap<>();
        bacFieldConfigList.forEach(bacFieldConfig -> {
            List<BacFieldConfig> configs = new ArrayList<>();
            if (result.containsKey(bacFieldConfig.getTargetWidget())) {
                configs.addAll(result.get(bacFieldConfig.getTargetWidget()));
            }
            configs.add(bacFieldConfig);
            result.put(bacFieldConfig.getTargetWidget(), configs);
        });
        return result;
    }

    public void changeMsg(String data_id) throws InterruptedException {
        JdyDBCClient jdyDBCClient = new JdyDBCClient();
        List<IasDataGripper> grippers = iasDataGripperPlusService.list();
        List<IasDataGripperGroup> gripperGroups = getIasDataGripperGroups(grippers);
        JdyDBCListRequest sourceRequest = new JdyDBCListRequest();
        sourceRequest.setApp_id("5fd066e4684fdb0006631413");
        sourceRequest.setEntry_id("6019f9ef29728a00075451ec");
        sourceRequest.setLimit(100);
        if (StringUtils.isNotBlank(data_id)) {
            sourceRequest.setData_id(data_id);
        }
        sourceRequest.setOperate_type(LIST);
        JSONArray sourceJsonList = jdyDBCClient.dbcCall(sourceRequest).getJSONArray("data");


        for (int i = 0; i < sourceJsonList.size(); i++) {
            JSONObject sourceJson = sourceJsonList.getJSONObject(i);
            JdyDBCCreateRequest targetRequest = new JdyDBCCreateRequest();
            targetRequest.setApp_id("5fd066e4684fdb0006631413");
            targetRequest.setEntry_id("6396c96929a5e10008e8ad81");
            targetRequest.setOperate_type(CREATE);
            JSONObject targetJson = new JSONObject();
            JSONObject setValue = new JSONObject();
            setValue.put("value", "是");
            targetJson.put("_widget_1679395635154", setValue);
            for (IasDataGripperGroup processConfig : gripperGroups) {
                processDataGrip(sourceJson, targetJson, processConfig);
            }
            targetRequest.setData(targetJson);
            jdyDBCClient.dbcCall(targetRequest);
            Thread.sleep(500);
        }
        if (sourceJsonList.size() == 100) {
            JSONObject lastOne = sourceJsonList.getJSONObject(100 - 1);
            String jdyId = lastOne.getString("_id");
            changeMsg(jdyId);
        }
    }

    public void iasCallBackProcess(JSONObject callBackObj) throws InterruptedException {
        JdyDBCClient jdyDBCClient = new JdyDBCClient();
        if (callBackObj.containsKey(OP)) {
            String option = callBackObj.getString(OP);
            if (callBackObj.containsKey(DATA)) {
                JSONObject callBackData = callBackObj.getJSONObject(DATA);
                if (callBackData.containsKey(DATA_APP_ID) && callBackData.containsKey(DATA_ENTRY_ID)) {
                    String appId = callBackData.getString(DATA_APP_ID);
                    String entryId = callBackData.getString(DATA_ENTRY_ID);
                    QueryWrapper<IasDataConfig> queryConfig = new QueryWrapper<>();
                    queryConfig.eq("trigger_app_id", appId).eq("trigger_entry_id", entryId)
                            .like("trigger_option", option);
                    List<IasDataConfig> configs = iasDataConfigService.list(queryConfig);
                    for (IasDataConfig config : configs) {
                        QueryWrapper<IasDataConfigAttached> queryAttached = new QueryWrapper<>();
                        queryAttached.eq("config_code", config.getConfigCode());
                        List<IasDataConfigAttached> configAttachedList = iasDataConfigAttachedService.list(queryAttached);
                        if (checkConfigAttached(callBackObj, configAttachedList)) {
                            String _id = callBackData.getString("_id");
                            if (StrUtil.isNotBlank(_id)) {
                                AfterSaleDispatchRequest request = new AfterSaleDispatchRequest();
                                request.setApp_id("600549610e809f00070f2c48");
                                request.setEntry_id("60348ef975c0b500075b7d0a");
                                request.setData_id(_id);
                                request.setUrlName(ONE_QUERY_URL);
                                AfterSaleDispatchResponse response = jdyClient.jdyCallV5(request);
                                if (null != response && null != response.getData()) {
                                    AfterSaleDispatchResponse.AfterSaleDispatchData responseData = response.getData();
                                    if (StrUtil.equalsAnyIgnoreCase(responseData.get_widget_1692595763159(), "是")) {
                                        log.info("该数据已创建售后服务完工报告表单");
                                        continue;
                                    }
                                }
                            }
                            JdyDBCCreateRequest targetRequest = new JdyDBCCreateRequest();
                            targetRequest.setApp_id(config.getTargetAppId());
                            targetRequest.setEntry_id(config.getTargetEntryId());
                            targetRequest.setOperate_type(CREATE);
                            JSONObject targetJson = new JSONObject();
                            QueryWrapper<IasDataGripper> queryGripper = new QueryWrapper<>();
                            queryGripper.eq("config_code", config.getConfigCode());
                            QueryWrapper<BacFieldConfig> queryBacConfig = new QueryWrapper<>();
                            queryBacConfig.eq("config_code", config.getConfigCode());
                            List<BacFieldConfig> bacFieldConfigList = bacfieldconfigService.list(queryBacConfig);
                            for (BacFieldConfig bacFieldConfig : bacFieldConfigList) {
                                iasCustomizeService.execute(callBackData, bacFieldConfig.getCustomizeCode());
                            }
                            List<IasDataGripper> grippers = iasDataGripperPlusService.list(queryGripper);
                            List<IasDataGripperGroup> gripperGroups = getIasDataGripperGroups(grippers);
                            for (IasDataGripperGroup processConfig : gripperGroups) {
                                processDataGrip(callBackData, targetJson, processConfig);
                            }

                            QueryWrapper<IasDataConfigOther> queryConfigOther = new QueryWrapper<>();
                            queryConfigOther.eq("config_code", config.getConfigCode());
                            queryConfigOther.eq("is_target_table", true);
                            List<IasDataConfigOther> iasDataConfigOthers = iasDataConfigOtherService.list(queryConfigOther);
                            for (IasDataConfigOther iasDataConfigOther : iasDataConfigOthers) {
                                if (iasDataConfigOther.getNeedStartFlow()) {
                                    targetRequest.setIs_start_workflow(true);
                                } else {
                                    JSONObject setValue = new JSONObject();
                                    setValue.put("value", iasDataConfigOther.getValue());
                                    targetJson.put(iasDataConfigOther.getWidgetsId(), setValue);
                                }

                            }
                            targetRequest.setData(targetJson);
                            jdyDBCClient.dbcCall(targetRequest);
                            Thread.sleep(500);
                            //回写售后派单表单
                            if (StrUtil.isNotBlank(_id)) {
                                AfterSaleDispatchUpdateRequest updateRequest = new AfterSaleDispatchUpdateRequest();
                                updateRequest.setApp_id("600549610e809f00070f2c48");
                                updateRequest.setEntry_id("60348ef975c0b500075b7d0a");
                                updateRequest.setUrlName(ONE_UPDATE_URL);
                                updateRequest.setData_id(_id);
                                AfterSaleDispatchUpdateRequest.UpdateData updateData = new AfterSaleDispatchUpdateRequest.UpdateData();
                                updateData.set_widget_1692595763159(ValueString.builder().value("是").build());
                                updateRequest.setData(updateData);
                                log.info("回写售后派单表单:[{}]", com.alibaba.fastjson2.JSON.toJSONString(updateRequest));
                                AfterSaleDispatchResponse updateResponse = jdyClient.jdyCallV5(updateRequest);
                                log.info("回写售后派单表单返回结果:[{}]", com.alibaba.fastjson2.JSON.toJSONString(updateResponse));
                            }
                            Thread.sleep(500);
                            QueryWrapper<IasDataConfigOther> queryConfigOther_2 = new QueryWrapper<>();
                            queryConfigOther.eq("config_code", config.getConfigCode());
                            queryConfigOther.eq("is_target_table", false);
                            List<IasDataConfigOther> iasDataConfigOthers_2 = iasDataConfigOtherService.list(queryConfigOther_2);
                            if (!iasDataConfigOthers_2.isEmpty()) {
                                JdyDBCCreateRequest updateRequest = new JdyDBCCreateRequest();
                                updateRequest.setApp_id(appId);
                                updateRequest.setEntry_id(entryId);
                                updateRequest.setOperate_type(UPDATE);
                                updateRequest.setData_id(callBackData.getString(ID));
                                JSONObject updateJson = new JSONObject();
                                for (IasDataConfigOther iasDataConfigOther : iasDataConfigOthers_2) {
                                    JSONObject setValue = new JSONObject();
                                    setValue.put("value", iasDataConfigOther.getValue());
                                    updateJson.put(iasDataConfigOther.getWidgetsId(), setValue);
                                }
                                updateRequest.setData(updateJson);
                                jdyDBCClient.dbcCall(updateRequest);
                                Thread.sleep(500);
                            }
                        }
                    }
                }
            }
        }
    }

    private List<IasDataGripperGroup> getIasDataGripperGroups(List<IasDataGripper> grippers) {
        List<IasDataGripperGroup> gripperGroups = new ArrayList<>();
        for (IasDataGripper iasDataGripper : grippers) {
            if (iasDataGripper.getParentName().equals("0")) {
                IasDataGripperGroup iasDataGripperGroup = new IasDataGripperGroup();
                BeanUtils.copyProperties(iasDataGripper, iasDataGripperGroup);
                if (iasDataGripper.getWidgetsType().equals(TYPE_SUBFORM)) {
                    List<IasDataGripperGroup> children = new ArrayList<>();
                    for (IasDataGripper itemGripper : grippers) {
                        if (itemGripper.getParentName().equals(iasDataGripper.getWidgetsName())) {
                            IasDataGripperGroup child = new IasDataGripperGroup();
                            BeanUtils.copyProperties(itemGripper, child);
                            children.add(child);
                        }
                    }
                    iasDataGripperGroup.setChildren(children);
                }
                gripperGroups.add(iasDataGripperGroup);
            }
        }
        return gripperGroups;
    }

    private Boolean checkConfigAttached(JSONObject callBackObj, List<IasDataConfigAttached> configAttachedList) {
        boolean flag = true;
        for (IasDataConfigAttached attached : configAttachedList) {
            if (attached.getTriggerWidgetsId().equals(DATA_FLOW_STATE)) {
                flag = attached.getTriggerTargetValue().equals(callBackObj.getString(DATA_FLOW_STATE));
            } else {
                JSONObject callBackData = callBackObj.getJSONObject(DATA);
                flag = attached.getTriggerTargetValue().equals(callBackData.getString(attached.getTriggerWidgetsId()));
            }
        }
        return flag;
    }

    public void processDataGrip(JSONObject sourceJson, JSONObject targetJson,
                                IasDataGripperGroup processConfig) {
        String sourceWidget = processConfig.getWidgetsName();
        String targetWidget = processConfig.getTargetName();
        switch (processConfig.getWidgetsType()) {
            case TYPE_SUBFORM:
                if (sourceJson.containsKey(sourceWidget)) {
                    JSONArray sourceSubform = sourceJson.getJSONArray(sourceWidget);
                    JSONArray setValue = new JSONArray();
                    for (int i = 0; i < sourceSubform.size(); i++) {
                        JSONObject sourceSubformItem = sourceSubform.getJSONObject(i);
                        List<IasDataGripperGroup> itemConfigList = processConfig.getChildren();
                        JSONObject setValueItem = new JSONObject();
                        for (IasDataGripperGroup itemConfig : itemConfigList) {
                            processDataGrip(sourceSubformItem, setValueItem, itemConfig);
                        }
                        setValue.add(setValueItem);
                    }
                    JSONObject subValue = new JSONObject();
                    subValue.put("value", setValue);
                    targetJson.put(targetWidget, subValue);
                }
                break;
            // 按一般的字符串处理
            case TYPE_TEXT:
            case TYPE_TEXT_AREA:
            case TYPE_DATETIME:
            case TYPE_COMBO:
            case TYPE_RADIO_GROUP:
                String value = "";
                if (sourceJson.containsKey(sourceWidget)) {
                    value = sourceJson.getString(sourceWidget);
                }
                JSONObject textValue = new JSONObject();
                textValue.put("value", value);
                targetJson.put(targetWidget, textValue);
                break;
            case TYPE_NUMBER:
                Integer number = 0;
                if (sourceJson.containsKey(sourceWidget)) {
                    number = sourceJson.getInteger(sourceWidget);
                }
                JSONObject numberValue = new JSONObject();
                numberValue.put("value", number);
                targetJson.put(targetWidget, numberValue);
                break;
            case TYPE_USER:
                if (sourceJson.containsKey(sourceWidget)) {
                    JSONObject userValue = new JSONObject();
                    JSONObject user = sourceJson.getJSONObject(sourceWidget);
                    if (user != null && user.containsKey("username")) {
                        userValue.put("value", user.getString("username"));
                        targetJson.put(targetWidget, userValue);
                    }
                }
                break;
            case TYPE_USER_GROUP:
                if (sourceJson.containsKey(sourceWidget)) {
                    JSONArray jsonArray = sourceJson.getJSONArray(sourceWidget);
                    List<String> userNames = new ArrayList<>();
                    for (int i = 0; i < jsonArray.size(); i++) {
                        if (jsonArray.getJSONObject(i) != null && jsonArray.getJSONObject(i).containsKey("username")) {
                            userNames.add(jsonArray.getJSONObject(i).getString("username"));
                        }
                    }
                    String[] userGroups = userNames.toArray(new String[]{});
                    JSONObject userGroupValue = new JSONObject();
                    userGroupValue.put("value", userGroups);
                    targetJson.put(targetWidget, userGroupValue);
                }
                break;
            case TYPE_COMBO_CHECK:
                if (sourceJson.containsKey(sourceWidget)) {
                    JSONArray jsonArray = sourceJson.getJSONArray(sourceWidget);
                    List<String> checkStringList = new ArrayList<>();
                    for (int i = 0; i < jsonArray.size(); i++) {
                        checkStringList.add(jsonArray.getString(i));
                    }
                    String[] checkGroups = checkStringList.toArray(new String[]{});
                    JSONObject checkGroupValue = new JSONObject();
                    checkGroupValue.put("value", checkGroups);
                    targetJson.put(targetWidget, checkGroupValue);
                }
                break;
            default:
                break;
        }
    }

    public void syncErrField(String data_id) throws InterruptedException {
        JdyDBCClient jdyDBCClient = new JdyDBCClient();
        JdyDBCListRequest jdyDBCListRequest = new JdyDBCListRequest();
        jdyDBCListRequest.setApp_id("5fd066e4684fdb0006631413");
        jdyDBCListRequest.setEntry_id("6396c96929a5e10008e8ad81");
        //jdyDBCListRequest.setFilter(Filter.builder().cond(Collections.singletonList(Cond.builder().field("_widget_1612315117670").method("eq").value("DJ22055").build())).build());
        jdyDBCListRequest.setLimit(100);
        if (StringUtils.isNotBlank(data_id)) {
            jdyDBCListRequest.setData_id(data_id);
        }
        jdyDBCListRequest.setOperate_type(LIST);
        JSONObject listResult = jdyDBCClient.dbcCall(jdyDBCListRequest);
        Thread.sleep(500);
        JSONArray sourceDataList = listResult.getJSONArray(DATA);
        QueryWrapper<IasDataGripper> gripperQueryWrapper = new QueryWrapper<>();
        gripperQueryWrapper.eq("config_code", "sync-pm4");
        List<IasDataGripper> grippers = iasDataGripperPlusService.list(gripperQueryWrapper);
        List<IasDataGripperGroup> gripperGroups = getIasDataGripperGroups(grippers);
        for (int x = 0; x < sourceDataList.size(); x++) {
            JSONObject sourceJson = sourceDataList.getJSONObject(x);
            processPlugIn(sourceJson, "_widget_1612408604526", "_widget_1612859893319", "_widget_1619335167277", 1000);
            processPlugIn(sourceJson, "_widget_1628759979168", "_widget_1628759979170", "_widget_1628759979173", 1000);
            processPlugIn(sourceJson, "_widget_1628759979478", "_widget_1628759979480", "_widget_1628759979483", 1000);
            processPlugIn(sourceJson, "_widget_1612430652648", "_widget_1612859892668", "_widget_1628768739123", 125);

            JdyDBCCreateRequest targetRequest = new JdyDBCCreateRequest();
            targetRequest.setApp_id("5fd066e4684fdb0006631413");
            targetRequest.setEntry_id("6396c96929a5e10008e8ad81");
            targetRequest.setOperate_type(UPDATE);
            targetRequest.setData_id(sourceJson.getString("_id"));
            JSONObject targetJson = new JSONObject();
            for (IasDataGripperGroup processConfig : gripperGroups) {
                processDataGrip(sourceJson, targetJson, processConfig);
            }
            targetRequest.setData(targetJson);
            jdyDBCClient.dbcCall(targetRequest);
            Thread.sleep(500);
        }

        if (sourceDataList.size() == 100) {
            JSONObject lastOne = sourceDataList.getJSONObject(100 - 1);
            String jdyId = lastOne.getString("_id");
            syncErrField(jdyId);
        }
    }

    private void processPlugIn(JSONObject sourceJson, String targetArrayId, String targetField, String sourceField, double coefficient) {
        JSONArray jsonArray_1 = sourceJson.getJSONArray(targetArrayId);
        JSONObject item_1 = jsonArray_1.getJSONObject(0);
        // 预测（引用）
        item_1.put(targetField, item_1.getIntValue(sourceField) * coefficient);
        JSONArray newArray = new JSONArray();
        newArray.add(item_1);
        sourceJson.remove(targetArrayId);
        sourceJson.put(targetArrayId, newArray);
    }

}

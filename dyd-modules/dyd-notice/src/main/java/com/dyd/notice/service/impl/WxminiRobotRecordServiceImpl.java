package com.dyd.notice.service.impl;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.dyd.common.core.utils.DateUtils;
import com.google.gson.Gson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dyd.notice.mapper.WxminiRobotRecordMapper;
import com.dyd.notice.domain.WxminiRobotRecord;
import com.dyd.notice.service.IWxminiRobotRecordService;

/**
 * 小程序聊天记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-23
 */
@Service
public class WxminiRobotRecordServiceImpl implements IWxminiRobotRecordService
{
    @Autowired
    private WxminiRobotRecordMapper wxminiRobotRecordMapper;

    /**
     * 查询小程序聊天记录
     *
     * @param id 小程序聊天记录主键
     * @return 小程序聊天记录
     */
    @Override
    public WxminiRobotRecord selectWxminiRobotRecordById(Long id)
    {
        return wxminiRobotRecordMapper.selectWxminiRobotRecordById(id);
    }

    /**
     * 查询小程序聊天记录列表
     *
     * @param wxminiRobotRecord 小程序聊天记录
     * @return 小程序聊天记录
     */
    @Override
    public List<WxminiRobotRecord> selectWxminiRobotRecordList(WxminiRobotRecord wxminiRobotRecord)
    {
        List<WxminiRobotRecord> list = wxminiRobotRecordMapper.selectWxminiRobotRecordList(wxminiRobotRecord);
        if(CollectionUtils.isEmpty(list) && StrUtil.isNotBlank(wxminiRobotRecord.getUserKey()) && 0L == wxminiRobotRecord.getId()){
            WxminiRobotRecord wxminiRobotRecord1 = new WxminiRobotRecord();
            wxminiRobotRecord1.setUserKey(wxminiRobotRecord.getUserKey());
            wxminiRobotRecord1.setAnswer("您好，我是岱鼎AI助手，我可以回答您的问题，帮助您解决问题，提供信息，进行对话交流等。如果您有任何需要，欢迎向我提问。");
            wxminiRobotRecord1.setAnswerType("0");
            wxminiRobotRecord1.setCreateTime(DateUtils.getNowDate());
            wxminiRobotRecordMapper.insertWxminiRobotRecord(wxminiRobotRecord1);
            list = new ArrayList<>();
            list.add(wxminiRobotRecord1);
        }
        /*for(WxminiRobotRecord wxminiRobotRecord1 :list){
            if("1".equals(wxminiRobotRecord1.getAnswerType())){
                Gson gson = new Gson();
                Type listType = new TypeToken<List<String>>() {}.getType();
                List<String> AnswerList = gson.fromJson(wxminiRobotRecord1.getAnswer(), listType);
                wxminiRobotRecord1.setQuestionList(AnswerList);
            }
        }*/
        list = list.stream().sorted(Comparator.comparingLong(WxminiRobotRecord::getId)).collect(Collectors.toList());
        return list;
    }

    /**
     * 新增小程序聊天记录
     *
     * @param wxminiRobotRecord 小程序聊天记录
     * @return 结果
     */
    @Override
    public int insertWxminiRobotRecord(WxminiRobotRecord wxminiRobotRecord)
    {
        wxminiRobotRecord.setCreateTime(DateUtils.getNowDate());
        return wxminiRobotRecordMapper.insertWxminiRobotRecord(wxminiRobotRecord);
    }

    /**
     * 修改小程序聊天记录
     *
     * @param wxminiRobotRecord 小程序聊天记录
     * @return 结果
     */
    @Override
    public int updateWxminiRobotRecord(WxminiRobotRecord wxminiRobotRecord)
    {
        return wxminiRobotRecordMapper.updateWxminiRobotRecord(wxminiRobotRecord);
    }

    /**
     * 批量删除小程序聊天记录
     *
     * @param ids 需要删除的小程序聊天记录主键
     * @return 结果
     */
    @Override
    public int deleteWxminiRobotRecordByIds(Long[] ids)
    {
        return wxminiRobotRecordMapper.deleteWxminiRobotRecordByIds(ids);
    }

    /**
     * 删除小程序聊天记录信息
     *
     * @param id 小程序聊天记录主键
     * @return 结果
     */
    @Override
    public int deleteWxminiRobotRecordById(Long id)
    {
        return wxminiRobotRecordMapper.deleteWxminiRobotRecordById(id);
    }
}

package com.dyd.notice.mapper;

import java.util.List;
import com.dyd.notice.domain.RobotSendingMessage;

/**
 * 发送群消息Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-03
 */
public interface RobotSendingMessageMapper 
{
    /**
     * 查询发送群消息
     * 
     * @param id 发送群消息主键
     * @return 发送群消息
     */
    public RobotSendingMessage selectRobotSendingMessageById(Long id);

    /**
     * 查询发送群消息列表
     * 
     * @param robotSendingMessage 发送群消息
     * @return 发送群消息集合
     */
    public List<RobotSendingMessage> selectRobotSendingMessageList(RobotSendingMessage robotSendingMessage);

    /**
     * 新增发送群消息
     * 
     * @param robotSendingMessage 发送群消息
     * @return 结果
     */
    public int insertRobotSendingMessage(RobotSendingMessage robotSendingMessage);

    /**
     * 修改发送群消息
     * 
     * @param robotSendingMessage 发送群消息
     * @return 结果
     */
    public int updateRobotSendingMessage(RobotSendingMessage robotSendingMessage);

    /**
     * 删除发送群消息
     * 
     * @param id 发送群消息主键
     * @return 结果
     */
    public int deleteRobotSendingMessageById(Long id);

    /**
     * 批量删除发送群消息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRobotSendingMessageByIds(Long[] ids);
}

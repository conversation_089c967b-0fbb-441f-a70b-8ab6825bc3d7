package com.dyd.notice.controller;

import com.dyd.common.core.utils.poi.ExcelUtil;
import com.dyd.common.core.web.controller.BaseController;
import com.dyd.common.core.web.domain.AjaxResult;
import com.dyd.common.core.web.page.TableDataInfo;
import com.dyd.notice.domain.NoticeMaterialFields;
import com.dyd.notice.domain.NoticeProductItem;
import com.dyd.notice.service.INoticeProductItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 产品物料信息Controller
 *
 * <AUTHOR>
 * @date 2023-03-16
 */
@RestController
@RequestMapping("/item")
public class NoticeProductItemController extends BaseController {
    @Autowired
    private INoticeProductItemService noticeProductItemService;

    @GetMapping("/title/list/{productCode}")
    public List<NoticeMaterialFields> getTitleList(@PathVariable String productCode){
        return noticeProductItemService.getTitleList(productCode);
    }

    /**
     * 查询产品物料信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(NoticeProductItem noticeProductItem) {
        startPage();
        List<NoticeProductItem> list = noticeProductItemService.selectNoticeProductItemList(noticeProductItem);
        return getDataTable(list);
    }

    /**
     * 导出产品物料信息列表
     */
    @PostMapping("/export")
    public void export(HttpServletResponse response, NoticeProductItem noticeProductItem) {
        List<NoticeProductItem> list = noticeProductItemService.selectNoticeProductItemList(noticeProductItem);
        ExcelUtil<NoticeProductItem> util = new ExcelUtil<NoticeProductItem>(NoticeProductItem.class);
        util.exportExcel(response, list, "产品物料信息数据");
    }

    /**
     * 获取产品物料信息详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(noticeProductItemService.selectNoticeProductItemById(id));
    }

    /**
     * 新增产品物料信息
     */
    @PostMapping
    public AjaxResult add(@RequestBody NoticeProductItem noticeProductItem) {
        return toAjax(noticeProductItemService.insertNoticeProductItem(noticeProductItem));
    }

    /**
     * 修改产品物料信息
     */
    @PutMapping
    public AjaxResult edit(@RequestBody NoticeProductItem noticeProductItem) {
        return toAjax(noticeProductItemService.updateNoticeProductItem(noticeProductItem));
    }

    /**
     * 删除产品物料信息
     */
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(noticeProductItemService.deleteNoticeProductItemByIds(ids));
    }
}

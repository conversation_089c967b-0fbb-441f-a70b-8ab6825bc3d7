package com.dyd.notice.mapper;

import java.util.List;
import com.dyd.notice.domain.NoticeProductBasicBox;
import com.dyd.notice.domain.NoticeProductBasicBoxBO;
import com.dyd.notice.domain.REST.NoticeProductBoxVo;
import org.apache.ibatis.annotations.Param;

/**
 * 产品富文本框关联Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-03-22
 */
public interface NoticeProductBasicBoxMapper 
{
    /**
     * 查询产品富文本框关联
     * 
     * @param id 产品富文本框关联主键
     * @return 产品富文本框关联
     */
    public NoticeProductBasicBoxBO selectNoticeProductBasicBoxById(Long id);

    /**
     * 查询产品富文本框关联列表
     * 
     * @param noticeProductBasicBox 产品富文本框关联
     * @return 产品富文本框关联集合
     */
    public List<NoticeProductBasicBox> selectNoticeProductBasicBoxList(NoticeProductBasicBox noticeProductBasicBox);
    /**
     * 查询产品富文本框关联列表
     *
     * @param noticeProductBasicBox 产品富文本框关联
     * @return 产品富文本框关联集合
     */
    public List<NoticeProductBasicBoxBO> selectNoticeProductBasicBoxBOList(NoticeProductBasicBox noticeProductBasicBox);

    /**
     * 新增产品富文本框关联
     * 
     * @param noticeProductBasicBox 产品富文本框关联
     * @return 结果
     */
    public int insertNoticeProductBasicBox(NoticeProductBasicBox noticeProductBasicBox);

    /**
     * 修改产品富文本框关联
     * 
     * @param noticeProductBasicBox 产品富文本框关联
     * @return 结果
     */
    public int updateNoticeProductBasicBox(NoticeProductBasicBox noticeProductBasicBox);

    /**
     * 删除产品富文本框关联
     * 
     * @param id 产品富文本框关联主键
     * @return 结果
     */
    public int deleteNoticeProductBasicBoxById(Long id);

    /**
     * 批量删除产品富文本框关联
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteNoticeProductBasicBoxByIds(Long[] ids);

    public List<NoticeProductBoxVo> getBoxVoByProductCode(@Param("productCode")String productCode);

}

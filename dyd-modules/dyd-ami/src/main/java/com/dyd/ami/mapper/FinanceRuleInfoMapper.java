package com.dyd.ami.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dyd.ami.domain.finance.FinanceRuleInfo;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 财务-合同条款(FinanceRuleInfo)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-04-19 12:37:25
 */
public interface FinanceRuleInfoMapper extends BaseMapper<FinanceRuleInfo> {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    FinanceRuleInfo queryById(Integer id);

    /**
     * 通过合同条款查询多条数据
     *
     * @param name 合同条款
     * @return 实例对象
     */
    List<FinanceRuleInfo> queryByName(String name);

    /**
     * 查询指定行数据
     *
     * @param financeRuleInfo 查询条件
     * @param pageable        分页对象
     * @return 对象列表
     */
    List<FinanceRuleInfo> queryAllByLimit(FinanceRuleInfo financeRuleInfo, @Param("pageable") Pageable pageable);

    /**
     * 统计总行数
     *
     * @param financeRuleInfo 查询条件
     * @return 总行数
     */
    long count(FinanceRuleInfo financeRuleInfo);

    /**
     * 新增数据
     *
     * @param financeRuleInfo 实例对象
     * @return 影响行数
     */
    int insert(FinanceRuleInfo financeRuleInfo);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<FinanceRuleInfo> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<FinanceRuleInfo> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<FinanceRuleInfo> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<FinanceRuleInfo> entities);

    /**
     * 修改数据
     *
     * @param financeRuleInfo 实例对象
     * @return 影响行数
     */
    int update(FinanceRuleInfo financeRuleInfo);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

    /**
     * 通过简道云Id删除数据
     * @param ruleId
     * @return
     */
    int deleteByDataId(String ruleId);

}


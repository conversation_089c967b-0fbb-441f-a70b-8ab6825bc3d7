package com.dyd.ami.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.dyd.ami.service.CustomerSalesInvoiceService;
import com.dyd.jdy.JdyClient;
import com.dyd.jdy.bean.common.ValueString;
import com.dyd.jdy.bean.jdy.request.CustomerSalesInvoiceModifyRequest;
import com.dyd.jdy.bean.jdy.request.CustomerSalesInvoiceRequest;
import com.dyd.jdy.bean.jdy.request.CustomerSalesInvoiceUpdateRequest;
import com.dyd.jdy.bean.jdy.response.CustomerSalesInvoiceResponse;
import com.dyd.jdy.constant.JdyConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 客户销项发票
 */
@Slf4j
@Service
public class CustomerSalesInvoiceServiceImpl implements CustomerSalesInvoiceService {

    @Resource
    private JdyClient jdyClient;

    /**
     * 客户销项发票特殊要求登记清单 数据推送
     *
     * @param request
     */
    @Override
    public void pushData(CustomerSalesInvoiceModifyRequest request) {
        log.info("客户销项发票特殊要求登记清单数据推送:[{}]", JSON.toJSONString(request));
        String seq = request.getData().get_widget_1671437304635();

        CustomerSalesInvoiceRequest customerSalesInvoiceRequest = new CustomerSalesInvoiceRequest();
        customerSalesInvoiceRequest.setApp_id("5fbcfb9d0bdae3000651709a");
        customerSalesInvoiceRequest.setEntry_id("63a01bf61447460008f297b6");
        customerSalesInvoiceRequest.setUrlName(JdyConstants.MORE_QUERY_URL);
        customerSalesInvoiceRequest.setLimit(10);

        List<CustomerSalesInvoiceRequest.Field> fieldList = new ArrayList<>();
        CustomerSalesInvoiceRequest.Field field = new CustomerSalesInvoiceRequest.Field();
        field.setField("_widget_1671437304635");
        field.setValue(Arrays.asList(seq));
        field.setMethod("eq");
        fieldList.add(field);
        CustomerSalesInvoiceRequest.Filter filter = new CustomerSalesInvoiceRequest.Filter();
        filter.setCond(fieldList);
        filter.setRel("and");
        customerSalesInvoiceRequest.setFilter(filter);

        log.info("查询客户销项发票特殊要求登记清单 请求参数:[{}]", JSON.toJSONString(customerSalesInvoiceRequest));
        CustomerSalesInvoiceResponse customerSalesInvoiceResponse = jdyClient.jdyCallV5(customerSalesInvoiceRequest);
        log.info("查询客户销项发票特殊要求登记清单 返回参数:[{}]", JSON.toJSONString(customerSalesInvoiceResponse));
        List<CustomerSalesInvoiceResponse.CustomerSalesInvoiceData> customerSalesInvoiceDataList = customerSalesInvoiceResponse.getData();
        if (CollectionUtil.isNotEmpty(customerSalesInvoiceDataList)) {
            CustomerSalesInvoiceResponse.CustomerSalesInvoiceData customerSalesInvoiceData = customerSalesInvoiceDataList.get(0);
            String id = customerSalesInvoiceData.get_id();
            CustomerSalesInvoiceUpdateRequest updateRequest = new CustomerSalesInvoiceUpdateRequest();
            updateRequest.setApp_id("5fbcfb9d0bdae3000651709a");
            updateRequest.setEntry_id("63a01bf61447460008f297b6");
            updateRequest.setUrlName(JdyConstants.ONE_UPDATE_URL);
            updateRequest.setData_id(id);

            CustomerSalesInvoiceUpdateRequest.CustomerSalesInvoiceUpdateData updateData = new CustomerSalesInvoiceUpdateRequest.CustomerSalesInvoiceUpdateData();
            updateData.set_widget_1671437304636(ValueString.builder().value(request.getData().get_widget_1671437304636()).build());
            updateData.set_widget_1671437304638(ValueString.builder().value(request.getData().get_widget_1671437304638()).build());
            updateData.set_widget_1671437304639(ValueString.builder().value(request.getData().get_widget_1671437304639()).build());
            updateData.set_widget_1671437304640(ValueString.builder().value(request.getData().get_widget_1671437304640()).build());
            updateData.set_widget_1671437304641(ValueString.builder().value(request.getData().get_widget_1671437304641()).build());
            updateData.set_widget_1671437304642(ValueString.builder().value(request.getData().get_widget_1671437304642()).build());
            updateData.set_widget_1671437304643(ValueString.builder().value(request.getData().get_widget_1671437304643()).build());
            updateData.set_widget_1671437304644(ValueString.builder().value(request.getData().get_widget_1671437304644()).build());
            updateData.set_widget_1671437304637(ValueString.builder().value(request.getData().get_widget_1671437304637()).build());
            updateData.set_widget_1671437304645(ValueString.builder().value(request.getData().get_widget_1671437304645()).build());
            updateData.set_widget_1671437304646(ValueString.builder().value(request.getData().get_widget_1671437304646()).build());
            updateRequest.setData(updateData);

            log.info("修改客户销项发票特殊要求登记清单 请求参数:[{}]", JSON.toJSONString(updateRequest));
            jdyClient.jdyCallV5(updateRequest);
            log.info("修改客户销项发票特殊要求登记清单成功");
        }
    }
}

package com.dyd.ami.mapper;

import com.dyd.ami.domain.AfterSalesReport;
import com.dyd.ami.domain.DailyLack;
import com.dyd.ami.domain.bi.BiBudget;
import com.dyd.ami.domain.bi.OdsMaterialPriceRollU9;
import com.dyd.ami.domain.bi.ProjectPerformanceResponse;
import com.dyd.common.datasource.annotation.Bidata;
import com.dyd.common.datasource.annotation.Bijava;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface BiMapper {
    @Bidata
    String getBiString();

    @Bidata
    int addMateria(OdsMaterialPriceRollU9 odsMaterialPriceRollU9);
    @Bidata
    int addMateriaBatch(List<OdsMaterialPriceRollU9> list);

    /**
     *查询项目部绩效数据
     * @param startDate
     * @param endDate
     * @param name
     * @return
     */
    @Bidata
    List<ProjectPerformanceResponse> selectProjectPerformance(@Param("startDate") String startDate,@Param("endDate") String endDate,@Param("name") String name);
    @Bijava
    List<DailyLack> getDailyLack(Map<String,String> map);

    int getClock(Map<String,String> map);
    @Bijava
    List<AfterSalesReport> afterSalesReport();

    Integer getUserDelFal(String userId);

    @Bidata
    List<BiBudget> selectBudget(@Param("projectCode") String projectCode);
}

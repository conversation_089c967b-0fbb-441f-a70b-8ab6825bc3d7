package com.dyd.ami.domain.IntelligentAppCascade;

import com.dyd.jdy.bean.JdyResponse;
import com.dyd.jdy.bean.common.ValueString;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: cb
 * @create: 2023-05-05 10:45
 * @Version
 **/

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class IntelligentAppCascadeResponse extends JdyResponse {
    private static final long serialVersionUID = -3472950085157814308L;

    /**
     * @des:应用id
     */
    private String app_id;

    /**
     * @des:entryId
     */
    private String entry_id;

    /**
     * @des:应用名称
     */
    private String name;


    /**
     * @des:所属公司名
     */
    private String label;


    /**
     * @des:应用信息
     */
    private List<FromDataApp> apps;

    /**
     * @des:表单信息
     */
    private List<FromDataForms> forms;


    /**
     * @des:数据总条数
     */
    private int size;

    /**
     * @des:页数
     */
    private int pageNum;


    /**
     * @des:每页数量
     */
    private int pageSize;


    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FromDataApp {

        /**
         * @des:应用id
         */
        private String app_id;

        /**
         * @des:应用名称
         */
        private String name;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FromDataForms {

        /**
         * @des:表单Id
         */
        private String entry_id;

        /**
         * @des:应用id
         */
        private String app_id;

        /**
         * @des:表单名称
         */
        private String name;
    }


}

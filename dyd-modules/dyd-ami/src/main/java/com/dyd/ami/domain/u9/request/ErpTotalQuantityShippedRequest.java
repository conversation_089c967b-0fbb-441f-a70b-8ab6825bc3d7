package com.dyd.ami.domain.u9.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 已发货数量请求参数
 */
@Data
public class ErpTotalQuantityShippedRequest {

    /**
     * 料号
     */
    @NotBlank(message = "料号不能为空")
    private String itemCode;

    /**
     * 开始时间
     */
    @NotBlank(message = "开始时间不能为空")
    private String startDate;

    /**
     * 结束时间
     */
    @NotBlank(message = "结束时间不能为空")
    private String endDate;
}

package com.dyd.ami.controller;

import com.dyd.ami.domain.salesmissionwall.SalesMissionPushRequest;
import com.dyd.ami.domain.salesmissionwall.SalesMissionRejectRequest;
import com.dyd.ami.domain.salesmissionwall.SalesMissionStatusRequest;
import com.dyd.ami.domain.salesmissionwall.SalesMissionStatusResponse;
import com.dyd.ami.service.SalesMissionWallService;
import com.dyd.common.core.domain.R;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 销售中心任务墙
 */
@RestController
@RequestMapping("/sales/mission/wall")
public class SalesMissionWallController {

    @Resource
    private SalesMissionWallService salesMissionWallService;

    /**
     * 获取任务状态
     *
     * @param salesMissionStatusRequest
     * @return
     */
    @PostMapping("/getTaskStatus")
    public R getTaskStatus(@RequestBody SalesMissionStatusRequest salesMissionStatusRequest) {
        return salesMissionWallService.getTaskStatus(salesMissionStatusRequest);
    }


    /**
     * 任务墙数据推送
     *
     * @param pushRequest
     */
    @PostMapping("/salesMissionPush")
    public void salesMissionPush(@RequestBody SalesMissionPushRequest pushRequest) {
        salesMissionWallService.salesMissionPush(pushRequest);
    }


    /**
     * 获取任务状态数据
     *
     * @param salesMissionStatusRequest
     * @return
     */
    @PostMapping("/getStatusData")
    public R<SalesMissionStatusResponse> getStatusData(@RequestBody SalesMissionStatusRequest salesMissionStatusRequest) {
        return salesMissionWallService.getStatusData(salesMissionStatusRequest);
    }

    /**
     * 任务墙数据推送(任务拒绝)
     *
     * @param rejectRequest
     */
    @PostMapping("/salesMissionRejectPush")
    public void salesMissionReject(@RequestBody SalesMissionRejectRequest rejectRequest) {
        salesMissionWallService.salesMissionReject(rejectRequest);
    }

}

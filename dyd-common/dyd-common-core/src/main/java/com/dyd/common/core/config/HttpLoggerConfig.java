package com.dyd.common.core.config;

import com.dyd.common.core.wrapper.ContentCachingRequestWrapper;
import com.dyd.common.core.wrapper.ResponseWrapper;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;

@Configuration
@ConditionalOnProperty(prefix = "request.log", name = "enabled", havingValue = "true")
public class HttpLoggerConfig extends OncePerRequestFilter implements Ordered {

    private static final Logger log = LoggerFactory.getLogger(HttpLoggerConfig.class);
    private AntPathMatcher pathMatcher = new AntPathMatcher();

    public HttpLoggerConfig() {
    }

    public static String getStreamAsString(InputStream stream, String charset) throws IOException {
        try (Reader reader = new InputStreamReader(stream, charset)) {
            StringBuilder response = new StringBuilder();
            char[] buff = new char[1024];
            int read;
            while ((read = reader.read(buff)) > 0) {
                response.append(buff, 0, read);
            }
            return response.toString();
        }
    }

    @Override
    public int getOrder() {
        return Integer.MAX_VALUE;
    }

    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        if (this.isIgnoreUri(request) || ServletFileUpload.isMultipartContent(request)) {
            filterChain.doFilter(request, response);
        } else {
            ResponseWrapper responseWrapper = new ResponseWrapper(response);
            ContentCachingRequestWrapper requestWrapper = new ContentCachingRequestWrapper(request);
            if ("GET".equals(request.getMethod())) {
                log.info("[HTTP]Start - {}--{}--:入参:{}", this.getClientIp(request), this.getRequestUri(request), request.getQueryString());
            } else {
                log.info("[HTTP]Start --{}--{}--:入参:{}", this.getClientIp(request), this.getRequestUri(request), requestWrapper.getBodyString());
            }
            filterChain.doFilter(requestWrapper, responseWrapper);
            if (responseWrapper.isTextContentType()) {
                String responseString = new String(responseWrapper.getDataStream());
                response.getOutputStream().write(responseString.getBytes());
                log.info("[HTTP]End --{}--{}--:出参:{}", this.getClientIp(request), this.getRequestUri(request), responseString);

            } else {
                responseWrapper.sendData();
                log.info("[HTTP]End --{}--{}--:下载文件类型:{}", this.getClientIp(request), this.getRequestUri(request), response.getContentType());

            }
        }
    }

    private HttpServletResponse setResponseTid(HttpServletResponse response) {
        return response;
    }

    private String getRequestUri(HttpServletRequest request) {
        String contextPath = request.getSession().getServletContext().getContextPath();
        return request.getRequestURI().replaceFirst(contextPath, "");
    }

    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }

        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }

        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }

        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }

        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }

        return ip;
    }

    private boolean isIgnoreUri(HttpServletRequest request) {
        return this.pathMatcher.match("/**/actuator/**", request.getRequestURI())
                || this.pathMatcher.match("/**/swagger-ui/**", request.getRequestURI())
                || this.pathMatcher.match("/**/swagger-resources/**", request.getRequestURI())
                || this.pathMatcher.match("/**/api-docs/**", request.getRequestURI())
                || this.pathMatcher.match("/**/callback/**", request.getRequestURI());

    }
}

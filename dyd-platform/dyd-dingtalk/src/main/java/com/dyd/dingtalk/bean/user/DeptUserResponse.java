package com.dyd.dingtalk.bean.user;

import com.dyd.dingtalk.bean.DingtalkResponse;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DeptUserResponse extends DingtalkResponse {

    /**
     * 返回码。
     */
    private Integer errcode;

    /**
     * 返回码描述
     */
    private String errmsg;

    private DeptUserResult result;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class DeptUserResult{

        /**
         * 是否还有更多的数据：
         *
         * true：有
         *
         * false：没有
         */
        private Boolean has_more;

        /**
         * 下一次分页的游标。
         */
        private Integer next_cursor;

        private List<DeptUserData> list;

        @JsonIgnoreProperties(ignoreUnknown = true)
        @Data
        public static class DeptUserData{

            /**
             * 用户的userId。
             */
            private String userid;

            /**
             * 用户在当前开发者企业账号范围内的唯一标识。
             */
            private String unionid;

            /**
             * 用户姓名。
             */
            private String name;

            /**
             * 头像地址。
             */
            private String avatar;

            /**
             * 手机号
             */
            private String mobile;

            /**
             * 办公地点
             */
            private String work_place;

            /**
             * 员工工号。
             */
            private String job_number;

            /**
             * 职位。
             */
            private String title;

            private String extension;

            /**
             * 员工邮箱。
             */
            private String email;

            /**
             * 员工的企业邮箱。
             */
            private String org_email;

            /**
             * 备注
             */
            private String remark;

            /**
             * 所属部门id列表。
             */
            private List<Long> dept_id_list;

            /**
             * 入职时间，Unix时间戳，单位毫秒。
             */
            private Long hired_date;

            /**
             * 是否激活了钉钉：
             *
             * true：已激活
             *
             * false：未激活
             */
            private Boolean active;

            /**
             * 是否为企业的管理员：
             *
             * true：是
             *
             * false：不是
             */
            private Boolean admin;

            /**
             * 是否为企业的老板：
             *
             * true：是
             *
             * false：不是
             */
            private Boolean boss;

            /**
             * 是否是部门的主管：
             *
             * true：是
             *
             * false：不是
             */
            private Boolean leader;
        }

    }
}

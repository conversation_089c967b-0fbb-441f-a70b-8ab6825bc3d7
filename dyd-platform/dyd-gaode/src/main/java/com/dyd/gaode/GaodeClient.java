package com.dyd.gaode;

import cn.hutool.core.exceptions.UtilException;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.dyd.gaode.bean.juli.JuliRequest;
import com.dyd.gaode.bean.juli.JuliResponse;
import com.dyd.gaode.constant.GaodeConstants;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;


/**
 * 高德请求
 * <AUTHOR>
 */
@Component
public class GaodeClient {
    private Logger logger = LoggerFactory.getLogger(GaodeClient.class);

    /**
     * 调用gaode
     */
    public String gaodeCallJuli(JuliRequest juliRequest) {
        try {
            juliRequest.setKey(GaodeConstants.KEY);
            //接口入参数据
            Gson gson = new Gson();
            String json = gson.toJson(juliRequest);
            //接口URL
            String url = GaodeConstants.JULIURL;
            //POST请求
            HttpRequest post = HttpRequest.get(url).timeout(GaodeConstants.TIMEOUT);
            post.header("Content-Type", "application/x-www-from-urlencoded");
            post.form("key", GaodeConstants.KEY);
            post.form("origin", juliRequest.getOrigin());
            post.form("destination", juliRequest.getDestination());
            HttpResponse execute = post.execute();
            //请求出参数据
            String ret = execute.body();
            ObjectMapper objectMapper = new ObjectMapper();
            JuliResponse juliResponse = objectMapper.readValue(ret, JuliResponse.class);
            //判断请求是否成功
            if ("0".equals(juliResponse.getStatus())) {
                throw new UtilException(juliResponse.getInfo());
            }
            return juliResponse.getRoute().getPaths().get(0).getDistance();
        } catch (Exception e) {
            logger.info(e.getMessage());
            throw new UtilException("服务不可用" + e.getMessage());
        }
    }

    public static void main(String[] args) {
        GaodeClient get = new GaodeClient();

        JuliRequest juliRequest = JuliRequest.builder()
                .origin("121.688,31.207")
                .destination("113.450,27.886")
                .build();

        String juli = get.gaodeCallJuli(juliRequest);
        System.out.println(juli);
    }
}

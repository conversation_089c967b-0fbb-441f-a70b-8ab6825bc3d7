package com.dyd.erp.bean.basic;
import com.dyd.erp.bean.ErpResponse;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 *u9 返回参数
 * <AUTHOR>
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ErpBasicResponse extends ErpResponse {


    /**
        * 列表数据
        */
       private List<ErpBasicResponse.D> d;


        @Data
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class D{

           /**
            * type子表
            */
           @JsonProperty(value = "__type")
           private String __type;
           /**
            * code
            */
           @JsonProperty(value = "Code")
           private  String Code;

           /**
            * 信息
            */
           @JsonProperty(value = "Msg")
           private  String Msg;

           /**
            *  名称
            */
           @JsonProperty(value = "Name")
           private String   Name;


        }



}

package com.dyd.jdy.bean.jdy.request;

import com.dyd.jdy.bean.V5.JdyRequestV5;
import com.dyd.jdy.bean.common.ValueList;
import com.dyd.jdy.bean.common.ValueString;
import com.dyd.jdy.bean.common.ValueT;
import com.dyd.jdy.bean.jdy.response.JdyRejectsHandlerFlowCreateResponse;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * @description MD1.2机械外形图创建请求参数
 * @author:
 */
@Data
public class JdyMd1point2CreateRequest extends JdyRequestV5<JdyRejectsHandlerFlowCreateResponse> {

    private String data_id;

    private String transaction_id;

    /**
     * 是否触发流程
     */
    private Boolean is_start_workflow;

    private JdyMd1point2CreateRequest.JdyData data;

    @Data
    public static class JdyData {

        /**
         * 项目号
         */
        private ValueString _widget_1617774553241;

        /**
         * 商机编号
         */
        private ValueString _widget_1617774553243;

        /**
         * 客户名称
         */
        private ValueString _widget_1617774553245;

        /**
         * 机械设计工程师
         */
        private ValueString _widget_1743472055916;

        /**
         * 系统外形图（PDF+DWG）
         */
        private ValueT<List<String>> _widget_1621492191516;

        /**
         * 系统物料号
         */
        private ValueString _widget_1621492191429;

    }

    /**
     * 获取返回对象类型
     */
    @Override
    public Class<JdyRejectsHandlerFlowCreateResponse> getResponseClass() {
        return JdyRejectsHandlerFlowCreateResponse.class;
    }
}

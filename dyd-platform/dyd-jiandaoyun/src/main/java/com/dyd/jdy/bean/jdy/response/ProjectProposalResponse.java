package com.dyd.jdy.bean.jdy.response;

import com.dyd.jdy.bean.JdyResponse;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * 【项目-需求技术方案】
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class ProjectProposalResponse extends JdyResponse {

    private List<ProjectProposal> data;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class ProjectProposal {

        private String _id;

        /**
         * 需求类型
         */
        private String _widget_1644473523859;
        /**
         * 预估方案难度
         */
        private String _widget_1642403224853;
        /**
         * 售后调试天数/单次单人_手动输入
         */
        private String _widget_1694585992745;
        /**
         * 售后调试人数
         */
        private String _widget_1689298508711;
        /**
         * 售后调试次数
         */
        private String _widget_1689298508712;
        /**
         * 项目需求编号
         */
        private String _widget_1642384433990;
        /**
         * 机械设计工时（天）_手动输入
         */
        private String _widget_1694509882426;
        /**
         * 技术支持工时_天_输入
         */
        private String _widget_1642468727773;
        /**
         * 技术支持调试工时（天）
         */
        private String _widget_1642468727852;
        /**
         * 电气设计工时（天）_手动输入
         */
        private String _widget_1694509882512;
        /**
         * 电气设计工时（天）
         */
        private String _widget_1642468727832;
        /**
         * 电气人员调试工时（天）
         */
        private String _widget_1644987998985;
        /**
         * 机械设计现场调试工时（天）
         */
        private String _widget_1690962098597;
        /**
         * 非标系统清单
         */
        private List<_widget_1642468726925> _widget_1642468726925;
        /**
         * 料清单_标准系统
         */
        private List<_widget_1642468726400> _widget_1642468726400;
        /**
         * 散件清单_标准系统
         */
        private List<_widget_1642468727195> _widget_1642468727195;
        /**
         * 期望交货期_周
         */
        private String _widget_1644905676568;
        /**
         * 商机编号
         */
        private String _widget_1642384433998;
        /**
         * 方案细化负责人
         */
        private List<Person> _widget_1642403226018;
        /**
         * 项目特殊说明
         */
        private String _widget_1649899512030;
        /**
         * 产品部特殊说明
         */
        private String _widget_1689175111306;
    }

    /**
     * 非标系统清单
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class _widget_1642468726925 {
        private String _id;
        /**
         * 功率kw(技术支持输入)
         */
        private String _widget_1693745793586;
        /**
         * 点火方式
         */
        private String _widget_1669182297496;
        /**
         * 系统物料号
         */
        private String _widget_1642468726956;
        /**
         * 图纸参考料号
         */
        private String _widget_1659064728605;
        /**
         * 核价单编号
         */
        private String _widget_1645407442909;
        /**
         * 型号
         */
        private String _widget_1691040490981;
        /**
         * 烧嘴型号
         */
        private String _widget_1694150341991;
        /**
         * 烧嘴型号
         */
        private String _widget_1642468727008;
        /**
         * 子型号（已弃用）
         */
        private String _widget_1644978863869;
        /**
         * 货物总成本
         */
        private String _widget_1653027623817;
        /**
         * 物料总成本
         */
        private String _widget_1644978864371;
        /**
         * 制作总费用
         */
        private String _widget_1644978864439;
        /**
         * 套数
         */
        private String _widget_1644978863961;
        /**
         * 物料成本.单套
         */
        private String _widget_1644978864098;
        /**
         * 制作费用.单套
         */
        private String _widget_1644978864126;
        /**
         * 呆滞成品
         */
        private String _widget_1652413099991;
        /**
         * 原项目号
         */
        private String _widget_1652413100046;
        /**
         * 消化数量
         */
        private String _widget_1652413100151;
        /**
         * 安装费用合计
         */
        private String _widget_1644978864156;
        /**
         * 测试费用
         */
        private String _widget_1689918785488;
        /**
         * 货物体积合计
         */
        private String _widget_1644978864271;
        /**
         * 物料交期（周）
         */
        private String _widget_1646276065221;
        /**
         * 图纸套数
         */
        private String _widget_1649388024115;
        /**
         * 判断_是否无系统物料号
         */
        private String _widget_1655688076139;
        /**
         * 备注选型
         */
        private String _widget_1668137197899;
    }

    /**
     * 料清单_标准系统
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class _widget_1642468726400 {
        /**
         * 货物总成本
         */
        private String _widget_1653027624403;
        /**
         * 物料成本合计
         */
        private String _widget_1644975660504;
        /**
         * 制作费用合计
         */
        private String _widget_1644975660606;
        /**
         * 货物体积合计
         */
        private String _widget_1644975660765;
        /**
         * 交期（周）
         */
        private String _widget_1644975660406;
        /**
         * 系统套数
         */
        private String _widget_1642468726616;
        /**
         * 安装费用合计
         */
        private String _widget_1644975660676;
    }

    /**
     * 散件清单_标准系统
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class _widget_1642468727195 {
        /**
         * 货物成本合计
         */
        private String _widget_1644976809286;
        /**
         * 制作费用合计
         */
        private String _widget_1644976809352;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class Person {
        private String name;
        private String username;
    }
}

package com.dyd.jdy.bean.jdy.request;

import com.dyd.jdy.bean.V5.JdyRequestV5;
import com.dyd.jdy.bean.common.ValueString;
import com.dyd.jdy.bean.jdy.response.LogisticsQuoteUpdateResponse;
import lombok.Data;

/**
 * @description 物流运输报价单表单数据更新请求参数
 * @author: yanlin
 * @date: 2023/7/31 14:12
 */
@Data
public class LogisticsQuoteUpdateRequest extends JdyRequestV5<LogisticsQuoteUpdateResponse> {

    /**
     * 数据id
     */
    private String data_id;

    private LogisticsQuoteUpdateData data;


    @Data
    public static class LogisticsQuoteUpdateData {
        /**
         * 流水号
         */
        private ValueString _widget_1690633748783;
    }

    /**
     * 获取返回对象类型
     */
    @Override
    public Class<LogisticsQuoteUpdateResponse> getResponseClass() {
        return LogisticsQuoteUpdateResponse.class;
    }
}

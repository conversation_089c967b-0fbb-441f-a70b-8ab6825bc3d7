package com.dyd.jdy.bean.plan;

import com.dyd.jdy.bean.JdyResponse;
import com.dyd.jdy.bean.common.Personnel;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class PlanYearGetDataResponse extends JdyResponse {
    /**
     * 表单字段
     */
    private List<FromData> data;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FromData {
        //部门
        private String _widget_1715760729395;
        //成员
        private Personnel _widget_1715760729387;
        //明细
        private List<Mingxi> _widget_1715760729388;
        //创建人
        private Personnel creator;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Mingxi {
        //计划项
        private String _widget_1715760729390;
        //计划描述
        private String _widget_1715760729391;
        //完成情况
        private Double _widget_1715760729392;
        //考核权重
        private String _widget_1715850558806;
        //年计划编号
        private String _widget_1724745519682;
    }
}

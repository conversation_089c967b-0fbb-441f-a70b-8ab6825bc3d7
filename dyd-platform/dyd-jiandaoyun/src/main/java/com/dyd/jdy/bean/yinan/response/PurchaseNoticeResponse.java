package com.dyd.jdy.bean.yinan.response;

import com.dyd.jdy.bean.JdyResponse;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * 【请购通知单_易楠】
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class PurchaseNoticeResponse extends JdyResponse {

    private PurchaseData data;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PurchaseData {
        private String _id;
        /**
         * 请购单号
         */
        private String _widget_1701138570316;
        /**
         * 销售订单号
         */
        private String _widget_1681550585718;
        /**
         * 销售员
         */
        private Person _widget_1681550585719;
        /**
         * 商机编号
         */
        private String _widget_1681550585721;
        /**
         * 客户名称
         */
        private String _widget_1681550585722;
        /**
         * 客户等级
         */
        private String _widget_1681550585723;
        /**
         * 币种
         */
        private String _widget_1681550585725;
        /**
         * 创建日期
         */
        private String _widget_1681465306894;
        /**
         * 采购人
         */
        private Person _widget_1681465306893;
        /**
         * 合同数量是否大于采购数量
         */
        private String _widget_1701745937346;
        /**
         * 货品清单
         */
        private List<_widget_1681550585726> _widget_1681550585726;
    }

    /**
     * 货品清单
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class _widget_1681550585726 {
        private String _id;
        /**
         * 采购状态
         */
        private String _widget_1701420881562;
        /**
         * Product Name
         */
        private String _widget_1681550585728;
        /**
         * 品名
         */
        private String _widget_1681550585729;
        /**
         * 料号
         */
        private String _widget_1681550585730;
        /**
         * 供应商名称
         */
        private String _widget_1701324400693;
        /**
         * 规格
         */
        private String _widget_1701137361737;
        /**
         * 描述
         */
        private String _widget_1701756973593;
        /**
         * 客户需求
         */
        private String _widget_1701137361738;
        /**
         * 品牌
         */
        private String _widget_1701756973600;
        /**
         * 数量_合同
         */
        private String _widget_1681550585734;
        /**
         * 请购数量
         */
        private String _widget_1681551243316;
        /**
         * 销售希望交期
         */
        private String _widget_1701420881547;
        /**
         * 付款条件
         */
        private String _widget_1701420881551;
        /**
         * 销售单价
         */
        private String _widget_1701420881558;
        /**
         * 供应商编号
         */
        private String _widget_1701324400692;
        /**
         * 采购数量校验
         */
        private String _widget_1701745937345;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Person {
        private String name;
        private String username;
    }
}

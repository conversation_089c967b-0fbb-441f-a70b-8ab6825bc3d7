package com.dyd.jdy.bean.ossFileMigrate;

import com.dyd.jdy.bean.JdyRequest;
import com.dyd.jdy.bean.common.ValueArray;
import com.dyd.jdy.bean.common.ValueString;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class EplanUpdateRequest extends JdyRequest<EplanUpdateResponse> {

    private String data_id;

    private FromData data;

    @Data
    @Builder
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FromData {
        /**
         * 文件预览URL
         */
        private ValueString _widget_1729672314494;

        /**
         * OSS文件相关
         */
        private ValueList _widget_1729672314495;

        /**
         * 删除标记
         */
        private ValueString _widget_1729948679553;

        /**
         * 附件
         */
        private ValueArray _widget_1685552395795;

    }

    @Data
    @Builder
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ValueList {
        private List<_widget_1729672314495> value;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class _widget_1729672314495 {
        /**
         * KEY
         */
        private ValueString _widget_1729672314497;
    }


    @Override
    public String getInterfaceName() {
        return "/data_update";
    }

    @Override
    public String getApiName() {
        return "/v4";
    }

    @Override
    public String getAppId() {
        return "/app/5fcdbd9db0390800077ca015";
    }

    @Override
    public String getEntryId() {
        return "/entry/650a3f7bd200fe3a4e05e6f0";
    }

    @Override
    public Class<EplanUpdateResponse> getResponseClass() {
        return EplanUpdateResponse.class;
    }
}

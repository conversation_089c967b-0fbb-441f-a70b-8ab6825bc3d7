package com.dyd.jdy.bean.performance;

import com.dyd.jdy.bean.JdyResponse;
import com.dyd.jdy.bean.common.Personnel;
import com.dyd.jdy.bean.parts.PartsDataResponse;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class performanceDataResponse extends JdyResponse {
    /**
     * 表单字段
     */
    private List<FromData> data;
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FromData {


        /**
         * 开发负责人
         */
        private List<Personnel> _widget_1690192328604;

        /**
         * IT完成时间
         */
        private  String _widget_1690192328605;

        /**
         * 绩效汇总
         */
        private  Integer  _widget_1690192328606;

        /**
         * 任务类型
         */
        private String _widget_1690277768660;

        /**
         * 及时性
         */
        private Integer _widget_1697424723793;

        /**
         * 服务态度
         */
        private Integer _widget_1697424723794;

        /**
         * 完成质量
         */
        private Integer _widget_1697424723795;

        /**
         * 专业能力
         */
        private Integer _widget_1697424723796;

        /**
         * Bug数
         */
        private Integer _widget_1697424723797;

        /**
         * 创建时间
         */
        private String createTime;

        private String user;
    }



}

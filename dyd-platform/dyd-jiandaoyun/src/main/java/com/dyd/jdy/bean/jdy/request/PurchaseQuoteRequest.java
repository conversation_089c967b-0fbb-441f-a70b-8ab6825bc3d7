package com.dyd.jdy.bean.jdy.request;

import com.dyd.jdy.bean.V5.JdyRequestV5;
import com.dyd.jdy.bean.jdy.response.PurchaseQuoteResponse;
import lombok.Data;

import java.util.List;

/**
 * 【2-2采购报价单】
 */
@Data
public class PurchaseQuoteRequest extends JdyRequestV5<PurchaseQuoteResponse> {

    private String data_id;

    //需要查询的数据字段
    private List<String> fields;

    private PurchaseQuoteRequest.Filter filter;

    //查询的数据条数，1~100，默认10
    private int limit;

    @Data
    public static class Field {

        //字段名
        private String field;

        //字段类型
        private String type;


        //过滤方法：
        //
        //“not_empty”(不为空)，
        //
        //“empty”(为空)，
        //
        //“eq”(等于)，
        //
        //“in”(等于任意一个)，最多可传递 200 个
        //
        //“range”(在x与y之间，并且包含x和y本身)，
        //
        //“nin”(不等于任意一个)，最多可传递 200 个
        //
        //“ne”(不等于),
        //
        //“like”(文本包含)
        //
        //“verified“(表示填写了手机号且已验证的值)
        //
        //“unverified“(表示填写了手机号但未验证值)
        private String method;

        //过滤值
        private List<Object> value;
    }

    @Data
    public static class Filter {
        //筛选组合关系；“and”(满足所有过滤条件), “or”(满足任一过滤条件)
        private String rel;

        private List<PurchaseQuoteRequest.Field> cond;
    }

    /**
     * 获取返回对象类型
     */
    @Override
    public Class<PurchaseQuoteResponse> getResponseClass() {
        return PurchaseQuoteResponse.class;
    }
}

package com.dyd.jdy.bean.dull;

import com.dyd.jdy.bean.JdyRequest;
import com.dyd.jdy.bean.collection.CollectionDataResponse;
import com.dyd.jdy.bean.common.Filter;
import lombok.Builder;
import lombok.Data;

/**
 * 呆滞品消化登记表
 * <AUTHOR>
 */
@Data
@Builder
public class DullProductDataRequest extends JdyRequest<DullProductDataResponse> {
    /**
    * 查询的数据条数，1~100，默认10
    */
    private Integer limit;

    /**
     * 帅选过滤
     */
    private Filter filter;


    @Override
    public String getInterfaceName() {
        return "/data";
    }

    @Override
    public String getApiName() {
        return "/v4";
    }

    @Override
    public String getAppId() {
        return "/app/5fcb133cfbd040000638780c";
    }

    @Override
    public String getEntryId() {
        return "/entry/5fdd71bed53bef000699f1ac";
    }

    @Override
    public Class<DullProductDataResponse> getResponseClass() {
        return DullProductDataResponse.class;
    }
}

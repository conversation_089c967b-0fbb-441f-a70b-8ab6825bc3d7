package com.dyd.jdy.bean.afterSalesMember.files;

import com.dyd.jdy.bean.JdyRequest;
import com.dyd.jdy.bean.common.ValueString;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Builder;
import lombok.Data;

/**
 * 01-4 售后服务工程师档案
 *
 * <AUTHOR>
 */
@Data
@Builder
public class AfterSalesMemberFilesUpdateRequest extends JdyRequest<AfterSalesMemberFilesUpdateResponse> {

    private String data_id;

    private FromDemo data;

    @Data
    @Builder
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FromDemo {
        /**
         * 姓名
         */
        private ValueString personnel_name;

        /**
         * 人员编号
         */
        private ValueString personnel_no;

        /**
         * 入职时间
         */
        private ValueString hiredate;

        /**
         * 在职时间（年限）
         */
        private ValueString age_limit;

        /**
         * 家庭地址
         */
        private ValueString home_address;

        /**
         * 目前状态
         */
        private ValueString on_job_status;

        /**
         * 技术能力评价
         */
        private ValueString technology_evaluate;

        /**
         * 基本评价
         */
        private ValueString basic_evaluate;

        /**
         * 离职时间
         */
        private ValueString leavedate;

        /**
         * 行业方向
         */
        private ValueString industry;

        /**
         * 人员等级
         */
        private ValueString grade;

        /**
         * 派工状态
         */
        private ValueString work_status;

        /**
         * 所属省份
         */
        private ValueString province;

        /**
         * 所属大区
         */
        private ValueString region;

        /**
         * 所在详细地址
         */
        private ValueString detailed_address;

        /**
         * 所在地经度
         */
        private ValueString longitude;

        /**
         * 所在地纬度
         */
        private ValueString latitude;

        /**
         * 预计结束时间
         */
        private ValueString end_date;

        /**
         * 是否预选
         */
        private ValueString preselection_mark;

        /**
         * 联系电话
         */
        private ValueString phone;

        /**
         * 职位
         */
        private ValueString position;
    }

    @Override
    public String getInterfaceName() {
        return "/data_update";
    }

    @Override
    public String getApiName() {
        return "/v4";
    }

    @Override
    public String getAppId() {
        return "/app/600549610e809f00070f2c48";
    }

    @Override
    public String getEntryId() {
        return "/entry/63ddc0ed0b91e8000991e755";
    }

    @Override
    public Class<AfterSalesMemberFilesUpdateResponse> getResponseClass() {
        return AfterSalesMemberFilesUpdateResponse.class;
    }
}

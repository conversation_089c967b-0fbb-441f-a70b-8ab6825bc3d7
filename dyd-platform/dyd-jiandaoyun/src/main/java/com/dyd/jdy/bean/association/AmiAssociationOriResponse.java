package com.dyd.jdy.bean.association;


import com.dyd.jdy.bean.JdyResponse;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * 配置数据信息
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AmiAssociationOriResponse extends JdyResponse {
    private List<FromData> data;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FromData {
        //主表应用
        String _widget_1669966728395;
        //主表
        String _widget_1670204614758;
        //子表应用
        String _widget_1669966728398;
        // 子表
        String _widget_1670204614755;
        // 子表配置的关联字段
        String _widget_1670223064082;
        // 主表配置的关联字段
        String _widget_1672907967635;
        // 是否需要发送通知
        String _widget_1673596424334;
        // 通知业务编码
        String _widget_1673596424336;
        List<AssociationFiled> _widget_1669966728402;
        String _id;
        String appId;
        String entryId;
    }
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AssociationFiled {
        String _id;
        //主表字段
        String _widget_1669966728404;
        //子表字段
        String _widget_1669966728405;
    }
}

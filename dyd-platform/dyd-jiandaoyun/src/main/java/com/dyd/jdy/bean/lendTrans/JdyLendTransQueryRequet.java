package com.dyd.jdy.bean.lendTrans;

import com.dyd.jdy.bean.V5.JdyRequestV5;
import com.dyd.jdy.bean.jdy.JdyCommonDto;
import com.dyd.jdy.bean.jdy.response.JdyProjectManegerResponse;
import lombok.Data;

import java.util.List;

/**
 * 借出
 */
@Data
public class JdyLendTransQueryRequet extends JdyRequestV5<JdyLendTransQueryResponse> {


    //上一次查询数据结果的最后一条数据的ID，没有则留空
    private String data_id;

    //需要查询的数据字段
    private List<String> fields;

    //查询的数据条数，1~100，默认10
    private int limit;

    //数据筛选器
    private JdyCommonDto.Filter filter;





    @Override
    public Class<JdyLendTransQueryResponse> getResponseClass() {
        return JdyLendTransQueryResponse.class;
    }
}

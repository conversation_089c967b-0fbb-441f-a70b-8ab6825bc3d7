package com.dyd.jdy.bean.jdy.response;

import com.dyd.jdy.bean.JdyResponse;
import com.dyd.jdy.bean.jdy.JdyCommonDto;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * DQ01-电气设计工作表单返回
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class JdyElectricalDesignResponse extends JdyResponse {

    private List<JdyElectricalDesignData> data;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class JdyElectricalDesignData{

        private String _id;

        /**
         * 项目号
         */
        private String _widget_1607412530740;

        /**
         * 电气BOM计划完成时间
         */
        private String _widget_1679557149300;

        /**
         * 电气BOM实际完成时间
         */
        private String _widget_1679557149301;

        /**
         * 电气确认图计划完成时间
         */
        private String _widget_1675912204061;

        /**
         * 电气确认图实际完成时间
         */
        private String _widget_1615110103253;

        /**
         * 电气图客户实际确认时间
         */
        private String _widget_1615272755768;

        /**
         * 电气生产图实际完成时间
         */
        private String _widget_1615110103332;

        /**
         * 生产图或程序
         */
        private List<ProductionDiagramDesc> _widget_1610953283478;

        /**
         * DQ01-电气设计工作--生产图计划完成时间
         */
        private String _widget_1675912204060;

        /**
         * 电气设计负责人
         */
        private List<JdyCommonDto.User> _widget_1682075379764;

        /**
         * 项目管理
         */
        private JdyCommonDto.User _widget_1614136213564;

    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ProductionDiagramDesc{

        /**
         * 文件名
         */
        private String name;

        /**
         * 文件大小
         */
        private Integer size;

        /**
         * 文件类型
         */
        private String mime;
        /**
         * 路径
         */
        private String url;

    }
}

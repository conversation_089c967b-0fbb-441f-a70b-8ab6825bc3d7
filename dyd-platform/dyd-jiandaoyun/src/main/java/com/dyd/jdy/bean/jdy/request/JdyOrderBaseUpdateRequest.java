package com.dyd.jdy.bean.jdy.request;

import com.dyd.jdy.bean.V5.JdyRequestV5;
import com.dyd.jdy.bean.common.ValueList;
import com.dyd.jdy.bean.common.ValueString;
import com.dyd.jdy.bean.jdy.response.JdyOrderBaseUpdateResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.dyd.jdy.bean.common.ValueT;
import com.dyd.jdy.bean.jdy.response.JdyFinanceOrderCollectionPlanResponse;
import com.dyd.jdy.bean.jdy.response.JdyOrderBaseUpdateResponse;
import lombok.Data;

import java.util.List;

/**
 * 应收款信息数据表请求参数
 */
@EqualsAndHashCode(callSuper=false)
@Data
public class JdyOrderBaseUpdateRequest extends JdyRequestV5<JdyOrderBaseUpdateResponse> {

    private String transaction_id;

    private FinanceOrderCollectionPlanData data;

    private String data_id;

    @Data
    public static class FinanceOrderCollectionPlanData {

        /**
         * U9订单编号
         */
        private ValueString _widget_1620719335689;

        /**
         * 商机编号
         */
        private ValueString _widget_1620719335638;

        /**
         * 项目编号
         */
        private ValueString _widget_1620719335965;

        /**
         * 订单分类
         */
        private ValueString _widget_1630979828223;

        /**
         * 商机名称
         */
        private ValueString _widget_1668585494361;

        /**
         * 商机描述
         */
        private ValueString _widget_1668585494362;

        /**
         * 业务员
         */
        private ValueString _widget_1620719337801;

        /**
         * 名称
         */
        private ValueString _widget_1728891058237;

        /**
         * 业绩人员
         */
        private ValueList _widget_1620869048573;

        /**
         * 成单日期
         */
        private ValueString _widget_1620719337744;

        /**
         * 最后发货日期
         */
        private ValueString _widget_1620722037392;

        /**
         * 客户编号
         */
        private ValueString _widget_1620719337868;

        /**
         * U9客户编号
         */
        private ValueString _widget_1620886517117;

        /**
         * 客户名称
         */
        private ValueString _widget_1620719337851;

        /**
         * 事业部
         */
        private ValueString _widget_1620719339466;

        /**
         * 订单来源
         */
        private ValueString _widget_1620719335656;

        /**
         * 客户性质
         */
        private ValueString _widget_1629342966409;

        /**
         * 客户行业
         */
        private ValueString _widget_1620719339679;

        /**
         * 客户应用
         */
        private ValueString _widget_1620719339697;

        /**
         * 品牌
         */
        private ValueString _widget_1620719339913;

        /**
         * 产品
         */
        private ValueString _widget_1620719339931;

        /**
         * 订单类型
         */
        private ValueString _widget_1620886517365;

        /**
         * 合同条款
         */
        private ValueString _widget_1656897927232;

        /**
         * 合同额
         */
        private ValueString _widget_1620719337635;

        /**
         * 开票额
         */
        private ValueString _widget_1620719340995;

        /**
         * 回款额
         */
        private ValueString _widget_1620719338455;

        /**
         * 未回款额
         */
        private ValueString _widget_1620719338601;

        /**
         *回款占比
         */
        private ValueString _widget_1669628863333;

        /**
         * 合同毛利_实际
         */
        private ValueString _widget_1620958131651;

        /**
         * 合同毛利率_实际
         */
        private ValueString _widget_1620958131689;

        /**
         * 发货比例
         */
        private ValueString _widget_1620719338711;

        /**
         * 已发货额
         */
        private ValueString _widget_1620797961674;

        /**
         * 订货数量
         */
        private ValueString _widget_1620888030944;

        /**
         * 出货数量
         */
        private ValueString _widget_1620888066589;

        /**
         * 现场施工费_预算
         */
        private ValueString _widget_1620719336923;

        /**
         * 现场施工费_实际
         */
        private ValueString _widget_1620719336961;

        /**
         * 未领取
         */
        private ValueString _widget_1620886516328;

    }


    @Override
    public Class<JdyOrderBaseUpdateResponse> getResponseClass() {
        return JdyOrderBaseUpdateResponse.class;
    }
}

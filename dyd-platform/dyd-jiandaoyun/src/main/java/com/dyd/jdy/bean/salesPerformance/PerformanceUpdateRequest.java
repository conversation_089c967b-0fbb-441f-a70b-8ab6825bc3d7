package com.dyd.jdy.bean.salesPerformance;

import com.dyd.jdy.bean.JdyRequest;
import com.dyd.jdy.bean.common.ValueNumber;
import com.dyd.jdy.bean.common.ValueString;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Builder;
import lombok.Data;

/**
 * 绩效指标
 * <AUTHOR>
 */
@Data
@Builder
public class PerformanceUpdateRequest extends JdyRequest<PerformanceUpdateResponse> {

    private String data_id;

    private FromDemo data;

    @Data
    @Builder
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FromDemo {
        /**
         * 维度-统计
         */
        private ValueString _widget_1691394875780;
        /**
         * 维度-时间
         */
        private ValueString _widget_1691394875791;
        /**
         * 维度-唯一
         */
        private ValueString _widget_1691473168264;
        /**
         * BU名称
         */
        private ValueString _widget_1691394875782;
        /**
         * 事业部
         */
        private ValueString _widget_1691474631892;
        /**
         * 事业部短名
         */
        private ValueString _widget_1691474631901;
        /**
         * 年度
         */
        private ValueNumber _widget_1691394875784;
        /**
         * 月度
         */
        private ValueString _widget_1691394875783;
        /**
         * 业绩指标
         */
        private ValueNumber _widget_1691394875787;
        /**
         * 当前业绩
         */
        private ValueNumber _widget_1691394875788;
        /**
         * 完成度
         */
        private ValueNumber _widget_1691394875789;
        /**
         * 时间进度
         */
        private ValueNumber _widget_1691650717235;
        /**
         * 最后更新时间
         */
        private ValueString _widget_1691473168281;
    }

    @Override
    public String getInterfaceName() {
        return "/data_update";
    }

    @Override
    public String getApiName() {
        return "/v4";
    }

    @Override
    public String getAppId() {
        return "/app/64ccab5498bb350008c259be";
    }

    @Override
    public String getEntryId() {
        return "/entry/64d0a33c5dbdf800078309eb";
    }

    @Override
    public Class<PerformanceUpdateResponse> getResponseClass() {
        return PerformanceUpdateResponse.class;
    }
}

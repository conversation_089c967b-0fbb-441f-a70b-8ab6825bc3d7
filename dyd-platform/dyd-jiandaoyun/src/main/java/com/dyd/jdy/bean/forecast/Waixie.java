package com.dyd.jdy.bean.forecast;

import com.dyd.jdy.bean.common.ValueNumber;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 备件需求单料品清单
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class Waixie {

    /**
     * 料号
     */
    private  String _id;
    /**
     * 外协加工成本.预测（自动）
     */
   private  String _widget_1614059284882;

   private  String  _widget_1614059284881;

   private  String _widget_1614059284883;
   private  String _widget_1614059284884;
   private  String _widget_1628647121072;

}

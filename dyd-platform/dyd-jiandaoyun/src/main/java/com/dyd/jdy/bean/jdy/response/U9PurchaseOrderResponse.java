package com.dyd.jdy.bean.jdy.response;

import com.dyd.jdy.bean.JdyResponse;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * 00-3-U9采购订单数据（来源U9)
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class U9PurchaseOrderResponse extends JdyResponse {

    private List<U9PurchaseOrderData> data;

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    public static class U9PurchaseOrderData {

        private String _id;
        /**
         * 提交时间
         */
        private String createTime;
        /**
         * 料号
         */
        private String _widget_1620357371301;
        /**
         * 料品名称
         */
        private String _widget_1620357371300;
        /**
         * 最终价(原币)
         */
        private String _widget_1620357371309;

        /**
         * 单据编号
         */
        private String _widget_1620357371297;

        /**
         * 库存数量
         */
        private Integer _widget_1637565831954;

        /**
         * 待收货数量
         */
        private Integer _widget_1620357371307;

        /**
         * 供应商确认数量
         */
        private Integer _widget_1620357371305;

        /**
         * 采购数量
         */
        private Integer _widget_1620357371304;

        /**
         * 要求交货日期
         */
        private String _widget_1620357371308;

        /**
         * 状态
         */
        private String _widget_1620357371295;
    }
}

package com.dyd.jdy.bean.forecast;

import com.dyd.jdy.bean.JdyRequest;
import com.dyd.jdy.bean.common.Filter;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Builder;
import lombok.Data;

/**
 * @description TODO
 * @author: tj
 * @date: 2023/2/14 14:57
 */
@Data
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class AfterSaleDataRequest extends JdyRequest<AfterSaleDataResponse> {

    private String data_id;
    private Filter filter;
    private Integer limit;

    @Override
    public String getInterfaceName() {
        return "/data";
    }

    @Override
    public String getApiName() {
        return "/v4";
    }

    @Override
    public String getAppId() {
        return "/app/600549610e809f00070f2c48";
    }

    @Override
    public String getEntryId() {
        return "/entry/60062aed7514bd000637a90e";
    }

    @Override
    public Class<AfterSaleDataResponse> getResponseClass() {
        return AfterSaleDataResponse.class;
    }
}

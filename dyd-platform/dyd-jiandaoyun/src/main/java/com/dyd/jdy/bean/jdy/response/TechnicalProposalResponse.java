package com.dyd.jdy.bean.jdy.response;

import com.dyd.jdy.bean.JdyResponse;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * 【技术支持方案库】
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class TechnicalProposalResponse extends JdyResponse {

    private List<TechnicalProposalData> data;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TechnicalProposalData {

        private String _id;

        /**
         * 方案编号
         */
        private String _widget_1615519282600;
        /**
         * 商机编号
         */
        private String _widget_1622082554480;
        /**
         * 用料清单
         */
        private List<_widget_1615532513677> _widget_1615532513677;

    }

    /**
     * 用料清单
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class _widget_1615532513677 {
        private String _id;
        /**
         * 序号
         */
        private String _widget_1623133325904;
        /**
         * 定制描述
         */
        private String _widget_1615532513711;
        /**
         * 定制单价
         */
        private String _widget_1615532513755;
        /**
         * 定制合计
         */
        private String _widget_1615532513858;
        /**
         * 料号
         */
        private String _widget_1615533300955;
        /**
         * 名称
         */
        private String _widget_1615533301006;
        /**
         * 品牌
         */
        private String _widget_1615533301046;
        /**
         * 型号
         */
        private String _widget_1615533301195;
        /**
         * 描述
         */
        private String _widget_1615533301268;
        /**
         * 单价
         */
        private String _widget_1615533301347;
        /**
         * 隐式单价
         */
        private String _widget_1692924283621;
        /**
         * 过期单价
         */
        private String _widget_1692669009704;
        /**
         * 参考成本更新时间
         */
        private String _widget_1692669009705;
        /**
         * 参考成本有效期
         */
        private String _widget_1692669009706;
        /**
         * 总用量
         */
        private String _widget_1615533301634;
        /**
         * 物料合计
         */
        private String _widget_1615537485422;
        /**
         * 制作费用
         */
        private String _widget_1615538474510;
        /**
         * 管路制作费辅助
         */
        private String _widget_1622015791683;
        /**
         * 烧嘴制作费辅助
         */
        private String _widget_1622015792093;
        /**
         * 电控制作费辅助
         */
        private String _widget_1622015792412;
        /**
         * 非标制作费辅助
         */
        private String _widget_1622015792742;
        /**
         * 炉子制作费辅助
         */
        private String _widget_1622600547421;
        /**
         * 制作费用合计
         */
        private String _widget_1615538474732;
        /**
         * 货期天
         */
        private String _widget_1618302092339;
        /**
         * 非安全库存的货期
         */
        private String _widget_1620809395056;
        /**
         * 辅助料号
         */
        private String _widget_1639559058178;
        /**
         * 安全库存
         */
        private String _widget_1618555304817;
        /**
         * 数量
         */
        private String _widget_1615533301462;
        /**
         * 产品数量
         */
        private String _widget_1615533301547;
        /**
         * 辅助判断价格1
         */
        private String _widget_1622621539507;
        /**
         * 辅助判断价格2
         */
        private String _widget_1622621558058;
        /**
         * 辅助判断价格3
         */
        private String _widget_1622621558395;
        /**
         * 可替换呆滞型号
         */
        private String _widget_1618558444799;
        /**
         * 呆滞数量
         */
        private String _widget_1618558445082;
        /**
         * 散发
         */
        private String _widget_1661222088433;
        /**
         * 可用库存
         */
        private String _widget_1615537486502;
        /**
         * 现货
         */
        private String _widget_1661412869565;
        /**
         * 货期
         */
        private String _widget_1661412870493;
        /**
         * 参考成本价
         */
        private String _widget_1691465063483;
        /**
         * 成本价失效日期
         */
        private String _widget_1691465063484;
    }
}

package com.dyd.jdy.bean.projectToU9;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * 散件清单_1
 * <AUTHOR>
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class SpareParts {

    /**
     * 散件清单_1.物料号
     */
    private  String _widget_1650782217262;

    /**
     * 散件清单_1.规格
     */
    private String _widget_1650782217264;

    /**
     * 散件清单_1.数量
     */
    private Integer _widget_1650782217265;

    /**
     * 散件清单_1.备注
     */
    private  String  _widget_1650782217269;

//    /**
//     * 表单字段
//     */
//    private FromData data;
//
//    @Data
//    @JsonIgnoreProperties(ignoreUnknown = true)
//    public static class FromData{
//
//        /**
//         * 散件清单_1.物料号
//         */
//        private  String _widget_1650782217262;
//
//        /**
//         * 散件清单_1.规格
//         */
//        private String _widget_1650782217264;
//
//        /**
//         * 散件清单_1.数量
//         */
//        private Integer _widget_1650782217265;
//
//        /**
//         * 散件清单_1.备注
//         */
//        private  String  _widget_1650782217269;
//
//    }

}

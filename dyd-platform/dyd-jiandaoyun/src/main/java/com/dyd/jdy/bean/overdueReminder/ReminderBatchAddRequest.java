package com.dyd.jdy.bean.overdueReminder;

import com.dyd.jdy.bean.JdyRequest;
import com.dyd.jdy.bean.common.ValueArray;
import com.dyd.jdy.bean.common.ValueString;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 项目管理超期提醒 新增多条数据
 * <AUTHOR>
 */
@Data
@Builder
public class ReminderBatchAddRequest extends JdyRequest<ReminderBatchAddResponse> {

    private List<DataList> data_list;

    @Data
    @Builder
    public static class DataList {
        /**
         * 消息类别
         */
        private ValueString _widget_1671460716380;
        /**
         * 消息类型
         */
        private ValueString _widget_1671557627898;
        /**
         * 项目编号
         */
        private ValueString _widget_1671460716375;
        /**
         * 客户编号
         */
        private ValueString _widget_1671460716376;
        /**
         * 商机编号
         */
        private ValueString _widget_1671460716377;
        /**
         * 所属销售员
         */
        private ValueString _widget_1671460716378;
        /**
         * 提醒日期
         */
        private ValueString _widget_1671460716384;
        /**
         * 需提醒人
         */
        private ValueArray _widget_1672900062650;
        /**
         * 承诺完成日期
         */
        private ValueString _widget_1671460716379;
        /**
         * 消息内容
         */
        private ValueString _widget_1671460716382;
    }

    @Override
    public String getInterfaceName() {
        return "/data_batch_create";
    }

    @Override
    public String getApiName() {
        return "/v1";
    }

    @Override
    public String getAppId() {
        return "/app/5fd066e4684fdb0006631413";
    }

    @Override
    public String getEntryId() {
        return "/entry/63a0776ce72a28000abf126b";
    }

    @Override
    public Class<ReminderBatchAddResponse> getResponseClass() {
        return ReminderBatchAddResponse.class;
    }
}

package com.dyd.jdy.bean.mail;

import com.dyd.jdy.bean.JdyResponse;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * 销售员_基础JOY 查询多条数据
 *
 * <AUTHOR>
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class JdyMailTempDataResponse extends JdyResponse {
    /**
     * 表单字段333
     */
    private List<FromData> data;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FromData {
        /**
         * 唯一ID
         */
        private String _id;

        private String temp_name;

        private List<TempFiles> temp_files;

    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TempFiles {
        /**
         * 唯一ID
         */
        private String name;


        private Integer size;

        private String mime;

        private String url;

    }

}
